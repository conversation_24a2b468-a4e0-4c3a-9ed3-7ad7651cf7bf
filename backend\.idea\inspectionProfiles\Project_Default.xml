<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyArgumentListInspection" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="WARNING_ATTRIBUTES" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="59">
            <item index="0" class="java.lang.String" itemvalue="pytest" />
            <item index="1" class="java.lang.String" itemvalue="numpy" />
            <item index="2" class="java.lang.String" itemvalue="kwsbp" />
            <item index="3" class="java.lang.String" itemvalue="tensorboard" />
            <item index="4" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="5" class="java.lang.String" itemvalue="matplotlib" />
            <item index="6" class="java.lang.String" itemvalue="openpyxl" />
            <item index="7" class="java.lang.String" itemvalue="absl-py" />
            <item index="8" class="java.lang.String" itemvalue="modelscope" />
            <item index="9" class="java.lang.String" itemvalue="async-timeout" />
            <item index="10" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="11" class="java.lang.String" itemvalue="yapf" />
            <item index="12" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="13" class="java.lang.String" itemvalue="datasets" />
            <item index="14" class="java.lang.String" itemvalue="gast" />
            <item index="15" class="java.lang.String" itemvalue="torchvision" />
            <item index="16" class="java.lang.String" itemvalue="frozenlist" />
            <item index="17" class="java.lang.String" itemvalue="fsspec" />
            <item index="18" class="java.lang.String" itemvalue="mkl-fft" />
            <item index="19" class="java.lang.String" itemvalue="funasr" />
            <item index="20" class="java.lang.String" itemvalue="safetensors" />
            <item index="21" class="java.lang.String" itemvalue="multiprocess" />
            <item index="22" class="java.lang.String" itemvalue="pyreadline3" />
            <item index="23" class="java.lang.String" itemvalue="tomli" />
            <item index="24" class="java.lang.String" itemvalue="Markdown" />
            <item index="25" class="java.lang.String" itemvalue="torchaudio" />
            <item index="26" class="java.lang.String" itemvalue="xxhash" />
            <item index="27" class="java.lang.String" itemvalue="pyarrow" />
            <item index="28" class="java.lang.String" itemvalue="tokenizers" />
            <item index="29" class="java.lang.String" itemvalue="transformers" />
            <item index="30" class="java.lang.String" itemvalue="hdbscan" />
            <item index="31" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="32" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="33" class="java.lang.String" itemvalue="tzdata" />
            <item index="34" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="35" class="java.lang.String" itemvalue="dill" />
            <item index="36" class="java.lang.String" itemvalue="torch" />
            <item index="37" class="java.lang.String" itemvalue="humanfriendly" />
            <item index="38" class="java.lang.String" itemvalue="addict" />
            <item index="39" class="java.lang.String" itemvalue="attrs" />
            <item index="40" class="java.lang.String" itemvalue="simplejson" />
            <item index="41" class="java.lang.String" itemvalue="sortedcontainers" />
            <item index="42" class="java.lang.String" itemvalue="pandas" />
            <item index="43" class="java.lang.String" itemvalue="umap" />
            <item index="44" class="java.lang.String" itemvalue="regex" />
            <item index="45" class="java.lang.String" itemvalue="propcache" />
            <item index="46" class="java.lang.String" itemvalue="aiohttp" />
            <item index="47" class="java.lang.String" itemvalue="multidict" />
            <item index="48" class="java.lang.String" itemvalue="grpcio" />
            <item index="49" class="java.lang.String" itemvalue="yarl" />
            <item index="50" class="java.lang.String" itemvalue="pytz" />
            <item index="51" class="java.lang.String" itemvalue="einops" />
            <item index="52" class="java.lang.String" itemvalue="aiosignal" />
            <item index="53" class="java.lang.String" itemvalue="mkl-service" />
            <item index="54" class="java.lang.String" itemvalue="sqlmodel" />
            <item index="55" class="java.lang.String" itemvalue="protobuf" />
            <item index="56" class="java.lang.String" itemvalue="routes" />
            <item index="57" class="java.lang.String" itemvalue="flask-sqlalchemy" />
            <item index="58" class="java.lang.String" itemvalue="gunicorn" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="SqlDialectInspection" enabled="false" level="WARNING" enabled_by_default="false" />
  </profile>
</component>