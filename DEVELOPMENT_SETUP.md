# 开发环境设置指南

## 前置要求

### 系统要求
- Node.js 18+ 
- Python 3.11+
- PostgreSQL 16 (端口 5433)
- Redis 6+

### 数据库设置

#### PostgreSQL 设置
```bash
# 创建数据库
createdb -p 5433 mxd_info_db

# 或者使用 psql
psql -p 5433 -U postgres
CREATE DATABASE mxd_info_db;
```

#### Redis 设置
确保 Redis 服务运行在默认端口 6379

## 快速开始

### 1. 安装依赖
```bash
# 安装根目录依赖 (concurrently)
npm install

# 安装前端依赖
npm run install:frontend

# 安装后端依赖
npm run install:backend
```

### 2. 配置环境变量
```bash
# 后端环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件，配置数据库连接等

# 前端环境变量
cp frontend/.env.example frontend/.env.local
# 编辑 frontend/.env.local 文件，配置 API 地址等
```

### 3. 初始化数据库
```bash
cd backend
python -m alembic upgrade head
```

### 4. 启动开发服务器
```bash
# 同时启动前后端 (推荐)
npm run dev

# 或者分别启动
npm run dev:frontend  # 前端: http://localhost:3000
npm run dev:backend   # 后端: http://localhost:8000
```

## 开发服务器地址

- **前端**: http://localhost:3000
- **后端 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/api/v1/docs
- **健康检查**: http://localhost:8000/health

## 项目结构

```
maplestory-info-station/
├── frontend/          # Next.js 前端
│   ├── app/          # Next.js App Router
│   ├── components/   # React 组件
│   ├── lib/          # 工具函数
│   ├── types/        # TypeScript 类型
│   └── public/       # 静态资源
├── backend/          # FastAPI 后端
│   ├── app/          # 应用代码
│   │   ├── api/      # API 路由
│   │   ├── core/     # 核心配置
│   │   ├── models/   # 数据库模型
│   │   ├── services/ # 业务逻辑
│   │   └── utils/    # 工具函数
│   ├── alembic/      # 数据库迁移
│   └── tests/        # 测试文件
└── docs/             # 文档
```

## 常用命令

```bash
# 开发
npm run dev                 # 启动前后端开发服务器
npm run dev:frontend        # 仅启动前端
npm run dev:backend         # 仅启动后端

# 构建
npm run build              # 构建前端
npm run build:frontend     # 构建前端

# 测试
npm run test               # 运行所有测试
npm run test:frontend      # 运行前端测试
npm run test:backend       # 运行后端测试

# 代码检查
npm run lint               # 检查前端代码
npm run lint:frontend      # 检查前端代码
```

## 数据库操作

```bash
cd backend

# 创建新的迁移
python -m alembic revision --autogenerate -m "描述"

# 应用迁移
python -m alembic upgrade head

# 回滚迁移
python -m alembic downgrade -1
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 PostgreSQL 是否运行在端口 5433
   - 检查数据库名称和凭据是否正确

2. **Redis 连接失败**
   - 检查 Redis 是否运行在端口 6379
   - 检查 Redis 配置

3. **前端无法连接后端**
   - 检查后端是否运行在端口 8000
   - 检查 CORS 配置
   - 检查前端环境变量中的 API 地址

4. **依赖安装失败**
   - 检查 Node.js 和 Python 版本
   - 清除缓存后重新安装

### 日志查看

- 前端日志：浏览器控制台
- 后端日志：终端输出
- 数据库日志：PostgreSQL 日志文件

## 下一步

完成基础架构搭建后，可以开始实施：
1. 用户认证系统
2. 会员权限系统  
3. 虚拟货币系统
4. 现有功能集成

参考 `FRONTEND_BACKEND_SEPARATION_PLAN.md` 了解完整的实施计划。
