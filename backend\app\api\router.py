"""
Main API router
"""

from fastapi import APIRouter
from app.api.v1 import auth, users, currency, enhancement

api_router = APIRouter()

# Include v1 API routes
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(currency.router, prefix="/currency", tags=["currency"])
api_router.include_router(enhancement.router, prefix="/enhancement", tags=["enhancement"])
