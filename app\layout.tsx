import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"
import { Header } from "@/components/header"

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins"
})

export const metadata: Metadata = {
  title: "冒险岛情报站 - MapleStory Information Hub",
  description: "专业的冒险岛游戏数据库，提供装备、怪物、技能、地图等全面信息",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={poppins.className}>
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
          <Header />
          <div className="max-w-7xl mx-auto px-4 py-6">{children}</div>
        </div>
      </body>
    </html>
  )
}
