"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Users } from "lucide-react"

export function Sidebar() {
  const announcements = [
    {
      title: "纸娃娃更新",
      date: "2025-06-07",
      content: "我在支持动态效果了，在新你在上身点击道具可以看到动态效果。",
      type: "update",
    },
    {
      title: "纸娃娃更新",
      date: "2025-06-04",
      content: "修复了部分道具显示不正常的问题，例如：【红草莓帽】",
      type: "fix",
    },
    {
      title: "技能数据库更新",
      date: "2025-06-03",
      content: "修正了VSE的技能数据外观高等级+50数据。",
      type: "update",
    },
  ]

  const externalLinks = [
    { name: "官服官网[金鹰]", url: "#", type: "官方" },
    { name: "台服官网[游戏橘子]", url: "#", type: "官方" },
    { name: "韩服官网[Nexon]", url: "#", type: "官方" },
    { name: "日服官网[Nexon]", url: "#", type: "官方" },
    { name: "官服马戏团", url: "#", type: "工具" },
    { name: "官服马戏团Wiki", url: "#", type: "工具" },
    { name: "NGA-官服马戏团区", url: "#", type: "工具" },
    { name: "官服马英文Wiki [Fandom]", url: "#", type: "工具" },
  ]

  return (
    <aside className="w-80 space-y-6">
      {/* Site Info Card */}
      <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
        <CardHeader className="pb-3">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">冒</span>
            </div>
            <div>
              <CardTitle className="text-lg">冒险岛小帽子</CardTitle>
              <p className="text-sm text-gray-600">小帽子：499151029</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <p className="text-sm text-gray-700">
            冒险岛小帽子是一个综合性的冒险岛数据库网站，收录、工具类资料站。如果您喜欢我的网站，请记住我的网址：Mxd.dvg.cn
          </p>
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">合作人员</span>
          </div>
          <div className="flex justify-center">
            <div className="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500">QR Code</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Announcements */}
      <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center space-x-2">
            <span className="text-red-500">📢</span>
            <span>更新日志</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {announcements.map((announcement, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">{announcement.title}</h4>
                <Badge variant="outline" className="text-xs">
                  {announcement.date}
                </Badge>
              </div>
              <p className="text-xs text-gray-600 leading-relaxed">{announcement.content}</p>
              {index < announcements.length - 1 && <hr className="border-gray-200" />}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* External Links */}
      <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center space-x-2">
            <span className="text-blue-500">🔗</span>
            <span>实用站点</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {externalLinks.map((link, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-sm text-gray-700">{link.name}</span>
              <div className="flex items-center space-x-1">
                <Badge variant="secondary" className="text-xs">
                  {link.type}
                </Badge>
                <ExternalLink className="w-3 h-3 text-gray-400" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </aside>
  )
}
