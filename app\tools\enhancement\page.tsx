import { Metadata } from 'next'
import EnhancementSimulator from '@/components/enhancement-simulator/EnhancementSimulator'

export const metadata: Metadata = {
  title: '装备强化模拟器 - 冒险岛情报站',
  description: '专业的冒险岛装备强化模拟器，支持星之力、潜能、额外属性强化，真实模拟游戏强化过程',
  keywords: ['冒险岛', '装备强化', '星之力', '潜能', '额外属性', '模拟器'],
}

export default function EnhancementPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            装备强化模拟器
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            体验真实的冒险岛装备强化过程，支持星之力、潜能、额外属性三种强化类型
          </p>
        </div>

        {/* 模拟器主体 */}
        <div className="flex justify-center">
          <EnhancementSimulator />
        </div>

        {/* 使用说明 */}
        <div className="mt-12 max-w-4xl mx-auto">
          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 shadow-lg">
            <h2 className="text-2xl font-semibold mb-4">使用说明</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-2">基本操作</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• 点击装备槽放入装备（左键随机选择，右键清除）</li>
                  <li>• 选择强化类型（星之力/潜能/额外属性）</li>
                  <li>• 点击强化按钮开始强化</li>
                  <li>• 高等级星之力强化需要迷你游戏</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">快捷键</h3>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">1/2/3</kbd> 切换标签页</li>
                  <li>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Enter</kbd> 开始强化</li>
                  <li>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">E</kbd> 随机选择装备</li>
                  <li>• <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">R</kbd> 清除装备</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
