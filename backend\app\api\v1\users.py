"""
Users API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import time

from app.core.deps import get_db, get_current_active_user
from app.models.user import User
from app.models.membership import UserMembership, MembershipTier
from app.models.currency import CurrencyAccount
from app.schemas.user import UserResponse, UserMembershipResponse

router = APIRouter()

@router.get("/me", response_model=dict)
async def get_current_user(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取当前用户信息"""
    return {
        "success": True,
        "data": UserResponse.from_orm(current_user),
        "timestamp": time.time()
    }

@router.get("/profile", response_model=dict)
async def get_user_profile(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户详细资料"""
    # 获取用户会员信息
    membership = db.query(UserMembership).join(MembershipTier).filter(
        UserMembership.user_id == current_user.id,
        UserMembership.is_active == True
    ).first()

    # 获取虚拟货币账户
    currency_account = db.query(CurrencyAccount).filter(
        CurrencyAccount.user_id == current_user.id
    ).first()

    profile_data = {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "is_active": current_user.is_active,
        "is_verified": current_user.is_verified,
        "created_at": current_user.created_at,
        "last_login": current_user.last_login,
        "membership_level": membership.tier.level if membership else 0,
        "membership_name": membership.tier.display_name if membership else "游客",
        "currency_balance": float(currency_account.balance) if currency_account else 0.0
    }

    return {
        "success": True,
        "data": profile_data,
        "timestamp": time.time()
    }

@router.put("/me", response_model=dict)
async def update_current_user(
    current_user: User = Depends(get_current_active_user)
):
    """更新当前用户信息"""
    return {
        "success": True,
        "data": {
            "message": "Update current user endpoint - to be implemented"
        },
        "timestamp": time.time()
    }

@router.get("/membership", response_model=dict)
async def get_user_membership(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户会员信息"""
    membership = db.query(UserMembership).join(MembershipTier).filter(
        UserMembership.user_id == current_user.id,
        UserMembership.is_active == True
    ).first()

    if not membership:
        # 返回默认的注册用户会员信息
        default_tier = db.query(MembershipTier).filter(
            MembershipTier.name == "registered"
        ).first()

        if default_tier:
            membership_data = {
                "tier_name": default_tier.name,
                "tier_display_name": default_tier.display_name,
                "tier_level": default_tier.level,
                "started_at": current_user.created_at,
                "expires_at": None,
                "is_active": True,
                "permissions": default_tier.permissions
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No membership found"
            )
    else:
        membership_data = {
            "tier_name": membership.tier.name,
            "tier_display_name": membership.tier.display_name,
            "tier_level": membership.tier.level,
            "started_at": membership.started_at,
            "expires_at": membership.expires_at,
            "is_active": membership.is_active,
            "permissions": membership.tier.permissions
        }

    return {
        "success": True,
        "data": membership_data,
        "timestamp": time.time()
    }
