import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { promises as fs } from "fs"
import path from "path"
import { ItemCard } from "@/components/item-card"

// This function reads all images from the cms-216 directory
async function getCMS216Items() {
  try {
    const itemsDirectory = path.join(process.cwd(), "public/images/cms-216")

    // Check if directory exists
    try {
      await fs.access(itemsDirectory)
    } catch {
      // Directory doesn't exist, return empty array
      console.log("CMS-216 directory not found, returning empty array")
      return []
    }

    const filenames = await fs.readdir(itemsDirectory)

    // Filter for image files
    const imageFiles = filenames.filter(
      (name) =>
        name.toLowerCase().endsWith(".png") ||
        name.toLowerCase().endsWith(".jpg") ||
        name.toLowerCase().endsWith(".jpeg") ||
        name.toLowerCase().endsWith(".gif") ||
        name.toLowerCase().endsWith(".webp"),
    )

    // Generate item data from filenames
    return imageFiles.map((filename) => {
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, "")
      // Ensure ID is not null or empty
      const id = nameWithoutExt || `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      return {
        id: id,
        name: nameWithoutExt.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()) || "未知道具",
        image: `/images/cms-216/${filename}`,
        filename: filename,
      }
    })
  } catch (error) {
    console.error("Error reading CMS-216 items:", error)
    return []
  }
}

export default async function CMS216Page() {
  const items = await getCMS216Items()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">版本新增道具</h1>
          <p className="text-gray-600 mt-2">CMS-216 版本新增内容</p>
        </div>
        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          CMS-216
        </Badge>
      </div>

      {/* Instructions for operators */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-800">📋 运营说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-blue-700 space-y-2">
            <p>
              • 将新道具图片放入 <code className="bg-blue-100 px-2 py-1 rounded">public/images/cms-216/</code> 目录
            </p>
            <p>• 支持格式：PNG, JPG, JPEG, GIF, WEBP</p>
            <p>• 图片文件名将自动转换为道具名称</p>
            <p>• 点击道具图片可跳转到详细页面</p>
            <p>• 鼠标悬停可查看道具详情</p>
          </div>
        </CardContent>
      </Card>

      {items.length > 0 ? (
        <>
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">新增道具列表</h2>
            <Badge variant="outline">{items.length} 个道具</Badge>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            {items.map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </>
      ) : (
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <div className="text-6xl mb-4">📦</div>
            <h3 className="text-xl font-semibold mb-2">暂无新增道具</h3>
            <p className="text-gray-600 mb-4">
              请将道具图片放入 <code className="bg-gray-100 px-2 py-1 rounded">public/images/cms-216/</code> 目录
            </p>
            <Button variant="outline">查看使用说明</Button>
          </CardContent>
        </Card>
      )}

      {/* Sample items for demonstration */}
      <Card className="bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>示例道具展示</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="group cursor-pointer">
                <div className="aspect-square bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg mb-2 flex items-center justify-center group-hover:shadow-md transition-shadow">
                  <span className="text-2xl">
                    {["⚔️", "🛡️", "💍", "👑", "🧪", "📜", "💎", "🏹", "🔮", "⚡", "🌟", "🎭"][index]}
                  </span>
                </div>
                <p className="text-xs text-center font-medium text-gray-700 group-hover:text-blue-600 transition-colors">
                  示例道具 {index + 1}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
