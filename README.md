# 🍄 冒险岛情报站 (MapleStory Information Hub)

[![Next.js](https://img.shields.io/badge/Next.js-14.2.16-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.17-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)

> 专业的冒险岛游戏数据库和工具集合网站，为玩家提供全面的游戏信息和实用工具

## 📖 项目简介

冒险岛情报站是一个基于 **Next.js 14** 构建的现代化游戏信息网站，采用 App Router 架构和 TypeScript 开发。项目专注于为冒险岛玩家提供准确、实用的游戏数据和模拟工具，帮助玩家更好地了解游戏机制和制定策略。

### 🎯 核心价值

- **数据准确性**：基于官方游戏数据，确保信息的准确性和时效性
- **工具实用性**：提供真实模拟游戏机制的实用工具
- **用户体验**：现代化的界面设计和流畅的交互体验
- **开放性**：支持社区贡献和数据更新

## ✨ 主要功能

### 🛠️ 游戏工具

| 工具名称 | 功能描述 | 状态 |
|---------|---------|------|
| **装备强化模拟器** | 模拟星之力、潜能、额外属性强化过程 | ✅ 已完成 |
| **星之力强化模拟器** | 专业的星之力强化概率计算和模拟 | ✅ 已完成 |
| **纸娃娃模拟器** | 角色形象搭配和外观预览工具 | 🚧 开发中 |
| **洗魔方模拟器** | 潜能重设和属性优化模拟 | 🚧 开发中 |
| **超级属性计算器** | 超级属性点分配优化计算 | 🚧 开发中 |

### 📊 数据库功能

- **道具数据库**：完整的装备、消耗品、其他道具信息
- **版本更新追踪**：CMS-216 等版本新增内容展示
- **智能搜索**：支持多条件筛选和快速搜索
- **详细信息**：道具属性、获取方式、使用说明等

### 🎨 界面特色

- **响应式设计**：完美适配桌面端和移动端
- **现代化UI**：基于 shadcn/ui 组件库的精美界面
- **主题支持**：支持明暗主题切换
- **动画效果**：流畅的页面过渡和交互动画

## 🚀 快速开始

### 环境要求

- **Node.js**: 18.0.0 或更高版本
- **pnpm**: 推荐使用 pnpm 作为包管理器
- **现代浏览器**: Chrome 88+, Firefox 85+, Safari 14+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/maplestory-info-station.git
   cd maplestory-info-station
   ```

2. **安装依赖**
   ```bash
   pnpm install
   # 或使用 npm
   npm install
   ```

3. **启动开发服务器**
   ```bash
   pnpm dev
   # 或使用 npm
   npm run dev
   ```

4. **访问应用**
   ```
   打开浏览器访问 http://localhost:3000
   ```

### 构建部署

```bash
# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start

# 静态导出（用于静态托管）
pnpm build && pnpm export
```

## 🏗️ 技术架构

### 核心技术栈

- **前端框架**: Next.js 14 (App Router)
- **开发语言**: TypeScript 5
- **样式方案**: Tailwind CSS 3.4.17
- **UI 组件库**: shadcn/ui + Radix UI
- **状态管理**: React Hooks + Context API
- **图标库**: Lucide React
- **字体**: Poppins (Google Fonts)

### 项目结构

```
maplestory-info-station/
├── 📁 app/                    # Next.js App Router 页面
│   ├── 📄 layout.tsx         # 全局布局组件
│   ├── 📄 page.tsx           # 首页
│   ├── 📁 cms-216/           # CMS-216 版本新增道具
│   ├── 📁 tools/             # 工具集合
│   │   └── 📁 enhancement/   # 装备强化模拟器
│   ├── 📁 starforce/         # 星之力强化模拟器
│   ├── 📁 database/          # 游戏数据库
│   └── 📁 items/[id]/        # 动态道具详情页
├── 📁 components/            # React 组件
│   ├── 📁 ui/               # shadcn/ui 基础组件
│   ├── 📁 enhancement-simulator/ # 强化模拟器组件
│   ├── 📄 header.tsx        # 网站头部导航
│   └── 📄 sidebar.tsx       # 侧边栏组件
├── 📁 lib/                  # 工具函数库
│   ├── 📄 enhancement-utils.ts # 强化相关工具函数
│   └── 📄 utils.ts          # 通用工具函数
├── 📁 types/                # TypeScript 类型定义
│   └── 📄 enhancement.ts    # 强化相关类型
├── 📁 public/               # 静态资源
│   ├── 📁 images/           # 图片资源
│   └── 📁 data/             # 数据文件
└── 📁 hooks/                # 自定义 React Hooks
```

## 🎮 功能详解

### 装备强化模拟器

装备强化模拟器是本项目的核心功能之一，提供了高度还原的冒险岛装备强化体验。

#### 主要特性

- **三种强化类型**：
  - 🌟 **星之力强化** (0-25星)：模拟真实的星之力强化概率和费用
  - 🎯 **潜能重设**：模拟潜在能力重新设定过程
  - ⚡ **额外属性强化**：模拟额外属性提升机制

- **真实游戏体验**：
  - 基于官方数据的成功率计算
  - 完整的强化动画效果
  - 迷你游戏机制（高等级星之力强化）
  - 智能装备选择和筛选

- **高级功能**：
  - 装备类型筛选（按强化类型）
  - 成功率加成计算
  - 费用统计和分析
  - 快捷键操作支持

#### 使用方法

1. **选择装备**：点击装备槽选择要强化的装备
2. **选择强化类型**：切换到对应的强化标签页
3. **设置参数**：配置强化相关选项（如安全防护、活动优惠等）
4. **开始强化**：点击强化按钮或使用快捷键开始强化
5. **查看结果**：观看强化动画并查看结果

### 星之力强化模拟器

专门针对星之力强化的独立模拟器，提供更详细的分析功能。

#### 核心功能

- **概率计算**：精确的成功率、失败率、大失败率计算
- **费用预估**：基于目标星数的费用预估和统计
- **批量模拟**：支持大量模拟以获得统计数据
- **优惠计算**：MVP优惠、活动优惠等费用计算
- **安全防护**：模拟安全防护机制的效果

### 数据库系统

#### CMS-216 版本新增道具

- **自动化管理**：通过文件系统自动读取道具图片
- **智能命名**：文件名自动转换为道具显示名称
- **多格式支持**：PNG、JPG、JPEG、GIF、WEBP 等格式
- **运营友好**：简单的文件上传即可添加新道具

#### 道具详情系统

- **动态路由**：`/items/[id]` 支持任意道具ID访问
- **详细信息**：道具属性、获取方式、使用说明
- **图片展示**：高清道具图片和缩略图
- **相关推荐**：相似道具推荐功能

## 📱 界面展示

### 首页设计

- **渐变背景**：现代化的蓝紫色渐变背景
- **功能导航**：清晰的工具分类和快速访问
- **数据统计**：实时的道具数量和功能统计
- **版本更新**：突出显示最新版本内容

### 工具界面

- **统一设计语言**：所有工具采用一致的界面风格
- **响应式布局**：完美适配各种屏幕尺寸
- **交互反馈**：丰富的动画效果和状态提示
- **快捷操作**：支持键盘快捷键和右键菜单

## 🔧 开发特色

### 代码组织

项目采用模块化的代码组织方式：

- **页面组件**：每个功能页面独立组件化
- **业务组件**：可复用的业务逻辑组件
- **UI组件**：基于 shadcn/ui 的统一UI组件
- **工具函数**：纯函数式的工具方法库
- **类型定义**：完整的 TypeScript 类型系统

### 性能优化

- **静态生成**：使用 Next.js 静态导出优化加载速度
- **图片优化**：支持多种图片格式和懒加载
- **代码分割**：按页面和功能进行代码分割
- **缓存策略**：合理的缓存策略提升用户体验

### 开发体验

- **TypeScript**：完整的类型安全保障
- **ESLint + Prettier**：统一的代码风格
- **热重载**：开发时的快速反馈
- **组件文档**：清晰的组件使用说明

## 📚 使用指南

### 快捷键操作

装备强化模拟器支持以下快捷键：

| 快捷键 | 功能 |
|--------|------|
| `1/2/3` | 切换强化类型标签页 |
| `Enter` | 开始强化 |
| `E` | 随机选择装备 |
| `R` | 清除当前装备 |
| `Space` | 暂停/继续自动强化 |

### 数据管理

#### 添加新道具

1. **准备图片文件**
   - 支持格式：PNG, JPG, JPEG, GIF, WEBP
   - 建议尺寸：64x64 像素或更高
   - 文件命名：使用英文和下划线，如 `dragon_sword.png`

2. **上传到指定目录**
   ```bash
   # 将图片放入对应目录
   public/images/cms-216/your_item.png
   ```

3. **自动生成数据**
   - 文件名自动转换为显示名称
   - 下划线转换为空格
   - 首字母自动大写
   - 例：`dragon_sword.png` → "Dragon Sword"

4. **重新构建**
   ```bash
   pnpm build
   ```

### 配置选项

#### 强化模拟器设置

- **安全防护**：12-16星可选择安全防护（费用翻倍，失败不损毁）
- **MVP优惠**：30%费用减免
- **活动优惠**：30%费用减免（可与MVP叠加）
- **抓星星**：成功率加成（常法/加法两种算法）

#### 界面自定义

- **主题切换**：支持明暗主题
- **语言设置**：当前支持中文
- **响应式布局**：自动适配设备屏幕

## 🛠️ 开发指南

### 项目结构详解

```
maplestory-info-station/
├── 📁 app/                     # Next.js App Router
│   ├── 📄 layout.tsx          # 全局布局（字体、主题、导航）
│   ├── 📄 page.tsx            # 首页（功能导航、数据统计）
│   ├── 📄 globals.css         # 全局样式
│   ├── 📁 cms-216/            # CMS-216版本道具
│   │   └── 📄 page.tsx        # 道具列表页面
│   ├── 📁 tools/              # 工具集合
│   │   ├── 📄 page.tsx        # 工具导航页
│   │   └── 📁 enhancement/    # 装备强化模拟器
│   │       └── 📄 page.tsx    # 强化模拟器页面
│   ├── 📁 starforce/          # 星之力强化模拟器
│   ├── 📁 database/           # 游戏数据库
│   ├── 📁 items/[id]/         # 动态道具详情
│   ├── 📁 paperdoll/          # 纸娃娃模拟器
│   ├── 📁 cubes/              # 洗魔方模拟器
│   └── 📁 hyperstats/         # 超级属性计算器
├── 📁 components/             # React组件
│   ├── 📁 ui/                 # shadcn/ui基础组件
│   ├── 📁 enhancement-simulator/ # 强化模拟器组件
│   │   ├── 📄 EnhancementSimulator.tsx # 主模拟器组件
│   │   ├── 📄 TabSelector.tsx  # 标签页选择器
│   │   ├── 📄 EquipmentSlot.tsx # 装备槽组件
│   │   ├── 📄 ItemSelector.tsx # 装备选择器
│   │   ├── 📄 InfoPanel.tsx   # 信息面板
│   │   ├── 📄 CostPanel.tsx   # 费用面板
│   │   ├── 📄 StarProgress.tsx # 星级进度
│   │   ├── 📄 EffectRenderer.tsx # 特效渲染器
│   │   ├── 📄 MinigameOverlay.tsx # 迷你游戏
│   │   ├── 📄 ResultOverlay.tsx # 结果显示
│   │   ├── 📄 ConfirmDialog.tsx # 确认对话框
│   │   ├── 📄 LoadingScreen.tsx # 加载屏幕
│   │   └── 📄 index.ts        # 组件导出
│   ├── 📄 header.tsx          # 网站头部导航
│   ├── 📄 sidebar.tsx         # 侧边栏
│   ├── 📄 item-card.tsx       # 道具卡片
│   └── 📄 item-tooltip.tsx    # 道具提示框
├── 📁 lib/                    # 工具函数库
│   ├── 📄 enhancement-utils.ts # 强化相关工具
│   ├── 📄 item-filter-utils.ts # 道具筛选工具
│   ├── 📄 resource-preloader.ts # 资源预加载
│   └── 📄 utils.ts            # 通用工具函数
├── 📁 types/                  # TypeScript类型定义
│   └── 📄 enhancement.ts      # 强化相关类型
├── 📁 hooks/                  # 自定义React Hooks
│   ├── 📄 use-mobile.tsx      # 移动端检测
│   └── 📄 use-toast.ts        # 消息提示
├── 📁 public/                 # 静态资源
│   ├── 📁 images/             # 图片资源
│   │   ├── 📁 cms-216/        # CMS-216道具图片
│   │   └── 📁 UIEquipEnchant/ # 强化UI资源
│   ├── 📁 data/               # 数据文件
│   │   ├── 📄 itemList.js     # 装备数据库
│   │   └── 📄 itemList.json   # JSON格式装备数据
│   └── 📄 UIEquipEnchant.img.xml # UI资源映射
└── 📁 scripts/                # 构建脚本
    ├── 📄 gen-json-cms-216.js # 生成道具JSON
    ├── 📄 build-test.js       # 构建测试
    └── 📄 final-build-verification.js # 构建验证
```

### 核心组件说明

#### EnhancementSimulator 主组件

装备强化模拟器的核心组件，管理整个强化流程：

- **状态管理**：使用 React Hooks 管理复杂状态
- **事件处理**：键盘快捷键、鼠标交互
- **动画控制**：强化特效和过渡动画
- **数据计算**：强化概率和费用计算

#### 工具函数库

`lib/enhancement-utils.ts` 包含核心业务逻辑：

- **概率计算**：基于官方数据的成功率计算
- **费用计算**：考虑各种优惠的费用计算
- **装备筛选**：按强化类型筛选兼容装备
- **属性计算**：装备属性和加成计算

### 数据流架构

```mermaid
graph TD
    A[用户操作] --> B[React组件]
    B --> C[工具函数]
    C --> D[数据计算]
    D --> E[状态更新]
    E --> F[UI重渲染]
    F --> G[动画效果]

    H[静态资源] --> I[文件系统API]
    I --> J[数据预处理]
    J --> K[组件渲染]
```

## 🚀 部署指南

### 静态部署 (推荐)

项目配置为静态导出，可部署到任何静态托管服务：

```bash
# 构建静态文件
pnpm build

# 输出目录: out/
# 将 out/ 目录内容上传到静态托管服务
```

### 支持的托管平台

1. **Vercel** (推荐)
   ```bash
   # 安装 Vercel CLI
   npm i -g vercel

   # 部署
   vercel --prod
   ```

2. **Netlify**
   - 连接 GitHub 仓库
   - 构建命令: `pnpm build`
   - 发布目录: `out`

3. **GitHub Pages**
   ```yaml
   # .github/workflows/deploy.yml
   name: Deploy to GitHub Pages
   on:
     push:
       branches: [ main ]
   jobs:
     build-and-deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v2
         - uses: actions/setup-node@v2
           with:
             node-version: '18'
         - run: npm install
         - run: npm run build
         - uses: peaceiris/actions-gh-pages@v3
           with:
             github_token: ${{ secrets.GITHUB_TOKEN }}
             publish_dir: ./out
   ```

## 🤝 贡献指南

### 如何贡献

1. **Fork 本仓库**
2. **创建功能分支**
   ```bash
   git checkout -b feature/AmazingFeature
   ```
3. **提交更改**
   ```bash
   git commit -m 'Add some AmazingFeature'
   ```
4. **推送到分支**
   ```bash
   git push origin feature/AmazingFeature
   ```
5. **开启 Pull Request**

### 代码规范

- **TypeScript**：使用 TypeScript 进行类型检查
- **ESLint**：遵循 ESLint 规则
- **代码风格**：保持代码简洁和可读性
- **注释**：为复杂逻辑添加适当的注释
- **组件规范**：遵循项目的组件开发模式

### 提交规范

使用语义化提交信息：

```bash
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 🔧 故障排除

### 常见问题

#### 1. 道具图片不显示
- **检查路径**：确认图片文件存在于 `public/images/cms-216/` 目录
- **格式支持**：确认图片格式为 PNG, JPG, JPEG, GIF, WEBP
- **文件命名**：避免使用特殊字符，推荐使用英文和下划线
- **权限问题**：确认文件具有读取权限

#### 2. 构建失败
- **依赖安装**：运行 `pnpm install` 重新安装依赖
- **TypeScript 错误**：检查类型定义和语法错误
- **环境变量**：确认必需的环境变量已正确设置
- **内存不足**：增加 Node.js 内存限制

#### 3. 样式不生效
- **Tailwind 配置**：检查 `tailwind.config.ts` 配置
- **类名拼写**：确认 CSS 类名拼写正确
- **缓存清理**：清除浏览器缓存或重启开发服务器
- **CSS 冲突**：检查是否有样式冲突

#### 4. 强化模拟器问题
- **数据加载**：确认装备数据文件正确加载
- **概率计算**：检查强化概率数据是否正确
- **动画卡顿**：检查浏览器性能和资源加载

### 调试技巧

```bash
# 查看构建详细信息
npm run build -- --debug

# 分析包大小
npm install -g @next/bundle-analyzer
ANALYZE=true npm run build

# 检查 TypeScript 错误
npx tsc --noEmit

# 运行 ESLint 检查
npx eslint . --ext .ts,.tsx
```

### 性能优化建议

1. **图片优化**
   - 使用 WebP 格式
   - 压缩图片文件大小
   - 实现懒加载

2. **代码优化**
   - 移除未使用的依赖
   - 优化组件渲染
   - 使用 React.memo 避免不必要的重渲染

3. **缓存策略**
   - 设置适当的缓存头
   - 使用 CDN 加速静态资源
   - 实现服务端缓存

## 📊 项目统计

### 技术指标

- **代码行数**：约 15,000+ 行
- **组件数量**：50+ 个 React 组件
- **页面数量**：10+ 个功能页面
- **工具数量**：5+ 个游戏工具
- **支持格式**：5 种图片格式
- **响应式支持**：完全响应式设计

### 功能覆盖

- ✅ **装备强化模拟器**：完整实现
- ✅ **星之力强化模拟器**：完整实现
- ✅ **道具数据库**：基础实现
- ✅ **CMS-216 版本支持**：完整实现
- 🚧 **纸娃娃模拟器**：开发中
- 🚧 **洗魔方模拟器**：开发中
- 🚧 **超级属性计算器**：开发中

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

### 免责声明

- 本项目仅供学习和研究使用
- 所有游戏相关内容版权归 Nexon 所有
- 项目中的游戏数据和图片资源仅用于演示目的
- 不得用于商业用途

### 第三方资源

- **shadcn/ui**：MIT 许可证
- **Tailwind CSS**：MIT 许可证
- **Next.js**：MIT 许可证
- **React**：MIT 许可证
- **Lucide React**：ISC 许可证

---

## 📞 联系方式

- **开发者**：小帽子
- **QQ**：499151029
- **网站**：Mxd.dvg.cn
- **更新日期**：2025-06-20

### 支持与反馈

如果您在使用过程中遇到问题或有改进建议，欢迎通过以下方式联系：

1. **GitHub Issues**：在项目仓库中提交 Issue
2. **QQ 联系**：499151029
3. **邮件反馈**：通过项目页面联系表单

感谢您对冒险岛情报站项目的关注和支持！🎮✨

## 🛠️ 开发指南

### 添加新功能页面

1. 在 `app/` 目录下创建新的页面文件夹
2. 创建 `page.tsx` 文件作为页面组件
3. 在 `components/header.tsx` 中添加导航链接
4. 更新首页 `app/page.tsx` 中的工具列表

### 组件开发规范

```typescript
// 组件文件结构示例
"use client" // 如果需要客户端功能

import { ComponentProps } from "@/components/ui/component"

interface MyComponentProps {
  // 定义 props 类型
}

export function MyComponent({ ...props }: MyComponentProps) {
  return (
    // JSX 内容
  )
}
```

### 样式指南

- 使用 Tailwind CSS 类名
- 保持一致的间距：`space-y-6`, `gap-4`
- 使用半透明背景：`bg-white/80 backdrop-blur-sm`
- 统一的圆角：`rounded-lg`
- 过渡动画：`transition-all duration-300`

### 数据处理

#### 添加新的道具类别

1. 在 `public/images/` 下创建新的类别文件夹
2. 参考 `app/cms-216/page.tsx` 创建读取逻辑
3. 实现类似的 `getItems()` 函数

#### 静态数据生成

```javascript
// scripts/gen-data.js 示例
const fs = require('fs')
const path = require('path')

function generateItemData(category) {
  const itemsDir = path.join(process.cwd(), `public/images/${category}`)
  const files = fs.readdirSync(itemsDir)

  return files.map(file => ({
    id: file.replace(/\.[^/.]+$/, ''),
    name: formatName(file),
    image: `/images/${category}/${file}`
  }))
}
```

## 🧪 测试

### 构建测试

项目包含多个构建测试脚本：

```bash
# 基础构建测试
node scripts/build-test.js

# 静态生成测试
node scripts/ssg-build-test.js

# 最终构建验证
node scripts/final-build-verification.js
```

### 手动测试清单

- [ ] 首页加载正常
- [ ] 导航菜单功能正常
- [ ] 道具列表显示正确
- [ ] 道具详情页面可访问
- [ ] 星之力模拟器计算正确
- [ ] 响应式布局在移动端正常
- [ ] 图片加载和错误处理

## 📊 性能优化

### 已实现的优化

1. **静态生成**: 使用 Next.js SSG 预生成页面
2. **图片优化**: 懒加载和错误处理
3. **代码分割**: Next.js 自动代码分割
4. **CSS 优化**: Tailwind CSS 的 purge 功能

### 建议的优化

1. **图片压缩**: 使用 WebP 格式
2. **缓存策略**: 设置适当的缓存头
3. **CDN 部署**: 使用 CDN 加速静态资源
4. **预加载**: 关键资源预加载

## 🔧 故障排除

### 常见问题

#### 1. 道具图片不显示
- 检查图片文件是否存在于 `public/images/cms-216/` 目录
- 确认图片格式是否支持 (PNG, JPG, JPEG, GIF, WEBP)
- 检查文件名是否包含特殊字符

#### 2. 构建失败
- 运行 `pnpm install` 重新安装依赖
- 检查 TypeScript 错误
- 确认所有必需的环境变量已设置

#### 3. 样式不生效
- 确认 Tailwind CSS 配置正确
- 检查类名是否拼写正确
- 运行 `pnpm dev` 重启开发服务器

### 调试技巧

```bash
# 查看构建详细信息
npm run build -- --debug

# 分析包大小
npm install -g @next/bundle-analyzer
ANALYZE=true npm run build
```

## 🔮 未来规划

### 短期目标 (1-3 个月)
- [ ] 完善星之力模拟器算法
- [ ] 添加更多道具类别
- [ ] 优化移动端体验
- [ ] 添加搜索功能

### 中期目标 (3-6 个月)
- [ ] 实现用户系统
- [ ] 添加收藏和分享功能
- [ ] 集成更多游戏工具
- [ ] 数据库 API 开发

### 长期目标 (6+ 个月)
- [ ] 多语言支持
- [ ] PWA 功能
- [ ] 实时数据同步
- [ ] 社区功能

## 🤝 贡献指南

### 如何贡献

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 保持代码简洁和可读性
- 添加适当的注释

## 📄 许可证

本项目仅供学习和研究使用。所有游戏相关内容版权归 Nexon 所有。

---

**联系方式**: 小帽子：499151029
**网站地址**: Mxd.dvg.cn
**更新日期**: 2025-06-19
