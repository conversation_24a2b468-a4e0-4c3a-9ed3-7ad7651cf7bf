'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { apiClient } from '@/lib/api-client'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  data?: any
}

export default function ApiTestPage() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Health Check', status: 'pending', message: 'Not tested' },
    { name: 'Auth Register', status: 'pending', message: 'Not tested' },
    { name: 'Auth Login', status: 'pending', message: 'Not tested' },
    { name: 'Get User', status: 'pending', message: 'Not tested' },
    { name: 'Get Balance', status: 'pending', message: 'Not tested' },
    { name: 'Starforce Enhancement', status: 'pending', message: 'Not tested' },
  ])

  const updateTest = (index: number, status: TestResult['status'], message: string, data?: any) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message, data } : test
    ))
  }

  const runHealthCheck = async () => {
    try {
      updateTest(0, 'pending', 'Testing...')
      const response = await apiClient.healthCheck()
      updateTest(0, 'success', 'Backend is healthy', response)
    } catch (error) {
      updateTest(0, 'error', `Failed: ${error}`)
    }
  }

  const runAuthRegister = async () => {
    try {
      updateTest(1, 'pending', 'Testing...')
      const response = await apiClient.register({
        username: 'testuser',
        password: 'testpass123'
      })
      updateTest(1, 'success', 'Register endpoint working', response)
    } catch (error) {
      updateTest(1, 'error', `Failed: ${error}`)
    }
  }

  const runAuthLogin = async () => {
    try {
      updateTest(2, 'pending', 'Testing...')
      const response = await apiClient.login({
        username: 'testuser',
        password: 'testpass123'
      })
      updateTest(2, 'success', 'Login endpoint working', response)
    } catch (error) {
      updateTest(2, 'error', `Failed: ${error}`)
    }
  }

  const runGetUser = async () => {
    try {
      updateTest(3, 'pending', 'Testing...')
      const response = await apiClient.getCurrentUser()
      updateTest(3, 'success', 'Get user endpoint working', response)
    } catch (error) {
      updateTest(3, 'error', `Failed: ${error}`)
    }
  }

  const runGetBalance = async () => {
    try {
      updateTest(4, 'pending', 'Testing...')
      const response = await apiClient.getBalance()
      updateTest(4, 'success', 'Get balance endpoint working', response)
    } catch (error) {
      updateTest(4, 'error', `Failed: ${error}`)
    }
  }

  const runStarforceEnhancement = async () => {
    try {
      updateTest(5, 'pending', 'Testing...')
      const response = await apiClient.starforceEnhancement({
        equipment_id: '1001',
        current_level: 10,
        target_level: 11
      })
      updateTest(5, 'success', 'Starforce endpoint working', response)
    } catch (error) {
      updateTest(5, 'error', `Failed: ${error}`)
    }
  }

  const runAllTests = async () => {
    await runHealthCheck()
    await runAuthRegister()
    await runAuthLogin()
    await runGetUser()
    await runGetBalance()
    await runStarforceEnhancement()
  }

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-500">Success</Badge>
      case 'error':
        return <Badge className="bg-red-500">Error</Badge>
      case 'pending':
        return <Badge className="bg-gray-500">Pending</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">API 连接测试</h1>
        <p className="text-gray-600">测试前后端连接和 API 端点</p>
      </div>

      <div className="mb-6">
        <Button onClick={runAllTests} className="mr-4">
          运行所有测试
        </Button>
        <Button onClick={runHealthCheck} variant="outline">
          仅测试健康检查
        </Button>
      </div>

      <div className="grid gap-4">
        {tests.map((test, index) => (
          <Card key={test.name}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{test.name}</CardTitle>
                {getStatusBadge(test.status)}
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-2">
                {test.message}
              </CardDescription>
              {test.data && (
                <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
                  {JSON.stringify(test.data, null, 2)}
                </pre>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-2">测试说明</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Health Check: 测试后端服务是否正常运行</li>
          <li>• Auth 端点: 测试认证相关 API（当前返回占位符响应）</li>
          <li>• User 端点: 测试用户相关 API</li>
          <li>• Currency 端点: 测试虚拟货币相关 API</li>
          <li>• Enhancement 端点: 测试装备强化相关 API</li>
        </ul>
      </div>
    </div>
  )
}
