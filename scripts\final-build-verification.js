const { execSync } = require("child_process")
const fs = require("fs")
const path = require("path")

console.log("🚀 Final build verification...")

try {
  // Ensure all necessary directories exist
  const dirs = ["public/images/cms-216", "app", "components/ui", "lib"]

  dirs.forEach((dir) => {
    const fullPath = path.join(process.cwd(), dir)
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true })
      console.log(`✅ Created directory: ${dir}`)
    }
  })

  // Check for problematic files that might cause build issues
  const problematicFiles = ["app/api/items/[id]/route.ts", "app/items/[id]/page.tsx"]

  problematicFiles.forEach((file) => {
    const fullPath = path.join(process.cwd(), file)
    if (fs.existsSync(fullPath)) {
      console.log(`⚠️  Found problematic file: ${file}`)
      fs.unlinkSync(fullPath)
      console.log(`🗑️  Removed: ${file}`)
    }
  })

  // Run the build
  console.log("🔨 Running build...")
  const result = execSync("npm run build", {
    encoding: "utf8",
    stdio: "pipe",
    timeout: 180000, // 3 minutes timeout
  })

  console.log("🎉 BUILD SUCCESSFUL!")
  console.log("📊 Build output:")
  console.log(result)

  // Check if out directory was created (for static export)
  const outDir = path.join(process.cwd(), "out")
  if (fs.existsSync(outDir)) {
    console.log("✅ Static export directory 'out' created successfully")

    // List some key files to verify
    const keyFiles = ["index.html", "cms-216.html", "starforce.html"]
    keyFiles.forEach((file) => {
      const filePath = path.join(outDir, file)
      if (fs.existsSync(filePath)) {
        console.log(`✅ Generated: ${file}`)
      } else {
        console.log(`⚠️  Missing: ${file}`)
      }
    })
  }
} catch (error) {
  console.log("❌ BUILD FAILED!")
  console.log("Error details:")
  console.log("STDERR:", error.stderr)
  console.log("STDOUT:", error.stdout)

  // Provide specific guidance
  const errorOutput = (error.stderr || "") + (error.stdout || "")

  if (errorOutput.includes("generateStaticParams")) {
    console.log("\n🔍 Still have dynamic route issues")
  }

  if (errorOutput.includes("Module not found")) {
    console.log("\n🔍 Missing module dependencies")
  }

  if (errorOutput.includes("Type error")) {
    console.log("\n🔍 TypeScript compilation errors")
  }
}
