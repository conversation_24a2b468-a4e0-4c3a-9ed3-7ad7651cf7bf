"""
Dependency injection for FastAPI
"""

from typing import Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User
from app.models.membership import MembershipTier, UserMembership
from app.utils.security import verify_token

# Security scheme
security = HTTPBearer(auto_error=False)

async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get current authenticated user from JWT token
    """
    if not credentials:
        return None
    
    try:
        # Verify JWT token using our security utility
        payload = verify_token(credentials.credentials)
        if not payload:
            return None

        user_id = payload.get("user_id")
        if not user_id:
            return None

        # Get user from database
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            return None

        return user

    except Exception:
        return None

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (required authentication)
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return current_user

async def get_current_verified_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Get current verified user (email verification required)
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email verification required"
        )
    
    return current_user

async def get_user_membership(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Optional[dict]:
    """
    Get user membership information
    """
    if not current_user:
        # Return guest membership for unauthenticated users
        guest_tier = db.query(MembershipTier).filter(
            MembershipTier.name == "guest"
        ).first()
        
        if guest_tier:
            return {
                "tier": guest_tier.name,
                "display_name": guest_tier.display_name,
                "level": guest_tier.level,
                "permissions": guest_tier.permissions,
                "expires_at": None
            }
        return None
    
    # Get active membership for authenticated user
    membership = db.query(UserMembership).join(MembershipTier).filter(
        UserMembership.user_id == current_user.id,
        UserMembership.is_active == True
    ).first()
    
    if membership:
        return {
            "tier": membership.tier.name,
            "display_name": membership.tier.display_name,
            "level": membership.tier.level,
            "permissions": membership.tier.permissions,
            "expires_at": membership.expires_at
        }
    
    # Default to registered user membership
    registered_tier = db.query(MembershipTier).filter(
        MembershipTier.name == "registered"
    ).first()
    
    if registered_tier:
        return {
            "tier": registered_tier.name,
            "display_name": registered_tier.display_name,
            "level": registered_tier.level,
            "permissions": registered_tier.permissions,
            "expires_at": None
        }
    
    return None

async def require_membership_level(min_level: int):
    """
    Dependency factory for requiring minimum membership level
    """
    async def check_membership(
        membership: dict = Depends(get_user_membership)
    ):
        if not membership or membership["level"] < min_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Membership level {min_level} or higher required"
            )
        return membership
    
    return check_membership

async def require_admin(
    current_user: User = Depends(get_current_active_user),
    membership: dict = Depends(get_user_membership)
):
    """
    Require admin privileges
    """
    if not membership or membership["level"] < 4:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user

async def get_fingerprint_id(request: Request) -> Optional[str]:
    """
    Extract fingerprint ID from request headers
    """
    return request.headers.get("X-Fingerprint-ID")

async def get_client_ip(request: Request) -> str:
    """
    Get client IP address
    """
    # Check for forwarded headers first
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # Fallback to direct connection
    return request.client.host if request.client else "unknown"

async def get_user_agent(request: Request) -> str:
    """
    Get user agent string
    """
    return request.headers.get("User-Agent", "unknown")
