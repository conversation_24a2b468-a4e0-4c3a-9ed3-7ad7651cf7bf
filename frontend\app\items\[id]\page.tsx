import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { notFound } from "next/navigation"

interface ItemDetailPageProps {
  params: {
    id: string
  }
}

// 静态生成参数 - 预生成常见的道具ID
export async function generateStaticParams() {
  // 这里可以从数据库或API获取所有道具ID
  // 为了演示，我们生成一些示例ID
  const commonItemIds = [
    "1",
  ]

  return commonItemIds.map((id) => ({
    id: id,
  }))
}

export default function ItemDetailPage({ params }: ItemDetailPageProps) {
  const { id } = params

  // 验证ID是否有效
  if (!id || id.trim() === "") {
    notFound()
  }

  // 解码URL参数
  const decodedId = decodeURIComponent(id)
  const itemName = decodedId.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Link href="/cms-216">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回列表
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">{itemName}</h1>
          <p className="text-gray-600">道具详细信息</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Item Image and Basic Info */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>道具信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center">
              <div className="w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                <Image
                  src={`/images/cms-216/${decodedId}.png`}
                  alt={itemName}
                  width={128}
                  height={128}
                  className="max-w-full max-h-full object-contain"
                  /*onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.src = "/placeholder.svg?height=128&width=128"
                  }}*/
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">道具名称:</span>
                <span className="font-semibold">{itemName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">版本:</span>
                <Badge variant="secondary">CMS-216</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">类型:</span>
                <span>装备道具</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">等级要求:</span>
                <span>200级</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Item Stats */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>道具属性</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>攻击力:</span>
                <span className="font-semibold text-red-600">+150</span>
              </div>
              <div className="flex justify-between">
                <span>STR:</span>
                <span className="font-semibold text-blue-600">+50</span>
              </div>
              <div className="flex justify-between">
                <span>DEX:</span>
                <span className="font-semibold text-green-600">+30</span>
              </div>
              <div className="flex justify-between">
                <span>总伤害:</span>
                <span className="font-semibold text-purple-600">+12%</span>
              </div>
              <div className="flex justify-between">
                <span>BOSS伤害:</span>
                <span className="font-semibold text-orange-600">+30%</span>
              </div>
              <div className="flex justify-between">
                <span>无视防御:</span>
                <span className="font-semibold text-indigo-600">+20%</span>
              </div>
            </div>

            <hr className="border-gray-200" />

            <div>
              <h4 className="font-semibold mb-2">特殊效果:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 攻击时有概率发动雷电攻击</li>
                <li>• 增加移动速度和跳跃力</li>
                <li>• 可进行星之力强化</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>获取方式</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span>商城购买</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>活动奖励</span>
              </li>
              <li className="flex items-center space-x-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span>BOSS掉落</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>相关道具</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-2">
              {Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"
                >
                  <span className="text-xl">{["⚔️", "🛡️", "💍", "👑"][index]}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
