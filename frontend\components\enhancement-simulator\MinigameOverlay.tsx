'use client'

import { useEffect, useState } from 'react'
import { MinigameOverlayProps } from '@/types/enhancement'

export default function MinigameOverlay({ isActive, gaugePosition, onStop }: MinigameOverlayProps) {
  const [position, setPosition] = useState(0)
  const [direction, setDirection] = useState(1)
  
  useEffect(() => {
    if (!isActive) {
      setPosition(0)
      setDirection(1)
      return
    }
    
    const timer = setInterval(() => {
      setPosition(prev => {
        let newPosition = prev + direction * 3
        let newDirection = direction
        
        if (newPosition >= 100) {
          newPosition = 100
          newDirection = -1
        } else if (newPosition <= 0) {
          newPosition = 0
          newDirection = 1
        }
        
        setDirection(newDirection)
        return newPosition
      })
    }, 50)
    
    return () => clearInterval(timer)
  }, [isActive, direction])
  
  if (!isActive) {
    return null
  }
  
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg p-8 max-w-md w-full mx-4">
        <h3 className="text-white text-xl font-bold text-center mb-6">
          迷你游戏
        </h3>
        
        <div className="mb-6">
          <p className="text-gray-300 text-sm text-center mb-4">
            在最佳时机按下空格键或点击停止按钮！
          </p>
          
          {/* 进度条容器 */}
          <div className="relative w-full h-8 bg-gray-700 rounded-lg overflow-hidden">
            {/* 最佳区域指示器 */}
            <div 
              className="absolute h-full bg-green-500/30"
              style={{
                left: '35%',
                width: '30%',
              }}
            />
            <div 
              className="absolute h-full bg-green-500/50"
              style={{
                left: '45%',
                width: '10%',
              }}
            />
            
            {/* 移动的进度条 */}
            <div 
              className="absolute h-full w-2 bg-yellow-400 transition-all duration-75"
              style={{
                left: `${position}%`,
                transform: 'translateX(-50%)',
              }}
            />
          </div>
          
          {/* 位置指示器 */}
          <div className="mt-2 text-center">
            <span className="text-gray-400 text-sm">
              位置: {position.toFixed(1)}%
            </span>
          </div>
        </div>
        
        <div className="flex justify-center space-x-4">
          <button
            onClick={onStop}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
          >
            停止 (空格键)
          </button>
        </div>
        
        <div className="mt-4 text-center">
          <div className="text-xs text-gray-400 space-y-1">
            <div>🟢 绿色区域: +5% 成功率</div>
            <div>🟢 深绿区域: +10% 成功率</div>
          </div>
        </div>
      </div>
    </div>
  )
}
