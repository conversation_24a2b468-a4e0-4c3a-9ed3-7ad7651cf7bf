# MSU装备强化模拟器 - 开发文档

## 📋 目录

- [架构概览](#架构概览)
- [技术栈](#技术栈)
- [项目结构详解](#项目结构详解)
- [核心组件](#核心组件)
- [开发规范](#开发规范)
- [API接口](#api接口)
- [UI布局系统](#ui布局系统)
- [动画系统](#动画系统)
- [数据管理](#数据管理)
- [调试工具](#调试工具)
- [性能优化](#性能优化)
- [测试指南](#测试指南)
- [部署说明](#部署说明)

## 🏗 架构概览

### 整体架构
```
┌─────────────────────────────────────────┐
│                用户界面层                │
│  ┌─────────┬─────────┬─────────────────┐ │
│  │ HTML    │ CSS     │ 用户交互事件     │ │
│  └─────────┴─────────┴─────────────────┘ │
├─────────────────────────────────────────┤
│                业务逻辑层                │
│  ┌─────────┬─────────┬─────────────────┐ │
│  │强化系统 │装备管理 │ 特效动画系统    │ │
│  └─────────┴─────────┴─────────────────┘ │
├─────────────────────────────────────────┤
│                数据访问层                │
│  ┌─────────┬─────────┬─────────────────┐ │
│  │装备数据库│XML配置 │ 本地存储        │ │
│  └─────────┴─────────┴─────────────────┘ │
└─────────────────────────────────────────┘
```

### 核心设计原则
1. **原版还原**: 严格按照原版游戏UI和交互逻辑
2. **模块化**: 功能模块独立，便于维护和扩展
3. **数据驱动**: 通过配置文件控制UI布局和游戏规则
4. **响应式**: 支持不同屏幕尺寸的设备
5. **可调试**: 内置调试工具，便于开发和测试

## 🛠 技术栈

### 前端技术
- **HTML5**: 语义化标签，现代Web标准
- **CSS3**: Flexbox/Grid布局，CSS变量，动画
- **Vanilla JavaScript (ES6+)**: 类、模块、异步编程
- **Web APIs**: Fetch API、Canvas API、Storage API

### 开发工具
- **浏览器开发者工具**: 调试JavaScript和CSS
- **Live Server**: 本地开发服务器
- **Git**: 版本控制

### 资源管理
- **XML配置**: UI布局定义
- **JSON数据**: 装备数据库
- **PNG图片**: 游戏UI资源和装备图片

## 📁 项目结构详解

```
强化模拟器/
├── 📄 index.html              # 主页面入口
├── 🎨 styles.css              # 全局样式定义
├── ⚙️ script.js               # 主要业务逻辑
├── 📊 itemList.json           # 装备数据库
├── 📖 README.md               # 项目说明文档
├── 📋 DEVELOPMENT.md          # 开发文档（本文件）
├── 🔧 UIEquipEnchant.img.xml  # UI布局配置
├── 🖼️ UIEquipEnchant.img/     # 游戏UI资源
│   ├── Main/                  # 主界面资源
│   │   ├── Background.png     # 主背景 (507x712)
│   │   ├── Tab/              # 标签页资源
│   │   │   ├── Starforce/    # 星力标签页
│   │   │   ├── Potential/    # 潜能标签页
│   │   │   └── Bonusstat/    # 额外属性标签页
│   │   ├── Equip/            # 装备相关资源
│   │   │   ├── Slot/         # 装备槽背景
│   │   │   ├── Badge/        # 星级徽章
│   │   │   ├── Effect/       # 强化特效
│   │   │   └── Notice/       # 通知栏
│   │   ├── Info/             # 信息面板
│   │   ├── Cost/             # 费用显示
│   │   └── button_*/         # 各种按钮资源
│   └── Common/               # 通用资源
│       ├── dialog/           # 对话框资源
│       └── miniGame/         # 迷你游戏资源
└── 🎒 itempic/               # 装备图片
    ├── 1001164.png           # 装备图片 (根据itemId)
    ├── 1072952.png
    └── ...
```

## 🧩 核心组件

### EquipEnhanceSimulator 类

主要的模拟器类，负责整个应用的状态管理和业务逻辑。

```javascript
class EquipEnhanceSimulator {
    constructor() {
        // 游戏状态
        this.currentTab = 'starforce';
        this.equipLevel = 0;
        this.equipMaxLevel = 25;
        this.enhancing = false;
        this.minigameActive = false;
        this.autoMode = false;
        
        // 当前装备信息
        this.currentEquip = {
            itemId: null,
            name: '',
            type: '',
            category: ''
        };
        
        // 装备数据库
        this.itemDatabase = null;
        
        // 强化概率数据
        this.starforceProbabilities = { /* ... */ };
        
        // 强化费用
        this.enhanceCosts = { /* ... */ };
    }
}
```

### 主要方法分类

#### 1. 初始化方法
- `init()`: 应用初始化
- `loadItemDatabase()`: 加载装备数据库
- `bindEvents()`: 绑定事件监听器
- `preloadImages()`: 预加载图片资源

#### 2. UI更新方法
- `updateUI()`: 更新整体界面
- `updateProbabilities()`: 更新概率显示
- `updateCosts()`: 更新费用显示
- `updateEquipBadge()`: 更新星级徽章
- `updateStarProgress()`: 更新进度显示
- `updateStats()`: 更新属性显示
- `updateNotice()`: 更新通知信息

#### 3. 装备管理方法
- `selectEquip()`: 随机选择装备
- `setEquip(itemId, name, type)`: 设置装备
- `clearEquip()`: 清除装备
- `getItemInfo(itemId)`: 获取装备信息
- `getItemTypeName(category)`: 获取装备类型名称

#### 4. 强化逻辑方法
- `startEnhance()`: 开始强化
- `confirmEnhance()`: 确认强化
- `performEnhance(bonus)`: 执行强化
- `calculateEnhanceResult(bonus)`: 计算强化结果
- `cancelEnhance()`: 取消强化

#### 5. 迷你游戏方法
- `startMinigame()`: 启动迷你游戏
- `animateMinigame()`: 迷你游戏动画
- `stopMinigame()`: 停止迷你游戏
- `calculateMinigameBonus(width)`: 计算迷你游戏奖励

#### 6. 特效动画方法
- `playStandbyEffect()`: 播放待机特效
- `playProgressEffect()`: 播放强化进行特效
- `playResultEffect(resultType)`: 播放结果特效
- `animateEffect(loop)`: 动画播放控制
- `stopAllEffects()`: 停止所有特效

#### 7. 标签页管理方法
- `switchTab(tab)`: 切换标签页
- `bindLayerControlEvents()`: 绑定分层控制事件
- `toggleLayer(layerNumber, visible)`: 切换层级显示

## 📐 开发规范

### 代码风格
```javascript
// 1. 使用驼峰命名法
const equipLevel = 0;
const currentEquip = {};

// 2. 常量使用大写字母
const MAX_STAR_LEVEL = 25;
const ENHANCE_COSTS = {};

// 3. 类名使用帕斯卡命名法
class EquipEnhanceSimulator {}

// 4. 私有方法使用下划线前缀
_privateMethod() {}

// 5. 事件处理器命名规范
handleButtonClick() {}
onEquipChange() {}
```

### CSS规范
```css
/* 1. 使用BEM命名规范 */
.equip-slot-background {}
.equip-slot-background--active {}
.equip-slot-background__icon {}

/* 2. 分层注释 */
/* Z-0: 背景层 */
/* Z-1: 标签页层 */
/* Z-2: 装备槽层 */

/* 3. 响应式设计 */
@media (max-width: 600px) {
    .ui-container {
        transform: scale(0.8);
    }
}
```

### 文件组织
```
styles.css 结构:
├── 全局重置和基础样式
├── 分层样式 (Z-0 到 Z-1000)
├── 交互状态样式
├── 动画和特效
├── 响应式设计
└── 调试工具样式
```

## 🔌 API接口

### 装备数据库接口

#### loadItemDatabase()
```javascript
async loadItemDatabase() {
    try {
        const response = await fetch('itemList.json');
        const data = await response.json();
        this.itemDatabase = {};
        
        data.orderBook.forEach(item => {
            this.itemDatabase[item.itemId] = {
                name: item.itemName,
                category: item.itemCategory,
                imageUrl: item.imageUrl
            };
        });
    } catch (error) {
        console.error('加载装备数据库失败:', error);
    }
}
```

#### getItemInfo(itemId)
```javascript
getItemInfo(itemId) {
    if (!this.itemDatabase) return null;
    return this.itemDatabase[parseInt(itemId)];
}
```

### 强化概率接口

#### starforceProbabilities 数据结构
```javascript
{
    0: { success: 95, failure: 5, major_failure: 0, failure_drop: 0 },
    1: { success: 90, failure: 10, major_failure: 0, failure_drop: 0 },
    // ... 更多等级
    24: { success: 1, failure: 59.4, major_failure: 39.6, failure_drop: 0 }
}
```

## 🎨 UI布局系统

### Z-Index分层系统
```
Z-1000: 弹出对话框 (popup, result)
Z-50:   迷你游戏覆盖层
Z-10:   底部UI (按钮, 费用显示)
Z-8:    装备物品和进度显示
Z-7:    概率信息和属性变化
Z-6:    信息面板背景
Z-5:    通知框
Z-4:    强化特效背景
Z-3:    装备徽章
Z-2:    装备槽背景
Z-1:    标签页和顶部按钮
Z-0:    主背景
```

### 坐标系统
基于XML配置的绝对定位系统：
```xml
<vector name="EquipSlot" x="209" y="168" />
<vector name="Equip" x="206" y="167" />
<vector name="EquipSlotBadge" x="206" y="165" />
```

对应CSS:
```css
.equip-slot-background {
    position: absolute;
    top: 168px;
    left: 209px;
    width: 90px;
    height: 90px;
}
```

### 响应式适配
```css
/* 平板设备 */
@media (max-width: 600px) {
    .ui-container {
        transform: scale(0.8);
        transform-origin: center;
    }
}

/* 手机设备 */
@media (max-width: 480px) {
    .ui-container {
        transform: scale(0.6);
        transform-origin: center;
    }
}
```

## 🎭 动画系统

### 特效动画
基于原版游戏的帧动画系统：

```javascript
// 动画类型
this.currentEffectType = 'standby'; // standby, progress, success, failed

// 最大帧数
this.maxFrames = {
    standby: 16,
    progress: 16,
    success: 18,
    failed: 16
};

// 动画播放
animateEffect(loop = true) {
    const frameInterval = 90; // 90ms/帧 (按照XML定义)
    
    // 使用requestAnimationFrame确保稳定帧率
    this.effectAnimationTimer = requestAnimationFrame(() => {
        this.animateEffect(loop);
    });
}
```

### CSS动画类
```css
/* 特效动画帧 */
.effect-standby-0 { background-image: url('...Standby/0.png'); }
.effect-standby-1 { background-image: url('...Standby/1.png'); }
/* ... */

/* 交互动画 */
.enhance-success {
    animation: successPulse 0.6s ease-in-out;
}

.enhance-fail {
    animation: failShake 0.6s ease-in-out;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.05); filter: brightness(1.3); }
}
```

## 💾 数据管理

### 装备数据结构
```javascript
// 当前装备
this.currentEquip = {
    itemId: null,        // 装备ID
    name: '',           // 装备名称
    type: '',           // 装备类型
    category: ''        // 装备分类
};

// 测试装备列表
this.testEquipItems = [
    { id: '1001164', name: '加载中...', type: 'hat' },
    { id: '1072952', name: '加载中...', type: 'shoes' },
    // ...
];
```

### 装备数据库格式
```json
{
  "orderBook": [
    {
      "itemId": 1001164,
      "itemName": "战士帽子",
      "itemCategory": 1000201001,
      "imageUrl": "itempic/1001164.png"
    }
  ]
}
```

### 装备类型映射
```javascript
getItemTypeName(category) {
    const categoryMap = {
        1000201001: '战士帽子', 1000301001: '普通帽子',
        1000201002: '战士上衣', 1000301002: '普通上衣',
        1000201003: '战士下装', 1000301003: '普通下装',
        // ...
    };
    return categoryMap[category] || '未知类型';
}
```

## 🐛 调试工具

### 分层控制面板
```javascript
// 显示/隐藏特定层级
toggleLayer(layerNumber, visible) {
    const elements = document.querySelectorAll(`.layer-z-${layerNumber}`);
    elements.forEach(element => {
        if (visible) {
            element.classList.remove('layer-hidden');
        } else {
            element.classList.add('layer-hidden');
        }
    });
}

// 显示所有层级
showAllLayers() {
    const layerCheckboxes = document.querySelectorAll('.layer-controls input[type="checkbox"]');
    layerCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
        const layerNumber = checkbox.id.replace('layer-', '');
        this.toggleLayer(layerNumber, true);
    });
}
```

### 控制台调试信息
```javascript
// 装备加载调试
console.log(`装备装载成功: ${itemInfo.name} [${this.getItemTypeName(itemInfo.category)}] (ID: ${itemId})`);

// 图片加载调试
console.log(`装备图片加载成功: itempic/${itemId}.png`);
console.log('图片尺寸:', imgElement.naturalWidth, 'x', imgElement.naturalHeight);

// 特效调试
console.log(`Z-${layerNumber}层 ${visible ? '显示' : '隐藏'}, 影响${elements.length}个元素`);
```

### 错误处理
```javascript
// 图片加载失败处理
imgElement.onerror = () => {
    console.error(`装备图片加载失败: itempic/${itemId}.png`);
    imgElement.remove();
    equipItem.innerHTML = '?';
};

// 数据库加载失败处理
catch (error) {
    console.error('加载装备数据库失败:', error);
}
```

## ⚡ 性能优化

### 图片预加载
```javascript
preloadImages() {
    const imagesToPreload = [
        'UIEquipEnchant.img/Main/Background.png',
        'UIEquipEnchant.img/Main/Equip/Slot/Starforce.png',
        // ...
    ];

    // 预加载所有特效动画帧
    for (let i = 0; i < 16; i++) {
        imagesToPreload.push(`UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/${i}.png`);
    }

    imagesToPreload.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}
```

### 动画优化
```javascript
// 使用requestAnimationFrame确保稳定帧率
animateEffect(loop = true) {
    if (!this.enhancing && this.currentEffectType !== 'standby') return;
    
    const frameInterval = 90; // 控制帧率
    
    const nextFrame = () => {
        const currentTime = performance.now();
        const elapsed = currentTime - this.lastFrameTime;
        
        if (elapsed >= frameInterval) {
            this.lastFrameTime = currentTime;
            this.animateEffect(loop);
        } else {
            this.effectAnimationTimer = requestAnimationFrame(nextFrame);
        }
    };
    
    this.effectAnimationTimer = requestAnimationFrame(nextFrame);
}
```

### 内存管理
```javascript
// 清理定时器
stopAllEffects() {
    if (this.effectAnimationTimer) {
        cancelAnimationFrame(this.effectAnimationTimer);
        this.effectAnimationTimer = null;
    }
    this.lastFrameTime = null;
}

// 图片资源清理
imgElement.onload = () => {
    // 处理完成后及时清理引用
};
```

## 🧪 测试指南

### 功能测试清单

#### 基础功能测试
- [ ] 页面加载正常
- [ ] 装备数据库加载成功
- [ ] UI资源加载完整
- [ ] 分层控制面板工作正常

#### 装备管理测试
- [ ] 点击装备槽随机选择装备
- [ ] 右键点击装备槽清除装备
- [ ] E键随机选择装备
- [ ] R键清除装备
- [ ] 装备图片正确显示和居中
- [ ] 装备信息正确显示

#### 强化功能测试
- [ ] 星力强化概率计算正确
- [ ] 潜能重设功能正常
- [ ] 额外属性强化功能正常
- [ ] 镇护选项影响概率
- [ ] 强化结果正确处理

#### 特效动画测试
- [ ] 待机特效循环播放
- [ ] 强化进行特效正常
- [ ] 成功/失败特效正确
- [ ] 特效切换流畅

#### 迷你游戏测试
- [ ] 10星以上触发迷你游戏
- [ ] 游戏界面正确显示
- [ ] 指针动画流畅
- [ ] 停止功能正常
- [ ] 奖励计算正确

### 浏览器兼容性测试
- [ ] Chrome 80+
- [ ] Firefox 75+
- [ ] Safari 13+
- [ ] Edge 80+

### 响应式测试
- [ ] 桌面端 (1920x1080)
- [ ] 平板端 (768x1024)
- [ ] 手机端 (375x667)

### 性能测试
- [ ] 页面加载时间 < 3秒
- [ ] 动画帧率稳定 60fps
- [ ] 内存占用合理
- [ ] CPU使用率正常

## 🚀 部署说明

### 本地开发环境
```bash
# 1. 克隆项目
git clone [repository-url]
cd 强化模拟器

# 2. 启动本地服务器
python -m http.server 8000
# 或
npx http-server
# 或使用VS Code Live Server插件

# 3. 访问应用
# 浏览器访问 http://localhost:8000
```

### 生产环境部署

#### 静态文件服务器 (推荐)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/强化模拟器;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 缓存静态资源
    location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 缓存CSS和JS
    location ~* \.(css|js)$ {
        expires 1w;
        add_header Cache-Control "public";
    }
}
```

#### GitHub Pages
```yaml
# .github/workflows/deploy.yml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./
```

### 部署检查清单
- [ ] 所有资源文件路径正确
- [ ] CORS问题已解决
- [ ] 图片资源完整
- [ ] JSON数据文件可访问
- [ ] HTTPS证书配置（如需要）
- [ ] 性能监控配置
- [ ] 错误日志收集

## 📝 开发日志

### 版本历史

#### v1.0.0 (当前版本)
- ✅ 完成核心强化系统
- ✅ 实现装备图片显示功能
- ✅ 添加分层UI调试工具
- ✅ 完善特效动画系统
- ✅ 集成装备数据库
- ✅ 实现迷你游戏功能

### 已知问题
1. **图片加载优化**: 部分装备图片可能不存在
2. **动画性能**: 在低端设备上可能有轻微卡顿
3. **浏览器兼容**: IE不支持，需要现代浏览器

### 未来规划
- [ ] 添加音效系统
- [ ] 实现装备属性预览
- [ ] 增加强化历史记录
- [ ] 支持自定义强化概率
- [ ] 多语言支持
- [ ] 移动端手势支持

---

**开发团队**: MSU模拟器开发组  
**文档版本**: v1.0.0  
**最后更新**: 2024年12月  

如有技术问题，请查阅本文档或提交Issue到项目仓库。 