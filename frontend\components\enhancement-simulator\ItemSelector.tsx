'use client'

import { useState, useMemo } from 'react'
import { ItemSelectorProps, ItemListEntry, EnhancementType } from '@/types/enhancement'
import ItemTooltip from './ItemTooltip'
import {
  filterItemsByEnhancementType,
  sortItemsByLevel,
  getEnhancementTypeDisplayName,
  getItemLevel
} from '@/lib/item-filter-utils'

interface ExtendedItemSelectorProps extends ItemSelectorProps {
  currentEnhancementType: EnhancementType
}

export default function ItemSelector({
  isVisible,
  items,
  selectedCategory,
  onItemSelect,
  onCategoryChange,
  onToggleVisibility,
  currentEnhancementType
}: ExtendedItemSelectorProps) {
  const [hoveredItem, setHoveredItem] = useState<ItemListEntry | null>(null)
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
  const [searchTerm, setSearchTerm] = useState('')

  // 筛选物品
  const filteredItems = useMemo(() => {
    let filtered = items

    // 首先按强化类型筛选
    filtered = filterItemsByEnhancementType(filtered, currentEnhancementType)

    // 按类别筛选
    if (selectedCategory !== null) {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    // 按搜索词筛选
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // 按等级排序
    return sortItemsByLevel(filtered)
  }, [items, selectedCategory, searchTerm, currentEnhancementType])

  // 获取装备类别列表（基于当前强化类型）
  const categories = useMemo(() => {
    const categoryMap = new Map<number, string>()
    const typeFilteredItems = filterItemsByEnhancementType(items, currentEnhancementType)

    typeFilteredItems.forEach(item => {
      const category = item.category
      const label = item.detailInfo?.metadata?.category?.tier3?.label || '未知类型'
      categoryMap.set(category, label)
    })
    return Array.from(categoryMap.entries()).sort((a, b) => a[1].localeCompare(b[1]))
  }, [items, currentEnhancementType])

  const handleItemHover = (item: ItemListEntry, event: React.MouseEvent) => {
    setHoveredItem(item)
    const rect = event.currentTarget.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const tooltipWidth = 280
    const tooltipHeight = 400

    let x = rect.right + 10
    let y = rect.top

    // 如果工具提示会超出右边界，显示在左侧
    if (x + tooltipWidth > viewportWidth) {
      x = rect.left - tooltipWidth - 10
    }

    // 如果工具提示会超出下边界，向上调整
    if (y + tooltipHeight > viewportHeight) {
      y = viewportHeight - tooltipHeight - 10
    }

    // 确保不会超出上边界
    if (y < 10) {
      y = 10
    }

    setTooltipPosition({ x, y })
  }

  const handleItemLeave = () => {
    setHoveredItem(null)
  }

  if (!isVisible) return null

  return (
    <>
      {/* 物品选择器面板 */}
      <div
        className="absolute bg-black/95 border border-blue-400/30 rounded-lg shadow-2xl z-[1] flex flex-col"
        style={{
          left: '-280px',
          top: '0px',
          width: '260px',
          height: '712px',
        }}
      >
        {/* 头部 - 固定高度 */}
        <div className="flex-shrink-0 p-4 border-b border-blue-400/20 bg-blue-900/30 rounded-t-lg">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-white text-base font-bold">装备选择器</h3>
            <button
              onClick={onToggleVisibility}
              className="text-gray-400 hover:text-white text-lg"
            >
              ×
            </button>
          </div>
          <p className="text-blue-300 text-xs italic">
            {getEnhancementTypeDisplayName(currentEnhancementType)}
          </p>
        </div>

        {/* 搜索和筛选 - 固定高度 */}
        <div className="flex-shrink-0 p-3 border-b border-blue-400/20">
          {/* 搜索框 */}
          <input
            type="text"
            placeholder="搜索装备名称..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm mb-3 focus:border-blue-400 focus:outline-none"
          />

          {/* 类别筛选 */}
          <select
            value={selectedCategory || ''}
            onChange={(e) => onCategoryChange(e.target.value ? Number(e.target.value) : null)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm focus:border-blue-400 focus:outline-none"
          >
            <option value="">所有类型</option>
            {categories.map(([categoryId, label]) => (
              <option key={categoryId} value={categoryId}>
                {label}
              </option>
            ))}
          </select>
        </div>

        {/* 装备网格容器 - 可滚动区域 */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full p-2 overflow-y-auto">
            <div className="grid grid-cols-5 gap-2">
              {filteredItems.map((item) => {
                const level = getItemLevel(item)
                return (
                  <div
                    key={item.itemId}
                    className="relative w-10 h-10 bg-gray-800 border border-gray-600 rounded cursor-pointer hover:border-blue-400 hover:bg-blue-900/30 transition-all duration-200"
                    onClick={() => onItemSelect(item)}
                    onMouseEnter={(e) => handleItemHover(item, e)}
                    onMouseLeave={handleItemLeave}
                  >
                    <img
                      src={item.imageUrl}
                      alt={item.name}
                      className="w-full h-full object-contain rounded"
                      onError={(e) => {
                        e.currentTarget.src = '/images/placeholder-item.png'
                      }}
                    />
                    {/* 等级徽章 - 缩小约25% */}
                    {level > 0 && (
                      <div className="absolute -bottom-0.5 -right-0.5 bg-red-600 text-white font-bold rounded-sm min-w-2 text-center leading-none"
                           style={{
                             fontSize: '9px',
                             padding: '1px 2px',
                           }}>
                        {level}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>

            {/* 无结果提示 */}
            {filteredItems.length === 0 && (
              <div className="text-center text-gray-400 mt-8">
                <p>未找到匹配的装备</p>
                <p className="text-sm mt-1">请尝试调整搜索条件</p>
              </div>
            )}
          </div>
        </div>

        {/* 底部信息栏 - 固定高度 */}
        <div className="flex-shrink-0 p-2 bg-gray-900/50 rounded-b-lg border-t border-blue-400/20">
          <div className="text-xs text-gray-400 text-center">
            共 {filteredItems.length} 个装备
          </div>
        </div>
      </div>

      {/* 工具提示 */}
      <ItemTooltip
        item={hoveredItem}
        isVisible={!!hoveredItem}
        position={tooltipPosition}
      />
    </>
  )
}
