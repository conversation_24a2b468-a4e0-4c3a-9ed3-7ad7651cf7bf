import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export default function ToolsPage() {
  const toolCategories = [
    {
      title: "角色工具",
      tools: [
        { name: "纸娃娃模拟器", description: "角色形象搭配工具", icon: "👗", href: "/paperdoll", popular: true },
        { name: "技能模拟器", description: "技能加点模拟", icon: "⚡", href: "/skills" },
        { name: "联盟等级计算器", description: "联盟角色等级计算", icon: "🏆", href: "/union" },
      ],
    },
    {
      title: "装备工具",
      tools: [
        { name: "星之力强化模拟", description: "装备强化成功率模拟", icon: "⭐", href: "/starforce", popular: true },
        { name: "装备强化模拟器", description: "专业的装备强化模拟器，支持星之力、潜能、额外属性", icon: "✨", href: "/tools/enhancement", popular: true },
        { name: "洗点方块模拟器", description: "潜能方块模拟", icon: "🎲", href: "/cubes" },
        { name: "装备搭配推荐", description: "AI智能装备推荐", icon: "🤖", href: "/equipment-recommend" },
      ],
    },
    {
      title: "数据工具",
      tools: [
        { name: "爆率查询工具", description: "怪物掉落概率查询", icon: "💎", href: "/drop-rates", popular: true },
        { name: "伤害计算器", description: "技能伤害计算", icon: "🔥", href: "/damage-calculator" },
        { name: "经验值计算器", description: "升级经验计算", icon: "📈", href: "/exp-calculator" },
      ],
    },
  ]

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">实用工具</h1>
        <Badge variant="secondary">11 个工具</Badge>
      </div>

      {toolCategories.map((category, categoryIndex) => (
        <section key={categoryIndex}>
          <h2 className="text-2xl font-semibold mb-6">{category.title}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {category.tools.map((tool, toolIndex) => (
              <Card
                key={toolIndex}
                className="group hover:shadow-lg transition-all duration-300 bg-white/80 backdrop-blur-sm border-gray-200"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="text-3xl">{tool.icon}</div>
                    {tool.popular && <Badge className="bg-red-100 text-red-800">热门</Badge>}
                  </div>
                </CardHeader>
                <CardContent>
                  <h3 className="font-semibold text-lg mb-2 group-hover:text-blue-600 transition-colors">
                    {tool.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">{tool.description}</p>
                  <Link href={tool.href}>
                    <Button className="w-full group-hover:bg-blue-600 transition-colors">立即使用</Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      ))}

      {/* Coming Soon */}
      <section>
        <h2 className="text-2xl font-semibold mb-6">即将推出</h2>
        <Card className="bg-gradient-to-r from-gray-100 to-gray-200 border-dashed border-2 border-gray-300">
          <CardContent className="p-8 text-center">
            <div className="text-4xl mb-4">🚀</div>
            <h3 className="text-xl font-semibold mb-2">更多工具正在开发中</h3>
            <p className="text-gray-600 mb-4">我们正在开发更多实用工具，包括公会管理、交易助手等功能</p>
            <Button variant="outline">提交建议</Button>
          </CardContent>
        </Card>
      </section>
    </div>
  )
}
