"""
User schemas
"""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime

class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: Optional[EmailStr] = None
    is_active: bool = True
    is_verified: bool = False

class UserCreate(BaseModel):
    """创建用户模式"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: str
    fingerprint_id: Optional[str] = None

class UserUpdate(BaseModel):
    """更新用户模式"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None

class UserResponse(BaseModel):
    """用户响应模式"""
    id: int
    username: str
    email: Optional[str] = None
    is_active: bool
    is_verified: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    fingerprint_id: Optional[str] = None
    
    class Config:
        from_attributes = True

class UserProfile(BaseModel):
    """用户资料模式"""
    id: int
    username: str
    email: Optional[str] = None
    is_active: bool
    is_verified: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    membership_level: int
    membership_name: str
    currency_balance: float = 0.0
    
    class Config:
        from_attributes = True

class UserMembershipResponse(BaseModel):
    """用户会员信息响应"""
    tier_name: str
    tier_display_name: str
    tier_level: int
    started_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool
    permissions: dict
    
    class Config:
        from_attributes = True
