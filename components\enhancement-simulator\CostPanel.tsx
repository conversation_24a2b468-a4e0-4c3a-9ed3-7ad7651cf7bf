'use client'

import { formatNumber } from '@/lib/enhancement-utils'

interface CostPanelProps {
  cost: number
}

export default function CostPanel({ cost }: CostPanelProps) {
  return (
    <>
      {/* Z-10: 费用背景 */}
      <div
        className="absolute z-[10]"
        style={{
          top: '532px',
          left: '40px',
          width: '457px',
          height: '80px',
        }}
      />
      
      {/* Z-10: 费用内容 */}
      <div
        className="absolute z-[10] p-[10px_15px]"
        style={{
          top: '532px',
          left: '40px',
          width: '457px',
          height: '80px',
        }}
      >
        {/* 第一行：Power Crystals */}
        <div className="flex justify-between items-center py-2 px-[15px] mb-[5px] rounded">
          <div className="flex items-center gap-[10px]">
            <div className="w-4 h-4 rounded-sm bg-gradient-to-br from-[#ffd700] to-[#ffb700] border border-[#cc8800]" />
            <span className="text-white text-[11px]">需要Power Crystals</span>
          </div>
          <div className="text-[#ffd700] text-[12px] font-bold">
            {formatNumber(cost)}
          </div>
          <div className="flex gap-[5px]">
            <button className="px-3 py-1 text-[10px] bg-gradient-to-br from-[#4a5568] to-[#2d3748] text-white border border-[#718096] rounded-sm cursor-pointer transition-all duration-200 hover:bg-gradient-to-br hover:from-[#5a6578] hover:to-[#3d4758] hover:-translate-y-px">
              充值
            </button>
          </div>
        </div>
        
        {/* 第二行：星力能量 */}
        <div className="flex justify-between items-center py-2 px-[15px] mb-[5px] rounded">
          <div className="flex items-center gap-[10px]">
            <div className="w-4 h-4 rounded-sm bg-gradient-to-br from-[#4299e1] to-[#3182ce] border border-[#2c5aa0]" />
            <span className="text-white text-[11px]">星力能量</span>
          </div>
          <div className="text-[#ffd700] text-[12px] font-bold">
            1,967,768,508
          </div>
          <div className="flex gap-[5px]">
            <button className="px-3 py-1 text-[10px] bg-gradient-to-br from-[#4a5568] to-[#2d3748] text-white border border-[#718096] rounded-sm cursor-pointer transition-all duration-200 hover:bg-gradient-to-br hover:from-[#5a6578] hover:to-[#3d4758] hover:-translate-y-px">
              购买
            </button>
            <button className="px-3 py-1 text-[10px] bg-gradient-to-br from-[#4a5568] to-[#2d3748] text-white border border-[#718096] rounded-sm cursor-pointer transition-all duration-200 hover:bg-gradient-to-br hover:from-[#5a6578] hover:to-[#3d4758] hover:-translate-y-px">
              自动购买
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
