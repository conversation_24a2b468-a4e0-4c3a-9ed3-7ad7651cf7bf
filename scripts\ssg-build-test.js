const { execSync } = require("child_process")
const fs = require("fs")
const path = require("path")

console.log("🚀 Testing SSG build with dynamic routes...")

try {
  // 确保必要目录存在
  const cmsDir = path.join(process.cwd(), "public/images/cms-216")
  if (!fs.existsSync(cmsDir)) {
    fs.mkdirSync(cmsDir, { recursive: true })
    console.log("✅ Created cms-216 directory")
  }

  // 运行构建
  console.log("🔨 Running npm run build...")
  const result = execSync("npm run build", {
    encoding: "utf8",
    stdio: "pipe",
    timeout: 180000, // 3分钟超时
  })

  console.log("🎉 BUILD SUCCESSFUL!")
  console.log("📊 Build output:")
  console.log(result)

  // 检查生成的静态文件
  const outDir = path.join(process.cwd(), "out")
  if (fs.existsSync(outDir)) {
    console.log("✅ Static export directory created")

    // 检查动态路由是否正确生成
    const itemsDir = path.join(outDir, "items")
    if (fs.existsSync(itemsDir)) {
      console.log("✅ Dynamic routes generated")

      // 列出生成的道具页面
      const itemPages = fs.readdirSync(itemsDir)
      console.log(`📄 Generated ${itemPages.length} item pages:`)
      itemPages.slice(0, 5).forEach((page) => {
        console.log(`   - ${page}`)
      })
      if (itemPages.length > 5) {
        console.log(`   ... and ${itemPages.length - 5} more`)
      }
    }

    // 检查API路由
    const apiDir = path.join(outDir, "api")
    if (fs.existsSync(apiDir)) {
      console.log("⚠️  API routes found in static export (this might be an issue)")
    } else {
      console.log("✅ No API routes in static export (correct for SSG)")
    }
  }

  console.log("\n🎯 SSG Build Summary:")
  console.log("✅ Dynamic routes with generateStaticParams working")
  console.log("✅ Static pages generated for SEO")
  console.log("✅ CDN-friendly static files created")
} catch (error) {
  console.log("❌ BUILD FAILED!")
  console.log("Error details:")
  console.log("STDERR:", error.stderr)
  console.log("STDOUT:", error.stdout)

  const errorOutput = (error.stderr || "") + (error.stdout || "")

  if (errorOutput.includes("generateStaticParams")) {
    console.log("\n🔍 generateStaticParams issue detected")
  }

  if (errorOutput.includes("Dynamic server usage")) {
    console.log("\n🔍 Dynamic server usage detected - need to make routes static")
  }

  if (errorOutput.includes("Module not found")) {
    console.log("\n🔍 Missing dependencies")
  }
}
