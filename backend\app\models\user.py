"""
User model
"""

from sqlalchemy import Column, Inte<PERSON>, <PERSON>, Bo<PERSON>an, DateTime, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=True, index=True)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    fingerprint_id = Column(String(255), nullable=True, index=True)
    
    # Relationships
    memberships = relationship("UserMembership", back_populates="user")
    currency_account = relationship("CurrencyAccount", back_populates="user", uselist=False)
    transactions = relationship("Transaction", back_populates="user")
    sessions = relationship("UserSession", back_populates="user")
    email_verifications = relationship("EmailVerification", back_populates="user")
    password_resets = relationship("PasswordReset", back_populates="user")
    
    # Indexes
    __table_args__ = (
        Index('idx_user_username', 'username'),
        Index('idx_user_email', 'email'),
        Index('idx_user_fingerprint', 'fingerprint_id'),
        Index('idx_user_active', 'is_active'),
    )
