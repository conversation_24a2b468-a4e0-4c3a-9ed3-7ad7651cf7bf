"""
Authentication schemas
"""

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
from datetime import datetime

class UserRegisterRequest(BaseModel):
    """用户注册请求"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    fingerprint_id: Optional[str] = Field(None, description="设备指纹ID")
    
    @validator('username', 'email')
    def validate_username_or_email(cls, v, values):
        """验证用户名或邮箱至少提供一个"""
        if 'username' in values and not values.get('username') and not v:
            raise ValueError('用户名或邮箱至少需要提供一个')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        """密码强度验证"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        if not any(c.isalpha() for c in v):
            raise ValueError('密码必须包含至少一个字母')
        return v

class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: Optional[str] = Field(None, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    password: str = Field(..., description="密码")
    fingerprint_id: Optional[str] = Field(None, description="设备指纹ID")
    
    @validator('username', 'email')
    def validate_login_identifier(cls, v, values):
        """验证登录标识符"""
        if 'username' in values and not values.get('username') and not v:
            raise ValueError('用户名或邮箱至少需要提供一个')
        return v

class TokenResponse(BaseModel):
    """令牌响应"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: "UserResponse"

class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str

class EmailVerificationRequest(BaseModel):
    """邮箱验证请求"""
    token: str

class ResendVerificationRequest(BaseModel):
    """重新发送验证邮件请求"""
    email: EmailStr

class PasswordResetRequest(BaseModel):
    """密码重置请求"""
    email: EmailStr

class PasswordResetConfirmRequest(BaseModel):
    """确认密码重置请求"""
    token: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @validator('new_password')
    def validate_password(cls, v):
        """密码强度验证"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        if not any(c.isalpha() for c in v):
            raise ValueError('密码必须包含至少一个字母')
        return v

class LogoutRequest(BaseModel):
    """登出请求"""
    refresh_token: Optional[str] = None

# 导入用户响应模式（避免循环导入）
from app.schemas.user import UserResponse
TokenResponse.model_rebuild()
