"""
Email service using Resend API
"""

import httpx
import logging
from typing import Optional
from app.core.config import settings

logger = logging.getLogger(__name__)

class EmailService:
    """邮件服务类"""
    
    def __init__(self):
        self.api_key = settings.RESEND_API_KEY
        self.from_email = settings.FROM_EMAIL
        self.from_name = settings.FROM_NAME
        self.base_url = "https://api.resend.com"
    
    async def send_verification_email(
        self, 
        to_email: str, 
        username: str, 
        verification_token: str
    ) -> bool:
        """发送邮箱验证邮件"""
        try:
            verification_url = f"{settings.FRONTEND_URL}/auth/verify-email?token={verification_token}"
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>邮箱验证 - 冒险岛情报站</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2563eb;">欢迎加入冒险岛情报站！</h2>
                    <p>亲爱的 {username}，</p>
                    <p>感谢您注册冒险岛情报站！请点击下面的链接验证您的邮箱地址：</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{verification_url}" 
                           style="background-color: #2563eb; color: white; padding: 12px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;">
                            验证邮箱
                        </a>
                    </div>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; color: #666;">{verification_url}</p>
                    <p style="color: #666; font-size: 14px;">
                        此链接将在24小时后失效。如果您没有注册账户，请忽略此邮件。
                    </p>
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #999; font-size: 12px;">
                        冒险岛情报站 - 您的装备强化专家<br>
                        此邮件由系统自动发送，请勿回复。
                    </p>
                </div>
            </body>
            </html>
            """
            
            return await self._send_email(
                to_email=to_email,
                subject="邮箱验证 - 冒险岛情报站",
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Failed to send verification email to {to_email}: {e}")
            return False
    
    async def send_password_reset_email(
        self, 
        to_email: str, 
        username: str, 
        reset_token: str
    ) -> bool:
        """发送密码重置邮件"""
        try:
            reset_url = f"{settings.FRONTEND_URL}/auth/reset-password?token={reset_token}"
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>密码重置 - 冒险岛情报站</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #dc2626;">密码重置请求</h2>
                    <p>亲爱的 {username}，</p>
                    <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{reset_url}" 
                           style="background-color: #dc2626; color: white; padding: 12px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;">
                            重置密码
                        </a>
                    </div>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; color: #666;">{reset_url}</p>
                    <p style="color: #666; font-size: 14px;">
                        此链接将在1小时后失效。如果您没有请求重置密码，请忽略此邮件。
                    </p>
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    <p style="color: #999; font-size: 12px;">
                        冒险岛情报站 - 您的装备强化专家<br>
                        此邮件由系统自动发送，请勿回复。
                    </p>
                </div>
            </body>
            </html>
            """
            
            return await self._send_email(
                to_email=to_email,
                subject="密码重置 - 冒险岛情报站",
                html_content=html_content
            )
            
        except Exception as e:
            logger.error(f"Failed to send password reset email to {to_email}: {e}")
            return False
    
    async def _send_email(
        self, 
        to_email: str, 
        subject: str, 
        html_content: str
    ) -> bool:
        """发送邮件的内部方法"""
        if not self.api_key:
            logger.warning("Resend API key not configured, email not sent")
            # 在开发环境中，打印邮件内容到控制台
            if settings.DEBUG:
                logger.info(f"=== EMAIL DEBUG ===")
                logger.info(f"To: {to_email}")
                logger.info(f"Subject: {subject}")
                logger.info(f"Content: {html_content}")
                logger.info(f"=== END EMAIL DEBUG ===")
            return True
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/emails",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "from": f"{self.from_name} <{self.from_email}>",
                        "to": [to_email],
                        "subject": subject,
                        "html": html_content
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    logger.info(f"Email sent successfully to {to_email}")
                    return True
                else:
                    logger.error(f"Failed to send email: {response.status_code} - {response.text}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            return False

# 全局邮件服务实例
email_service = EmailService()
