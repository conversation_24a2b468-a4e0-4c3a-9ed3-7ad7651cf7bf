"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"

export default function StarforcePage() {
  const [currentStars, setCurrentStars] = useState(0)
  const [targetStars, setTargetStars] = useState(1)
  const [itemLevel, setItemLevel] = useState(160)
  const [safeguard, setSafeguard] = useState(false)
  const [mvpDiscount, setMvpDiscount] = useState(false)
  const [eventDiscount, setEventDiscount] = useState(false)
  const [simulationResults, setSimulationResults] = useState<any>(null)

  const starforceData = {
    0: { success: 95, fail: 5, destroy: 0, cost: 1000 },
    1: { success: 90, fail: 10, destroy: 0, cost: 1800 },
    2: { success: 85, fail: 15, destroy: 0, cost: 2700 },
    3: { success: 85, fail: 15, destroy: 0, cost: 3600 },
    4: { success: 85, fail: 15, destroy: 0, cost: 4500 },
    5: { success: 80, fail: 20, destroy: 0, cost: 5400 },
    6: { success: 75, fail: 25, destroy: 0, cost: 6300 },
    7: { success: 70, fail: 30, destroy: 0, cost: 7200 },
    8: { success: 65, fail: 35, destroy: 0, cost: 8100 },
    9: { success: 60, fail: 40, destroy: 0, cost: 9000 },
    10: { success: 55, fail: 42.5, destroy: 2.5, cost: 9900 },
    11: { success: 50, fail: 45, destroy: 5, cost: 10800 },
    12: { success: 45, fail: 46.5, destroy: 8.5, cost: 11700 },
    13: { success: 40, fail: 48, destroy: 12, cost: 12600 },
    14: { success: 35, fail: 49.5, destroy: 15.5, cost: 13500 },
    15: { success: 30, fail: 67.9, destroy: 2.1, cost: 22500 },
  }

  const runSimulation = () => {
    let attempts = 0
    let totalCost = 0
    let stars = currentStars
    const maxAttempts = 10000

    while (stars < targetStars && attempts < maxAttempts) {
      attempts++
      const data = starforceData[stars as keyof typeof starforceData]
      if (!data) break

      let cost = data.cost
      if (mvpDiscount) cost *= 0.7
      if (eventDiscount) cost *= 0.7
      if (safeguard && stars >= 12 && stars <= 16) cost *= 2

      totalCost += cost

      const random = Math.random() * 100
      if (random <= data.success) {
        stars++
      } else if (random <= data.success + data.fail) {
        if (stars >= 10 && stars <= 14) {
          stars = Math.max(stars - 1, 10)
        }
      } else {
        if (!safeguard) {
          stars = 12
        }
      }
    }

    setSimulationResults({
      attempts,
      totalCost,
      success: stars >= targetStars,
      finalStars: stars,
    })
  }

  const currentData = starforceData[currentStars as keyof typeof starforceData]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">星之力强化模拟器</h1>
        <Badge variant="secondary">CMS版本</Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Simulator Interface */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>强化模拟器</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Equipment Display */}
            <div className="bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg p-6">
              <div className="flex items-center justify-center mb-4">
                <div className="w-20 h-20 bg-blue-200 rounded-lg flex items-center justify-center border-2 border-dashed border-blue-400">
                  <span className="text-2xl">⚔️</span>
                </div>
              </div>

              {/* Stars Display */}
              <div className="flex justify-center mb-4">
                <div className="flex flex-wrap justify-center gap-1">
                  {Array.from({ length: 25 }).map((_, i) => (
                    <span key={i} className={`text-lg ${i < currentStars ? "text-yellow-400" : "text-gray-300"}`}>
                      ⭐
                    </span>
                  ))}
                </div>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  {currentStars}星 → {currentStars + 1}星
                </p>
                {currentData && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>成功概率:</span>
                      <span className="text-green-600 font-semibold">{currentData.success}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>失败(保持)概率:</span>
                      <span className="text-orange-600 font-semibold">{currentData.fail}%</span>
                    </div>
                    {currentData.destroy > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>装备损毁概率:</span>
                        <span className="text-red-600 font-semibold">{currentData.destroy}%</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Settings */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">装备等级</label>
                  <Select value={itemLevel.toString()} onValueChange={(v) => setItemLevel(Number.parseInt(v))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="160">160级</SelectItem>
                      <SelectItem value="150">150级</SelectItem>
                      <SelectItem value="140">140级</SelectItem>
                      <SelectItem value="130">130级</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">目标星数</label>
                  <Input
                    type="number"
                    min="1"
                    max="25"
                    value={targetStars}
                    onChange={(e) => setTargetStars(Number.parseInt(e.target.value) || 1)}
                  />
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="safeguard"
                    checked={safeguard}
                    onCheckedChange={(checked) => setSafeguard(checked as boolean)}
                  />
                  <label htmlFor="safeguard" className="text-sm">
                    防止损毁
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="mvp"
                    checked={mvpDiscount}
                    onCheckedChange={(checked) => setMvpDiscount(checked as boolean)}
                  />
                  <label htmlFor="mvp" className="text-sm">
                    MVP优惠 (30%折扣)
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="event"
                    checked={eventDiscount}
                    onCheckedChange={(checked) => setEventDiscount(checked as boolean)}
                  />
                  <label htmlFor="event" className="text-sm">
                    活动优惠 (30%折扣)
                  </label>
                </div>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button onClick={runSimulation} className="flex-1">
                开始模拟
              </Button>
              <Button variant="outline" onClick={() => setCurrentStars(0)}>
                重置
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>模拟结果</CardTitle>
          </CardHeader>
          <CardContent>
            {simulationResults ? (
              <div className="space-y-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold mb-2">{simulationResults.success ? "✅ 成功!" : "❌ 失败"}</div>
                  <p className="text-gray-600">最终星数: {simulationResults.finalStars}星</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>尝试次数:</span>
                    <span className="font-semibold">{simulationResults.attempts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>总花费:</span>
                    <span className="font-semibold text-yellow-600">
                      {simulationResults.totalCost.toLocaleString()} 金币
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>平均每次花费:</span>
                    <span className="font-semibold">
                      {Math.round(simulationResults.totalCost / simulationResults.attempts).toLocaleString()} 金币
                    </span>
                  </div>
                </div>

                <Progress
                  value={simulationResults.success ? 100 : (simulationResults.finalStars / targetStars) * 100}
                  className="w-full"
                />
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-4">⭐</div>
                <p>点击"开始模拟"查看结果</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>抓星星成功率加成算法</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <input type="radio" name="method" defaultChecked />
                <label>常法</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="radio" name="method" />
                <label>加法</label>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium mb-2">成功率加成(%):</label>
                <Input type="number" defaultValue="5" className="w-20" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>活动优惠</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <Checkbox id="event-active" />
                <label htmlFor="event-active">没有活动</label>
              </div>
              <p className="text-gray-600 mt-2">CMS 没有的活动方式</p>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>星之力强化费用优惠 30%</li>
                <li>5星以下不会损毁</li>
                <li>10星以下不会掉级</li>
                <li>15星以下不会损毁</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
