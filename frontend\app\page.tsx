import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ArrowRight, Zap, Database, Map, TrendingUp } from "lucide-react"
import Link from "next/link"
import { Sidebar } from "@/components/sidebar"

export default function HomePage() {
  const tools = [
    {
      name: "纸娃娃模拟器",
      description: "角色形象搭配工具",
      icon: "👗",
      href: "/paperdoll",
      color: "from-pink-500 to-rose-500",
    },
    {
      name: "爆率查询工具",
      description: "怪物掉落概率查询",
      icon: "💎",
      href: "/drop-rates",
      color: "from-emerald-500 to-teal-500",
    },
    {
      name: "星之力强化模拟",
      description: "装备强化成功率模拟",
      icon: "⭐",
      href: "/starforce",
      color: "from-yellow-500 to-orange-500",
    },
  ]

  const gameTools = [
    { name: "星之力强化模拟", icon: "⭐", href: "/starforce" },
    { name: "洗魔方模拟器", icon: "🎲", href: "/cubes" },
    { name: "超级属性计算器", icon: "📊", href: "/hyperstats" },
    { name: "WSE优化计算器", icon: "⚡", href: "/wse" },
    { name: "伤害公式模拟器", icon: "🔥", href: "/damage" },
    { name: "火龙套统计计算器", icon: "🐉", href: "/dragon-set" },
    { name: "联盟等级计算器", icon: "🏆", href: "/union" },
    { name: "神秘商店计算器", icon: "🔮", href: "/mystery-shop" },
  ]

  const categories = [
    { name: "套装", icon: "👕", count: 156 },
    { name: "脸型", icon: "😊", count: 89 },
    { name: "武器", icon: "⚔️", count: 234 },
    { name: "帽子", icon: "🎩", count: 178 },
    { name: "金币", icon: "💰", count: 45 },
    { name: "上衣", icon: "👔", count: 267 },
    { name: "下装", icon: "👖", count: 145 },
    { name: "披风", icon: "🦸", count: 98 },
    { name: "武器", icon: "🗡️", count: 189 },
    { name: "手套", icon: "🧤", count: 123 },
    { name: "耳环", icon: "💍", count: 67 },
    { name: "肩章", icon: "🎖️", count: 78 },
    { name: "其它", icon: "📦", count: 234 },
    { name: "坐骑", icon: "🐎", count: 89 },
    { name: "鞋子", icon: "👟", count: 156 },
    { name: "宠物", icon: "🐱", count: 145 },
    { name: "戒指", icon: "💎", count: 234 },
    { name: "徽章", icon: "🏅", count: 67 },
  ]

  return (
    <div className="flex gap-6">
      <Sidebar />
      <main className="flex-1 space-y-8">
        {/* Hero Section */}
        <Card className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white border-0">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent" />
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px)`,
              backgroundSize: "60px 60px",
            }}
          />
          <CardContent className="relative p-8">
            <div className="flex items-center justify-between">
              <div className="space-y-6 max-w-2xl">
                <div className="space-y-2">
                  <Badge className="bg-white/20 text-white border-white/30 mb-4">🎮 专业游戏数据库</Badge>
                  <h1 className="text-5xl font-bold leading-tight">
                    欢迎来到
                    <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                      冒险岛情报站
                    </span>
                  </h1>
                </div>
                <p className="text-xl text-blue-100 leading-relaxed">
                  专业的冒险岛数据库，提供最全面的游戏资料、实用工具和最新资讯。 让每一次冒险都充满智慧与策略！
                </p>
                <div className="flex flex-wrap gap-4">
                  <Link href="/cms-216">
                  <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 shadow-lg">
                    <span className="mr-2">🚀</span>
                    开始探索
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                  </Link>
                  <Link href="/tools">
                    <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-blue-600 hover:bg-white/10 backdrop-blur-sm"
                  >
                    <span className="mr-2">🛠️</span>
                    查看工具
                  </Button>
                    </Link>
                </div>
                <div className="flex items-center space-x-6 text-sm text-blue-200">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>实时更新</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>📊</span>
                    <span>5000+ 道具数据</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>⚡</span>
                    <span>12+ 实用工具</span>
                  </div>
                </div>
              </div>
              <div className="hidden lg:block relative">
                <div className="w-64 h-64 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-20 animate-pulse"></div>
                  <div className="absolute inset-4 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-30 animate-pulse delay-1000"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-8xl animate-bounce">🍄</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Featured Tools */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center space-x-2">
              <Zap className="w-6 h-6 text-yellow-500" />
              <span>实用工具</span>
            </h2>
            <Badge variant="secondary">热门推荐</Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {tools.map((tool, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-300 bg-white/80 backdrop-blur-sm border-gray-200"
              >
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${tool.color} rounded-xl flex items-center justify-center text-2xl`}
                    >
                      {tool.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg group-hover:text-blue-600 transition-colors">{tool.name}</h3>
                      <p className="text-gray-600 text-sm">{tool.description}</p>
                    </div>
                  </div>
                  <Link href={tool.href}>
                    <Button className="w-full mt-4 group-hover:bg-blue-600 transition-colors">立即使用</Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Game Tools Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {gameTools.map((tool, index) => (
              <Link key={index} href={tool.href}>
                <Card className="group hover:shadow-md transition-all duration-300 bg-white/80 backdrop-blur-sm border-gray-200 cursor-pointer">
                  <CardContent className="p-4 text-center">
                    <div className="text-3xl mb-2">{tool.icon}</div>
                    <p className="text-xs font-medium text-gray-700 group-hover:text-blue-600 transition-colors">
                      {tool.name}
                    </p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        {/* External Resources */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center space-x-2">
              <Database className="w-6 h-6 text-green-500" />
              <span>外部资源</span>
            </h2>
          </div>

          <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
            <CardContent className="p-6">
              <p className="text-gray-600 mb-4">官方外部可访问在线天空数据库UI的网址，全部均为第三方</p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {["官服", "台服", "韩服", "日服"].map((server, index) => (
                  <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl mb-2">🌐</div>
                    <p className="font-medium">{server}官网</p>
                    <Button variant="outline" size="sm" className="mt-2">
                      访问
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Version New Items */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center space-x-2">
              <span className="text-2xl">🆕</span>
              <span>版本新增道具</span>
            </h2>
            <Badge variant="secondary" className="bg-red-100 text-red-800">
              CMS-216
            </Badge>
          </div>

          <Link href="/cms-216">
            <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer bg-gradient-to-r from-red-500 via-pink-500 to-purple-600 text-white border-0 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent" />
              <div className="absolute top-4 right-4">
                <Badge className="bg-white/20 text-white border-white/30">NEW</Badge>
              </div>
              <CardContent className="relative p-8">
                <div className="flex items-center justify-between">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <h3 className="text-3xl font-bold">CMS-216 版本新增道具</h3>
                      <p className="text-xl text-pink-100">探索最新版本的全新装备和道具</p>
                    </div>
                    <div className="flex items-center space-x-6 text-sm text-pink-200">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">⚔️</span>
                        <span>新装备</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">💎</span>
                        <span>稀有道具</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">🎁</span>
                        <span>限时获取</span>
                      </div>
                    </div>
                    <Button
                      size="lg"
                      className="bg-white text-purple-600 hover:bg-pink-50 shadow-lg group-hover:shadow-xl transition-all"
                    >
                      <span className="mr-2">🔍</span>
                      立即查看
                      <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                  <div className="hidden lg:block relative">
                    <div className="w-48 h-48 relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-30 animate-pulse"></div>
                      <div className="absolute inset-4 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full opacity-40 animate-pulse delay-500"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-6xl animate-bounce">🎁</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </section>

        {/* Categories */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center space-x-2">
              <Map className="w-6 h-6 text-purple-500" />
              <span>数据分类</span>
            </h2>
          </div>

          <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-9 gap-3">
            {categories.map((category, index) => (
              <Card
                key={index}
                className="group hover:shadow-md transition-all duration-300 bg-white/80 backdrop-blur-sm border-gray-200 cursor-pointer"
              >
                <CardContent className="p-3 text-center">
                  <div className="text-2xl mb-1">{category.icon}</div>
                  <p className="text-xs font-medium text-gray-700 group-hover:text-blue-600 transition-colors mb-1">
                    {category.name}
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Latest Updates */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold flex items-center space-x-2">
              <TrendingUp className="w-6 h-6 text-red-500" />
              <span>最新更新分享</span>
            </h2>
            <Button variant="outline" size="sm">
              More
            </Button>
          </div>

          <Card className="bg-white/80 backdrop-blur-sm border-gray-200">
            <CardContent className="p-6">
              <p className="text-gray-600 text-center">技能数据库已经合并至数据库为九力游戏内容百科全书</p>
            </CardContent>
          </Card>
        </section>
      </main>
    </div>
  )
}
