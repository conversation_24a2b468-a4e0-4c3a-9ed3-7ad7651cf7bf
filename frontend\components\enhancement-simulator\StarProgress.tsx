'use client'

interface StarProgressProps {
  currentLevel: number
  maxLevel: number
}

export default function StarProgress({ currentLevel, maxLevel }: StarProgressProps) {
  const nextLevel = Math.min(currentLevel + 1, maxLevel)
  
  return (
    <div
      className="absolute flex items-center justify-center z-[8]"
      style={{
        top: '275px',
        left: '154px',
        width: '200px',
        height: '40px',
      }}
    >
      <div className="flex items-center gap-[15px] text-[18px] font-bold bg-transparent p-2 border-none">
        {/* 当前星级 */}
        <div className="flex items-center gap-[5px] text-[#ffd700]">
          <span>⭐</span>
          <span>{currentLevel}</span>
        </div>

        {/* 箭头 */}
        <div className="text-[#888] text-[16px]">→</div>

        {/* 下一星级 */}
        <div className="flex items-center gap-[5px] text-[#ffd700]">
          <span>⭐</span>
          <span>{nextLevel}</span>
        </div>
      </div>
    </div>
  )
}
