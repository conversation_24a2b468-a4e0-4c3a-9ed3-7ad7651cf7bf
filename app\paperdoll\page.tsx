import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function PaperdollPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">纸娃娃模拟器</h1>
        <Badge variant="secondary">Beta</Badge>
      </div>

      <Card className="bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>角色形象搭配</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Character Preview */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">角色预览</h3>
              <div className="bg-gradient-to-b from-blue-100 to-purple-100 rounded-lg p-8 min-h-96 flex items-center justify-center">
                <div className="text-6xl">🧙‍♂️</div>
              </div>
              <div className="flex space-x-2">
                <Button>保存搭配</Button>
                <Button variant="outline">重置</Button>
                <Button variant="outline">随机搭配</Button>
              </div>
            </div>
            {/*<div>The entire web application should display text using Poppins as the primary font, with the specified fallback chain ensuring consistent typography across all devices and browsers.</div>*/}

            {/* Equipment Categories */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">装备分类</h3>
              <Tabs defaultValue="weapon" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="weapon">武器</TabsTrigger>
                  <TabsTrigger value="armor">防具</TabsTrigger>
                  <TabsTrigger value="accessory">饰品</TabsTrigger>
                  <TabsTrigger value="cash">时装</TabsTrigger>
                </TabsList>
                <TabsContent value="weapon" className="space-y-2">
                  <div className="grid grid-cols-4 gap-2">
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div
                        key={i}
                        className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"
                      >
                        <span className="text-2xl">⚔️</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="armor" className="space-y-2">
                  <div className="grid grid-cols-4 gap-2">
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div
                        key={i}
                        className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"
                      >
                        <span className="text-2xl">🛡️</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="accessory" className="space-y-2">
                  <div className="grid grid-cols-4 gap-2">
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div
                        key={i}
                        className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"
                      >
                        <span className="text-2xl">💍</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="cash" className="space-y-2">
                  <div className="grid grid-cols-4 gap-2">
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div
                        key={i}
                        className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"
                      >
                        <span className="text-2xl">👗</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
