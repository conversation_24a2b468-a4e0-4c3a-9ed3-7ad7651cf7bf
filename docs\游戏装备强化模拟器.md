我需要开发msu游戏的装备强化模拟器web前端，完整阅读UIEquipEnchant.img.xml了解装备强化界面的布局，并正确使用UIEquipEnchant.img目录下的UI图片完成前端的UI界面和动效，逻辑部分暂不处理
这是链游冒险岛的各种资料集齐的网站。 先阅读整个项目，分析项目。
我需要添加一个装备强化模拟器，完整阅读public/images/UIEquipEnchant/目录 了解装备强化界面的布局。在工具/tools页面的装备工具栏，添加“装备强化模拟器”。
正确使用UIEquipEnchant目录下的UI图片完成前端的UI界面和动效。

完整功能
主强化界面（星力、潜能、附加属性）
兑换系统界面
各种弹窗和对话框
星力强化系统 - 0-25星完整强化
强化小游戏 - 精准度游戏机制
真实成功率 - 星级越高难度越大
破坏机制 - 高星级装备可能被破坏
粒子特效 - 成功时的视觉效果
费用计算 - 动态计算强化费用

动态效果支持
按钮状态动画（normal/mouseOver/pressed/disabled）
粒子特效系统（particle1-5）
强化成功/失败特效
进度条和计时器动画

精美界面
仿游戏UI设计 - 深色主题，渐变效果
流畅动画 - 按钮悬停、粒子效果
响应式交互 - 完整的用户体验
视觉反馈 - 成功/失败状态显示

1、我发现，很多image的路径，你搞错了。 我修改了几处，并推送到了github。你要git pull来更新代码。
2、我将装备布局文件放到了 public/images/UIEquipEnchant/UIEquipEnchant.img.xml
3、你必须完整阅读UIEquipEnchant.img.xml了解装备强化界面的布局，然后修改代码。
4、我还在游戏的时候，抓取了屏幕截图，并将截图放在 public/images/Screenshots
首先 参考public/images/Screenshots/1.png 和public/images/Screenshots/2.png 实现装备强化页面和效果。


1、你必须完整阅读UIEquipEnchant.img.xml了解装备强化界面的布局
2、我还在游戏的时候，抓取了屏幕截图，并将截图放在 public/images/Screenshots
3、参考我的截图实现冒险岛游戏的装备强化页面和动画效果。


我用html开发了MSU装备强化模拟器。项目文件在这个 mxd-tools-enhancement 目录，你参考 README.md 可以了解项目信息。然后将MSU装备强化模拟器工具，添加重构到当项目。在工具/tools页面的装备工具栏，添加“装备强化模拟器”。要将 mxd-tools-enhancement 的功能全部重构到当前next项目





