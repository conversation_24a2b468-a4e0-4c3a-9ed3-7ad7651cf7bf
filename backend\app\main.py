"""
MapleStory Info Station Backend API
FastAPI application entry point
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from contextlib import asynccontextmanager
import time
import logging

from app.core.config import settings
from app.api.router import api_router
from app.middleware.cors import setup_cors
from app.middleware.auth import AuthMiddleware

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="MapleStory Information Station Backend API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
)

# Setup CORS
setup_cors(app)

# Add custom middleware
app.add_middleware(AuthMiddleware)

# Add request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "app_name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT
        },
        "timestamp": time.time()
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "MapleStory Info Station Backend API",
        "version": settings.APP_VERSION,
        "docs_url": f"{settings.API_V1_STR}/docs"
    }

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "Internal server error",
                "details": str(exc) if settings.DEBUG else None
            },
            "timestamp": time.time()
        }
    )

# Application startup
async def startup():
    """Application startup"""
    from app.core.database import check_db_connection, check_redis_connection

    logger.info("Starting MapleStory Info Station Backend...")

    # Check connections
    db_ok = await check_db_connection()
    redis_ok = await check_redis_connection()

    if db_ok and redis_ok:
        logger.info("All services connected successfully")
    else:
        logger.warning("Some services failed to connect")

# Call startup manually for now
# startup() will be called when the server starts

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
