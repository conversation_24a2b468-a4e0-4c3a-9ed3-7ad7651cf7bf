const { execSync } = require("child_process")
const fs = require("fs")
const path = require("path")

console.log("Testing build to identify the exact error...")

try {
  // Create necessary directories
  const cmsDir = path.join(process.cwd(), "public/images/cms-216")
  if (!fs.existsSync(cmsDir)) {
    fs.mkdirSync(cmsDir, { recursive: true })
    console.log("Created cms-216 directory")
  }

  // Run the build
  const result = execSync("npm run build", {
    encoding: "utf8",
    stdio: "pipe",
    timeout: 120000,
  })

  console.log("✅ Build successful!")
  console.log(result)
} catch (error) {
  console.log("❌ Build failed with error:")
  console.log("STDERR:", error.stderr)
  console.log("STDOUT:", error.stdout)

  // Check for specific errors
  const errorOutput = (error.stderr || "") + (error.stdout || "")

  if (errorOutput.includes("generateStaticParams")) {
    console.log("\n🔍 IDENTIFIED: Dynamic API route issue with static export")
    console.log("Need to remove /api/items/[id] route or change export config")
  }

  if (errorOutput.includes('Page "/api/items/[id]"')) {
    console.log("\n🔍 CONFIRMED: The /api/items/[id]/route.ts is causing the build failure")
  }
}
