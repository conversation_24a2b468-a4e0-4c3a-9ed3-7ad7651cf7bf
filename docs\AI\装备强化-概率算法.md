# 1
我有一个 CSV 文件，记录了《冒险岛》装备“星之力”强化系统的概率数据，字段包括：
当前级, 下一级, 成功率, 失败保级, 失败降级, 失败损坏。
请用 Python 编写一个强化模拟器，要求如下：

1. 从该 CSV 文件中读取并构建强化概率映射（按当前星级索引）。
2. 实现函数 simulate_enhancement(current_star: int) -> Tuple[str, int]，用于模拟一次强化。
3. 每次强化使用对应星级的概率，根据以下逻辑判断结果：
成功：进入“下一级”，返回 "Success"
失败保级：返回 "Fail (Hold)"，星级不变
失败降级：返回 "Fail (Drop)"，星级 -1
失败损坏：返回 "Boom"，星级变为 0
4. 所有概率总和应为 1（或接近 1）。可使用 random.choices() 或等效方式实现带权重随机选择。
5. 提供一个函数用于连续模拟 N 次强化过程，从 0 星开始，打印每次星级和结果。
无需使用任何外部依赖，仅使用标准库完成。

还要考虑，用户开启了“解锁抓星星”和“防止破坏”选项。



