import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Search, Filter } from "lucide-react"

export default function DatabasePage() {
  const categories = [
    { name: "武器", count: 1234, icon: "⚔️", color: "bg-red-100 text-red-800" },
    { name: "防具", count: 856, icon: "🛡️", color: "bg-blue-100 text-blue-800" },
    { name: "饰品", count: 567, icon: "💍", color: "bg-purple-100 text-purple-800" },
    { name: "消耗品", count: 2341, icon: "🧪", color: "bg-green-100 text-green-800" },
    { name: "其他", count: 789, icon: "📦", color: "bg-gray-100 text-gray-800" },
    { name: "宠物", count: 234, icon: "🐱", color: "bg-pink-100 text-pink-800" },
  ]

  const recentItems = [
    { name: "屠龙刀", type: "武器", level: 200, icon: "⚔️" },
    { name: "龙鳞甲", type: "防具", level: 180, icon: "🛡️" },
    { name: "力量戒指", type: "饰品", level: 120, icon: "💍" },
    { name: "生命药水", type: "消耗品", level: 1, icon: "🧪" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">游戏数据库</h1>
        <Badge variant="secondary">5,021 items</Badge>
      </div>

      {/* Search Section */}
      <Card className="bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input placeholder="搜索道具、装备、技能..." className="pl-10" />
            </div>
            <Button>
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
            <Button>搜索</Button>
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {categories.map((category, index) => (
          <Card
            key={index}
            className="group hover:shadow-lg transition-all duration-300 cursor-pointer bg-white/80 backdrop-blur-sm"
          >
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-3">{category.icon}</div>
              <h3 className="font-semibold text-lg mb-2 group-hover:text-blue-600 transition-colors">
                {category.name}
              </h3>
              <Badge className={category.color}>{category.count} 项</Badge>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Items */}
      <Card className="bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>最近更新</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentItems.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              >
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">{item.icon}</div>
                  <div>
                    <h4 className="font-semibold">{item.name}</h4>
                    <p className="text-sm text-gray-600">
                      {item.type} • 等级 {item.level}
                    </p>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  查看详情
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
