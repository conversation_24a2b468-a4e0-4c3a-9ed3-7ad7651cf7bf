"""
Membership schemas
"""

from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class MembershipTierBase(BaseModel):
    """会员等级基础模式"""
    name: str
    display_name: str
    level: int
    description: Optional[str] = None
    permissions: Dict[str, Any]

class MembershipTierCreate(MembershipTierBase):
    """创建会员等级模式"""
    pass

class MembershipTierResponse(MembershipTierBase):
    """会员等级响应模式"""
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserMembershipCreate(BaseModel):
    """创建用户会员关系模式"""
    user_id: int
    tier_id: int
    expires_at: Optional[datetime] = None

class UserMembershipResponse(BaseModel):
    """用户会员关系响应模式"""
    id: int
    user_id: int
    tier_id: int
    started_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool
    tier: MembershipTierResponse
    
    class Config:
        from_attributes = True
