// 装备过滤和排序工具函数

import { ItemListEntry, EnhancementType } from '@/types/enhancement'

// 获取装备等级
export function getItemLevel(item: ItemListEntry): number {
  // 尝试多个可能的路径来获取required信息
  let requiredData = null
  
  // 路径1: 在item.detailInfo.metadata.required (正确路径)
  if (item.detailInfo?.metadata?.required) {
    requiredData = item.detailInfo.metadata.required
  }
  // 路径2: 在item.detailInfo.metadata.common.required (备用路径)
  else if (item.detailInfo?.metadata?.common?.required) {
    requiredData = item.detailInfo.metadata.common.required
  }
  // 路径3: 在item.detailInfo.required (备用路径)
  else if (item.detailInfo?.required) {
    requiredData = item.detailInfo.required
  }

  // 最优先：从required.level获取装备等级要求
  if (requiredData && requiredData.level !== undefined) {
    return requiredData.level
  }
  
  // 次优先：从detailInfo中获取等级
  if (item.detailInfo?.metadata?.common?.level) {
    return item.detailInfo.metadata.common.level
  }
  
  // 如果没有级别信息，尝试根据itemId智能推断等级
  const itemId = parseInt(item.itemId.toString())
  
  // 根据装备ID范围推断等级（这是一个简化的推断逻辑）
  if (itemId >= 1004000) return 160  // 高级装备
  if (itemId >= 1003000) return 140  // 中高级装备
  if (itemId >= 1002000) return 100  // 中级装备
  if (itemId >= 1001000) return 50   // 低中级装备
  
  return 0 // 默认返回0表示无等级要求
}

// 根据强化类型过滤装备
export function filterItemsByEnhancementType(
  items: ItemListEntry[], 
  enhancementType: EnhancementType
): ItemListEntry[] {
  return items.filter(item => {
    const common = item.detailInfo?.metadata?.common
    if (!common) return false

    switch (enhancementType) {
      case 'starforce':
        return common.enableStarforce === true
      case 'potential':
        return !common.blockUpgradePotential
      case 'bonusstat':
        return !common.blockUpgradeExtraOption
      default:
        return false
    }
  })
}

// 按等级排序装备
export function sortItemsByLevel(items: ItemListEntry[]): ItemListEntry[] {
  return [...items].sort((a, b) => {
    const levelA = getItemLevel(a)
    const levelB = getItemLevel(b)
    return levelA - levelB
  })
}

// 获取强化类型的显示名称
export function getEnhancementTypeDisplayName(type: EnhancementType): string {
  switch (type) {
    case 'starforce':
      return '星力强化道具'
    case 'potential':
      return '潜能重设道具'
    case 'bonusstat':
      return '额外属性道具'
    default:
      return '未知类型'
  }
}

// 获取装备类型名称
export function getItemTypeName(item: ItemListEntry): string {
  const category = item.detailInfo?.metadata?.category
  if (category?.tier3?.label) {
    // 将英文标签转换为中文
    const typeMap: Record<string, string> = {
      'Hat': '帽子',
      'Top': '上衣',
      'Bottom': '下装',
      'Overall': '套装',
      'Shoes': '鞋子',
      'Gloves': '手套',
      'Weapon': '武器',
      'Shield': '盾牌',
      'Earrings': '耳环',
      'Ring': '戒指',
      'Pendant': '项链',
      'Belt': '腰带',
      'Cape': '披风',
      'Face': '脸饰',
      'Eye': '眼饰'
    }
    return typeMap[category.tier3.label] || category.tier3.label
  }
  
  // 根据itemCategory返回中文装备类型名称
  const categoryMap: Record<number, string> = {
    1000201001: '帽子', 1000301001: '帽子',
    1000201002: '上衣', 1000301002: '上衣',
    1000201003: '下装', 1000301003: '下装',
    1000201004: '套装', 1000301004: '套装',
    1000201005: '鞋子', 1000301005: '鞋子',
    1000201006: '手套', 1000301006: '手套',
    1000201007: '武器', 1000301007: '武器',
    1000201008: '盾牌', 1000301008: '盾牌',
    1000201009: '饰品', 1000301009: '饰品',
    1000401001: '宠物',
    1000501001: '消耗品'
  }
  
  return categoryMap[item.category] || '未知类型'
}

// 检查装备是否为现金道具
export function isCashItem(item: ItemListEntry): boolean {
  return item.detailInfo?.metadata?.common?.isCashItem === true
}

// 检查装备是否为BOSS奖励
export function isBossReward(item: ItemListEntry): boolean {
  return item.detailInfo?.metadata?.common?.isBossReward === true
}

// 检查装备是否为唯一装备
export function isOnlyEquip(item: ItemListEntry): boolean {
  return item.detailInfo?.metadata?.common?.isOnly === true
}

// 获取装备的职业要求
export function getJobRequirement(item: ItemListEntry): string {
  const job = item.detailInfo?.metadata?.required?.job
  if (job?.className && job.className !== 'Explorer') {
    return job.className
  }
  return '全职业'
}

// 获取装备的属性要求
export function getStatRequirements(item: ItemListEntry): {
  str: number
  dex: number
  int: number
  luk: number
} {
  const required = item.detailInfo?.metadata?.required
  return {
    str: required?.str || 0,
    dex: required?.dex || 0,
    int: required?.int || 0,
    luk: required?.luk || 0
  }
}
