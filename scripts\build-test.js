const { execSync } = require("child_process")
const fs = require("fs")
const path = require("path")

console.log("Testing pnpm run build...")

try {
  // First, let's check if we have the necessary files
  console.log("Checking project structure...")

  // Create necessary directories if they don't exist
  const dirs = ["public/images/cms-216", "app/globals.css"]

  dirs.forEach((dir) => {
    const fullPath = path.join(process.cwd(), dir)
    if (!fs.existsSync(fullPath)) {
      if (dir.includes(".css")) {
        // Create the CSS file
        fs.writeFileSync(
          fullPath,
          `
@tailwind base;
@tailwind components;
@tailwind utilities;
        `,
        )
        console.log(`Created ${dir}`)
      } else {
        // Create directory
        fs.mkdirSync(fullPath, { recursive: true })
        console.log(`Created directory ${dir}`)
      }
    }
  })

  // Run the build
  console.log("Running build...")
  const result = execSync("npm run build", {
    encoding: "utf8",
    stdio: "pipe",
  })

  console.log("Build successful!")
  console.log(result)
} catch (error) {
  console.log("Build failed with errors:")
  console.log(error.stdout)
  console.log(error.stderr)

  // Let's analyze the errors and provide fixes
  const errorOutput = error.stderr + error.stdout

  if (errorOutput.includes("Cannot find module")) {
    console.log("\n=== Missing Module Errors ===")
    console.log("Need to install missing dependencies")
  }

  if (errorOutput.includes("Type error")) {
    console.log("\n=== TypeScript Errors ===")
    console.log("Need to fix TypeScript issues")
  }

  if (errorOutput.includes("ESLint")) {
    console.log("\n=== ESLint Errors ===")
    console.log("Need to fix linting issues")
  }
}
