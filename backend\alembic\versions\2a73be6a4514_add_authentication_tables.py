"""Add authentication tables

Revision ID: 2a73be6a4514
Revises: 
Create Date: 2025-06-24 20:16:52.940079

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2a73be6a4514'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('membership_tiers',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('permissions', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('level'),
    sa.UniqueConstraint('name')
    )
    op.create_index('idx_membership_tier_level', 'membership_tiers', ['level'], unique=False)
    op.create_index('idx_membership_tier_name', 'membership_tiers', ['name'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('fingerprint_id', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_user_active', 'users', ['is_active'], unique=False)
    op.create_index('idx_user_email', 'users', ['email'], unique=False)
    op.create_index('idx_user_fingerprint', 'users', ['fingerprint_id'], unique=False)
    op.create_index('idx_user_username', 'users', ['username'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_fingerprint_id'), 'users', ['fingerprint_id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('currency_accounts',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('balance', sa.DECIMAL(precision=15, scale=2), nullable=False),
    sa.Column('total_earned', sa.DECIMAL(precision=15, scale=2), nullable=False),
    sa.Column('total_spent', sa.DECIMAL(precision=15, scale=2), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint('balance >= 0', name='positive_balance'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index('idx_currency_account_user', 'currency_accounts', ['user_id'], unique=False)
    op.create_table('device_fingerprints',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('fingerprint_id', sa.String(length=255), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('device_info', sa.JSON(), nullable=True),
    sa.Column('first_seen', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('last_seen', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('visit_count', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('fingerprint_id')
    )
    op.create_index('idx_device_fingerprint_id', 'device_fingerprints', ['fingerprint_id'], unique=False)
    op.create_index('idx_device_fingerprint_user', 'device_fingerprints', ['user_id'], unique=False)
    op.create_table('email_verifications',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('verified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token')
    )
    op.create_index('idx_email_verification_token', 'email_verifications', ['token'], unique=False)
    op.create_index('idx_email_verification_user', 'email_verifications', ['user_id', 'verified_at'], unique=False)
    op.create_table('password_resets',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('used_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token')
    )
    op.create_index('idx_password_reset_token', 'password_resets', ['token'], unique=False)
    op.create_index('idx_password_reset_user', 'password_resets', ['user_id', 'used_at'], unique=False)
    op.create_table('transactions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=20), nullable=False),
    sa.Column('amount', sa.DECIMAL(precision=15, scale=2), nullable=False),
    sa.Column('balance_before', sa.DECIMAL(precision=15, scale=2), nullable=False),
    sa.Column('balance_after', sa.DECIMAL(precision=15, scale=2), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('reference_type', sa.String(length=50), nullable=True),
    sa.Column('reference_id', sa.String(length=100), nullable=True),
    sa.Column('extra_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_transaction_reference', 'transactions', ['reference_type', 'reference_id'], unique=False)
    op.create_index('idx_transaction_type', 'transactions', ['type'], unique=False)
    op.create_index('idx_transaction_user_created', 'transactions', ['user_id', 'created_at'], unique=False)
    op.create_table('user_memberships',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('tier_id', sa.Integer(), nullable=False),
    sa.Column('started_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['tier_id'], ['membership_tiers.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_user_membership_tier_active', 'user_memberships', ['tier_id', 'is_active'], unique=False)
    op.create_index('idx_user_membership_user_active', 'user_memberships', ['user_id', 'is_active'], unique=False)
    op.create_table('user_sessions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_token', sa.String(length=255), nullable=False),
    sa.Column('refresh_token', sa.String(length=255), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('last_activity', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('refresh_token'),
    sa.UniqueConstraint('session_token')
    )
    op.create_index('idx_user_session_refresh', 'user_sessions', ['refresh_token'], unique=False)
    op.create_index('idx_user_session_token', 'user_sessions', ['session_token'], unique=False)
    op.create_index('idx_user_session_user_expires', 'user_sessions', ['user_id', 'expires_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_user_session_user_expires', table_name='user_sessions')
    op.drop_index('idx_user_session_token', table_name='user_sessions')
    op.drop_index('idx_user_session_refresh', table_name='user_sessions')
    op.drop_table('user_sessions')
    op.drop_index('idx_user_membership_user_active', table_name='user_memberships')
    op.drop_index('idx_user_membership_tier_active', table_name='user_memberships')
    op.drop_table('user_memberships')
    op.drop_index('idx_transaction_user_created', table_name='transactions')
    op.drop_index('idx_transaction_type', table_name='transactions')
    op.drop_index('idx_transaction_reference', table_name='transactions')
    op.drop_table('transactions')
    op.drop_index('idx_password_reset_user', table_name='password_resets')
    op.drop_index('idx_password_reset_token', table_name='password_resets')
    op.drop_table('password_resets')
    op.drop_index('idx_email_verification_user', table_name='email_verifications')
    op.drop_index('idx_email_verification_token', table_name='email_verifications')
    op.drop_table('email_verifications')
    op.drop_index('idx_device_fingerprint_user', table_name='device_fingerprints')
    op.drop_index('idx_device_fingerprint_id', table_name='device_fingerprints')
    op.drop_table('device_fingerprints')
    op.drop_index('idx_currency_account_user', table_name='currency_accounts')
    op.drop_table('currency_accounts')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_fingerprint_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index('idx_user_username', table_name='users')
    op.drop_index('idx_user_fingerprint', table_name='users')
    op.drop_index('idx_user_email', table_name='users')
    op.drop_index('idx_user_active', table_name='users')
    op.drop_table('users')
    op.drop_index('idx_membership_tier_name', table_name='membership_tiers')
    op.drop_index('idx_membership_tier_level', table_name='membership_tiers')
    op.drop_table('membership_tiers')
    # ### end Alembic commands ###
