"""
Membership models
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Index, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class MembershipTier(Base):
    __tablename__ = "membership_tiers"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), unique=True, nullable=False)  # guest, registered, gold, diamond, admin
    display_name = Column(String(100), nullable=False)  # 游客, 注册用户, 黄金用户, 钻石用户, 管理员
    level = Column(Integer, nullable=False, unique=True)  # 0, 1, 2, 3, 4
    description = Column(Text, nullable=True)
    permissions = Column(JSON, nullable=True)  # 权限配置
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user_memberships = relationship("UserMembership", back_populates="tier")
    
    # Indexes
    __table_args__ = (
        Index('idx_membership_tier_name', 'name'),
        Index('idx_membership_tier_level', 'level'),
    )

class UserMembership(Base):
    __tablename__ = "user_memberships"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    tier_id = Column(Integer, ForeignKey("membership_tiers.id"), nullable=False)
    started_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)  # NULL 表示永久
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="memberships")
    tier = relationship("MembershipTier", back_populates="user_memberships")
    
    # Indexes
    __table_args__ = (
        Index('idx_user_membership_user_active', 'user_id', 'is_active'),
        Index('idx_user_membership_tier_active', 'tier_id', 'is_active'),
    )
