"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"
import { ItemTooltip } from "./item-tooltip"

interface ItemCardProps {
  item: {
    id: string
    name: string
    image: string
    filename: string
  }
}

export function ItemCard({ item }: ItemCardProps) {
  const [imageError, setImageError] = useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  return (
    <ItemTooltip itemId={item.id}>
      <Link href={`/items/${encodeURIComponent(item.id)}`}>
        <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer bg-white/80 backdrop-blur-sm border-gray-200">
          <CardContent className="p-3">
            <div className="aspect-square bg-gray-100 rounded-lg mb-2 overflow-hidden">
              <Image
                src={imageError ? "/placeholder.svg?height=80&width=80" : item.image}
                alt={item.name}
                width={80}
                height={80}
                className="w-full h-full object-contain group-hover:scale-110 transition-transform duration-300"
                onError={handleImageError}
              />
            </div>
            <div className="text-center">
              <p className="text-xs font-medium text-gray-700 group-hover:text-blue-600 transition-colors line-clamp-2">
                {item.name}
              </p>
            </div>
          </CardContent>
        </Card>
      </Link>
    </ItemTooltip>
  )
}
