"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"

export default function HyperstatsPage() {
  const [level, setLevel] = useState(200)
  const [hyperStats, setHyperStats] = useState({
    str: 0,
    dex: 0,
    int: 0,
    luk: 0,
    hp: 0,
    mp: 0,
    df: 0, // 无视防御
    damage: 0, // 伤害
    bossDamage: 0, // BOSS伤害
    critRate: 0, // 暴击率
    critDamage: 0, // 暴击伤害
    arcaneForce: 0, // 神秘之力
    sacredPower: 0, // 圣晶之力
  })

  const hyperStatNames = {
    str: "STR",
    dex: "DEX",
    int: "INT",
    luk: "LUK",
    hp: "最大HP",
    mp: "最大MP",
    df: "无视防御",
    damage: "伤害",
    bossDamage: "BOSS伤害",
    critRate: "暴击率",
    critDamage: "暴击伤害",
    arcaneForce: "神秘之力",
    sacredPower: "圣晶之力",
  }

  const getHyperStatCost = (currentLevel: number) => {
    if (currentLevel < 10) return currentLevel + 1
    if (currentLevel < 50) return Math.floor((currentLevel - 9) / 5) + 10
    if (currentLevel < 100) return Math.floor((currentLevel - 49) / 10) + 20
    return Math.floor((currentLevel - 99) / 20) + 25
  }

  const getTotalHyperStatPoints = (level: number) => {
    if (level < 140) return 0
    return Math.min(level - 139, 650)
  }

  const getUsedPoints = () => {
    return Object.values(hyperStats).reduce((total, statLevel) => {
      let points = 0
      for (let i = 0; i < statLevel; i++) {
        points += getHyperStatCost(i)
      }
      return total + points
    }, 0)
  }

  const updateHyperStat = (stat: string, value: number) => {
    setHyperStats((prev) => ({
      ...prev,
      [stat]: Math.max(0, Math.min(150, value)),
    }))
  }

  const resetHyperStats = () => {
    setHyperStats({
      str: 0,
      dex: 0,
      int: 0,
      luk: 0,
      hp: 0,
      mp: 0,
      df: 0,
      damage: 0,
      bossDamage: 0,
      critRate: 0,
      critDamage: 0,
      arcaneForce: 0,
      sacredPower: 0,
    })
  }

  const totalPoints = getTotalHyperStatPoints(level)
  const usedPoints = getUsedPoints()
  const remainingPoints = totalPoints - usedPoints

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">超级属性计算器</h1>
        <Badge variant="secondary">Hyper Stats</Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Level Settings */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>角色设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">角色等级</label>
              <div className="space-y-2">
                <Slider
                  value={[level]}
                  onValueChange={(value) => setLevel(value[0])}
                  min={140}
                  max={300}
                  step={1}
                  className="w-full"
                />
                <Input
                  type="number"
                  value={level}
                  onChange={(e) => setLevel(Number.parseInt(e.target.value) || 140)}
                  min={140}
                  max={300}
                />
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>总超级属性点:</span>
                  <span className="font-semibold">{totalPoints}</span>
                </div>
                <div className="flex justify-between">
                  <span>已使用点数:</span>
                  <span className="font-semibold text-blue-600">{usedPoints}</span>
                </div>
                <div className="flex justify-between">
                  <span>剩余点数:</span>
                  <span className={`font-semibold ${remainingPoints < 0 ? "text-red-600" : "text-green-600"}`}>
                    {remainingPoints}
                  </span>
                </div>
              </div>
            </div>

            <Button onClick={resetHyperStats} variant="outline" className="w-full">
              重置所有属性
            </Button>
          </CardContent>
        </Card>

        {/* Hyper Stats */}
        <Card className="lg:col-span-2 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>超级属性分配</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(hyperStats).map(([stat, value]) => (
                <div key={stat} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">{hyperStatNames[stat as keyof typeof hyperStatNames]}</label>
                    <Badge variant="outline" className="text-xs">
                      Lv.{value}
                    </Badge>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateHyperStat(stat, value - 1)}
                      disabled={value <= 0}
                    >
                      -
                    </Button>

                    <div className="flex-1">
                      <Slider
                        value={[value]}
                        onValueChange={(newValue) => updateHyperStat(stat, newValue[0])}
                        min={0}
                        max={150}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => updateHyperStat(stat, value + 1)}
                      disabled={value >= 150 || remainingPoints < getHyperStatCost(value)}
                    >
                      +
                    </Button>

                    <Input
                      type="number"
                      value={value}
                      onChange={(e) => updateHyperStat(stat, Number.parseInt(e.target.value) || 0)}
                      className="w-16 text-center"
                      min={0}
                      max={150}
                    />
                  </div>

                  <div className="text-xs text-gray-500 flex justify-between">
                    <span>下级消耗: {getHyperStatCost(value)} 点</span>
                    <span>
                      效果: +
                      {stat === "hp" || stat === "mp"
                        ? value * 50
                        : stat === "df" || stat === "damage" || stat === "bossDamage"
                          ? value * 3
                          : stat === "critRate" || stat === "critDamage"
                            ? value * 2
                            : stat === "arcaneForce" || stat === "sacredPower"
                              ? value * 5
                              : value * 30}
                      {stat === "hp" || stat === "mp"
                        ? ""
                        : stat === "df" ||
                            stat === "damage" ||
                            stat === "bossDamage" ||
                            stat === "critRate" ||
                            stat === "critDamage"
                          ? "%"
                          : ""}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Summary */}
      <Card className="bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>属性总览</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {Object.entries(hyperStats)
              .filter(([_, value]) => value > 0)
              .map(([stat, value]) => (
                <div key={stat} className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm font-medium text-gray-600 mb-1">
                    {hyperStatNames[stat as keyof typeof hyperStatNames]}
                  </div>
                  <div className="text-lg font-bold text-blue-600">Lv.{value}</div>
                  <div className="text-xs text-gray-500">
                    +
                    {stat === "hp" || stat === "mp"
                      ? value * 50
                      : stat === "df" || stat === "damage" || stat === "bossDamage"
                        ? value * 3
                        : stat === "critRate" || stat === "critDamage"
                          ? value * 2
                          : stat === "arcaneForce" || stat === "sacredPower"
                            ? value * 5
                            : value * 30}
                    {stat === "hp" || stat === "mp"
                      ? ""
                      : stat === "df" ||
                          stat === "damage" ||
                          stat === "bossDamage" ||
                          stat === "critRate" ||
                          stat === "critDamage"
                        ? "%"
                        : ""}
                  </div>
                </div>
              ))}
          </div>

          {Object.values(hyperStats).every((v) => v === 0) && (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-4">📊</div>
              <p>开始分配超级属性点数</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
