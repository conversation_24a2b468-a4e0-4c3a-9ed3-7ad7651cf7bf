// MSU装备强化模拟器主要逻辑
class EquipEnhanceSimulator {
    constructor() {
        // 游戏状态
        this.currentTab = 'starforce';
        this.equipLevel = 0;
        this.equipMaxLevel = 25; // 星力强化最大等级
        this.enhancing = false;
        this.minigameActive = false;
        this.autoMode = false;
        
        // 当前装备信息
        this.currentEquip = {
            itemId: null,
            name: '',
            type: '',
            category: ''
        };
        
        // 装备数据库
        this.itemDatabase = null;
        
        // 加载状态
        this.loadingProgress = 0;
        this.totalResources = 0;
        this.loadedResources = 0;
        
        // 常用装备itemId列表（用于测试）- 使用实际存在的装备
        this.testEquipItems = [
            { id: '1001164', name: '加载中...', type: 'hat' },
            { id: '1072952', name: '加载中...', type: 'shoes' },
            { id: '1082169', name: '加载中...', type: 'gloves' },
            { id: '1102149', name: '加载中...', type: 'ring' },
            { id: '1382104', name: '加载中...', type: 'overall' },
            { id: '1702424', name: '加载中...', type: 'weapon' },
            { id: '1702952', name: '加载中...', type: 'weapon' },
            { id: '1790342', name: '加载中...', type: 'weapon' },
            { id: '1791494', name: '加载中...', type: 'weapon' }
        ];
        
        // 特效动画状态
        this.effectAnimationFrame = 0;
        this.effectAnimationTimer = null;
        this.lastFrameTime = null;
        this.currentEffectType = 'standby'; // standby, progress, success, failed
        this.maxFrames = {
            standby: 16,
            progress: 16,
            success: 18,
            failed: 16
        };
        
        // 强化概率数据（按照游戏设定）
        this.starforceProbabilities = {
            0: { success: 95, failure: 5, major_failure: 0, failure_drop: 0 },
            1: { success: 90, failure: 10, major_failure: 0, failure_drop: 0 },
            2: { success: 85, failure: 15, major_failure: 0, failure_drop: 0 },
            3: { success: 85, failure: 15, major_failure: 0, failure_drop: 0 },
            4: { success: 80, failure: 20, major_failure: 0, failure_drop: 0 },
            5: { success: 75, failure: 25, major_failure: 0, failure_drop: 0 },
            6: { success: 70, failure: 30, major_failure: 0, failure_drop: 0 },
            7: { success: 65, failure: 35, major_failure: 0, failure_drop: 0 },
            8: { success: 60, failure: 40, major_failure: 0, failure_drop: 0 },
            9: { success: 55, failure: 45, major_failure: 0, failure_drop: 0 },
            10: { success: 50, failure: 45, major_failure: 5, failure_drop: 0 },
            11: { success: 45, failure: 50, major_failure: 5, failure_drop: 0 },
            12: { success: 40, failure: 55, major_failure: 5, failure_drop: 0 },
            13: { success: 35, failure: 60, major_failure: 5, failure_drop: 0 },
            14: { success: 30, failure: 65, major_failure: 5, failure_drop: 0 },
            15: { success: 30, failure: 67.9, major_failure: 2.1, failure_drop: 0 },
            16: { success: 30, failure: 67, major_failure: 3, failure_drop: 0 },
            17: { success: 30, failure: 65.9, major_failure: 4.1, failure_drop: 0 },
            18: { success: 30, failure: 64.8, major_failure: 5.2, failure_drop: 0 },
            19: { success: 30, failure: 63.7, major_failure: 6.3, failure_drop: 0 },
            20: { success: 30, failure: 62.6, major_failure: 7.4, failure_drop: 0 },
            21: { success: 30, failure: 61.5, major_failure: 8.5, failure_drop: 0 },
            22: { success: 3, failure: 77.6, major_failure: 19.4, failure_drop: 0 },
            23: { success: 2, failure: 68.6, major_failure: 29.4, failure_drop: 0 },
            24: { success: 1, failure: 59.4, major_failure: 39.6, failure_drop: 0 }
        };

        // 强化费用（金币）
        this.enhanceCosts = {
            0: 1000000, 1: 2000000, 2: 4000000, 3: 8000000, 4: 16000000,
            5: 32000000, 6: 64000000, 7: 128000000, 8: 256000000, 9: 512000000,
            10: 1024000000, 11: 2048000000, 12: 4096000000, 13: 8192000000, 14: 16384000000,
            15: 32768000000, 16: 65536000000, 17: 131072000000, 18: 262144000000, 19: 524288000000,
            20: 1048576000000, 21: 2097152000000, 22: 4194304000000, 23: 8388608000000, 24: 16777216000000
        };

        this.init();
    }

    async init() {
        // 显示loading界面
        this.showLoadingScreen();
        
        try {
            // 步骤1: 绑定事件
            this.updateLoadingProgress(10, '绑定事件监听器...');
            await this.delay(200);
            this.bindEvents();
            
            // 步骤2: 加载装备数据库
            this.updateLoadingProgress(20, '加载装备数据库...');
            await this.loadItemDatabase();
            
            // 步骤3: 预加载图片资源
            this.updateLoadingProgress(30, '预加载UI资源...');
            await this.preloadImages();
            
            // 步骤4: 预加载装备图片
            this.updateLoadingProgress(70, '预加载装备图片...');
            await this.preloadEquipImages();
            
            // 步骤5: 初始化UI
            this.updateLoadingProgress(90, '初始化界面...');
            await this.delay(300);
            this.updateUI();
            this.selectEquip(); // 自动放入装备
            
            // 确保数据加载完成后再初始化道具选择器
            if (this.itemList && this.itemList.length > 0) {
                this.initItemSelector(); // 初始化道具选择器
            } else {
                console.error('道具数据加载失败，无法初始化道具选择器');
            }
            
            // 步骤6: 完成加载
            this.updateLoadingProgress(100, '加载完成！');
            await this.delay(500);
            
            // 隐藏loading界面，显示主界面
            this.hideLoadingScreen();
            
            // 延迟启动特效，确保界面完全加载
            setTimeout(() => {
                this.initializeEffects();
            }, 500);
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.updateLoadingProgress(0, '加载失败，请刷新页面重试');
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
            // 2秒后完全移除loading界面
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 1000);
        }
    }

    updateLoadingProgress(percentage, status) {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        const loadingStatus = document.getElementById('loading-status');
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${status} ${percentage}%`;
        }
        
        if (loadingStatus) {
            loadingStatus.textContent = status;
        }
        
        this.loadingProgress = percentage;
    }

    async loadItemDatabase() {
        try {
            console.log('开始加载道具数据库...');
            console.log('当前页面URL:', window.location.href);
            console.log('是否为file://协议:', window.location.protocol === 'file:');
            
            // 检查全局数据是否已加载
            if (window.itemListData && Array.isArray(window.itemListData)) {
                console.log('使用预加载的道具数据...');
                const data = window.itemListData;
                console.log('预加载数据获取成功，数据类型:', typeof data, '长度:', data.length);
                
                this.itemDatabase = {};
                this.itemList = []; // 存储完整的道具列表
                
                // itemList.json是一个数组
                const originalData = [...data]; // 保存原始数据用于统计
                const filteredData = []; // 存储过滤后的数据
                
                data.forEach((item, index) => {
                    if (item && item.itemId && item.itemName) {
                        // 获取道具的需求等级
                        const requiredLevel = this.getItemLevelFromData(item);
                        
                        // 过滤掉需求等级为0的道具（通常是无意义的道具）
                        if (requiredLevel > 0) {
                            this.itemDatabase[item.itemId] = {
                                itemId: item.itemId,
                                name: item.itemName,
                                category: item.itemCategory,
                                imageUrl: item.imageUrl,
                                detailInfo: item.detailInfo,
                                required: item.required  // 添加required字段
                            };
                            this.itemList.push({
                                itemId: item.itemId,
                                name: item.itemName,
                                category: item.itemCategory,
                                imageUrl: item.imageUrl,
                                detailInfo: item.detailInfo,
                                required: item.required  // 添加required字段
                            });
                            
                            // 添加到过滤后的数据中
                            filteredData.push(item);
                        } else {
                            console.log(`跳过需求等级为0的道具: ${item.itemName} (ID: ${item.itemId})`);
                        }
                    } else {
                        console.warn(`道具 ${index} 数据不完整:`, item);
                    }
                });
                
                // 输出优化统计信息
                const originalCount = originalData.length;
                const filteredCount = filteredData.length;
                const removedCount = originalCount - filteredCount;
                const reductionPercentage = ((removedCount / originalCount) * 100).toFixed(1);
                
                console.log(`数据优化统计:`);
                console.log(`原始道具数量: ${originalCount}`);
                console.log(`过滤后数量: ${filteredCount}`);
                console.log(`移除数量: ${removedCount} (${reductionPercentage}%)`);
                
                // 创建优化后的数据文件内容
                const optimizedContent = `window.itemListData = ${JSON.stringify(filteredData, null, 0)};`;
                
                // 计算文件大小减少
                const originalSize = JSON.stringify(originalData).length;
                const optimizedSize = JSON.stringify(filteredData).length;
                const sizeSaved = originalSize - optimizedSize;
                const sizeReductionPercentage = ((sizeSaved / originalSize) * 100).toFixed(1);
                
                console.log(`文件大小优化:`);
                console.log(`原始大小: ${(originalSize / 1024 / 1024).toFixed(2)}MB`);
                console.log(`优化后大小: ${(optimizedSize / 1024 / 1024).toFixed(2)}MB`);
                console.log(`节省空间: ${(sizeSaved / 1024 / 1024).toFixed(2)}MB (${sizeReductionPercentage}%)`);
                
                // 提示用户可以手动保存优化后的文件
                console.log(`如需保存优化后的文件，请在浏览器控制台执行:`);
                console.log(`window.saveOptimizedData = () => {`);
                console.log(`  const blob = new Blob(['${optimizedContent.replace(/'/g, "\\'")}'], {type: 'application/javascript'});`);
                console.log(`  const url = URL.createObjectURL(blob);`);
                console.log(`  const a = document.createElement('a');`);
                console.log(`  a.href = url;`);
                console.log(`  a.download = 'itemList_level_filtered.js';`);
                console.log(`  a.click();`);
                console.log(`  URL.revokeObjectURL(url);`);
                console.log(`};`);
                console.log(`然后调用: window.saveOptimizedData()`);
                
                // 暴露优化后的数据到全局作用域，方便下载
                window.optimizedItemData = optimizedContent;
                
                // 更新测试装备名称
                this.updateTestEquipNames();
                console.log(`装备数据库加载完成，共${this.itemList.length}个道具`);
                
                // 统计不同类型装备的数量
                const starforceCount = this.itemList.filter(item => 
                    item.detailInfo?.metadata?.common?.enableStarforce === true
                ).length;
                console.log(`其中支持星力强化的装备: ${starforceCount} 个`);
                
            } else {
                throw new Error('预加载的道具数据不可用');
            }
        } catch (error) {
            console.error('加载装备数据库失败:', error);
            console.error('错误类型:', error.constructor.name);
            console.error('错误详情:', error.message);
            console.error('错误堆栈:', error.stack);
            
            // 如果预加载数据失败，使用测试数据作为备用
            console.log('预加载数据失败，使用测试道具数据...');
            this.createTestItemData();
        }
    }

    // 创建测试道具数据
    createTestItemData() {
        this.itemDatabase = {};
        this.itemList = [];
        
        // 创建一些测试道具
        const testItems = [
            { id: 1000009, name: "Red M-Forcer Helmet", category: 1000301001, enableStarforce: true },
            { id: 1001164, name: "黑色贝雷帽", category: 1000301001, enableStarforce: true },
            { id: 1072952, name: "黑色战斗靴", category: 1000301005, enableStarforce: true },
            { id: 1082169, name: "黑色工作手套", category: 1000301006, enableStarforce: true },
            { id: 1102149, name: "力量戒指", category: 1000301009, enableStarforce: true },
            { id: 1382104, name: "黑色风衣", category: 1000301004, enableStarforce: true },
            { id: 1702424, name: "黑色长剑", category: 1000301007, enableStarforce: true },
            { id: 1702952, name: "蓝色法杖", category: 1000301007, enableStarforce: true },
            { id: 1790342, name: "红色弓箭", category: 1000301007, enableStarforce: true },
            { id: 1791494, name: "绿色匕首", category: 1000301007, enableStarforce: true }
        ];
        
        testItems.forEach(testItem => {
            const item = {
                itemId: testItem.id,
                itemName: testItem.name,
                itemCategory: testItem.category,
                imageUrl: `https://api-static.msu.io/itemimages/icon/${testItem.id}.png`,
                detailInfo: {
                    metadata: {
                        common: {
                            itemName: testItem.name,
                            itemId: testItem.id,
                            enableStarforce: testItem.enableStarforce,
                            blockUpgradePotential: false,
                            blockUpgradeExtraOption: false,
                            level: Math.floor(Math.random() * 200) + 1
                        }
                    }
                }
            };
            
            this.itemDatabase[item.itemId] = {
                itemId: item.itemId,
                name: item.itemName,
                category: item.itemCategory,
                imageUrl: item.imageUrl,
                detailInfo: item.detailInfo
            };
            this.itemList.push({
                itemId: item.itemId,
                name: item.itemName,
                category: item.itemCategory,
                imageUrl: item.imageUrl,
                detailInfo: item.detailInfo
            });
        });
        
        console.log(`创建了${this.itemList.length}个测试道具`);
    }
    
    updateTestEquipNames() {
        if (!this.itemDatabase) return;
        
        this.testEquipItems.forEach(item => {
            const itemData = this.itemDatabase[parseInt(item.id)];
            if (itemData) {
                item.name = itemData.name;
                item.category = itemData.category;
            } else {
                item.name = `装备 ${item.id}`;
            }
        });
        
        // 如果当前有装备，更新显示
        if (this.currentEquip.itemId) {
            this.updateNotice();
        }
    }
    
    getItemInfo(itemId) {
        if (!this.itemDatabase) return null;
        return this.itemDatabase[parseInt(itemId)];
    }

    getItemLevelFromData(item) {
        // 尝试多个可能的路径来获取required信息
        let requiredData = null;
        
        // 路径1: 在item.detailInfo.metadata.required (正确路径)
        if (item.detailInfo?.metadata?.required) {
            requiredData = item.detailInfo.metadata.required;
        }
        // 路径2: 直接在item.required (备用路径)
        else if (item.required) {
            requiredData = item.required;
        }
        // 路径3: 在item.detailInfo.required (备用路径)
        else if (item.detailInfo?.required) {
            requiredData = item.detailInfo.required;
        }

        // 最优先：从required.level获取装备等级要求
        if (requiredData && requiredData.level !== undefined) {
            return requiredData.level;
        }
        
        // 次优先：从detailInfo中获取等级
        if (item.detailInfo && 
            item.detailInfo.metadata && 
            item.detailInfo.metadata.common && 
            item.detailInfo.metadata.common.level) {
            return item.detailInfo.metadata.common.level;
        }
        
        return 0; // 默认返回0表示无等级要求
    }

    getItemTypeName(category) {
        // 新增：如果传入的是完整的item对象，直接从metadata中获取类型
        if (category && typeof category === 'object' && category.detailInfo) {
            const categoryLabel = category.detailInfo?.metadata?.category?.label;
            if (categoryLabel) {
                // 返回完整的类型路径，如"Item > Armor > Armor > Hat"
                return categoryLabel;
            }
        }
        
        // 如果传入的是itemId，从数据库中查找
        if (typeof category === 'number' || typeof category === 'string') {
            const itemId = parseInt(category);
            const itemData = this.getItemInfo(itemId);
            if (itemData && itemData.detailInfo?.metadata?.category?.label) {
                return itemData.detailInfo.metadata.category.label;
            }
        }
        
        // 原有的映射逻辑作为备用
        const categoryMap = {
            // 战士装备
            1000201001: '战士帽子', 1000201002: '战士上衣', 1000201003: '战士下装', 
            1000201004: '战士套装', 1000201005: '战士鞋子', 1000201006: '战士手套', 
            1000201007: '战士武器', 1000201008: '战士盾牌', 1000201009: '战士饰品',
            
            // 普通装备
            1000301001: '帽子', 1000301002: '上衣', 1000301003: '下装', 
            1000301004: '套装', 1000301005: '鞋子', 1000301006: '手套', 
            1000301007: '披风', 1000301008: '脸部装饰', 1000301009: '饰品',
            1000301010: '腰带', 1000301011: '肩膀', 1000301012: '武器',
            1000301013: '徽章', 1000301014: '眼部装饰',
            
            // 外观装备
            1000302001: '眼睛', 1000302002: '发型', 1000302003: '皮肤',
            
            // 武器类
            1000101001: '单手剑', 1000101002: '单手斧', 1000101003: '单手钝器',
            1000101004: '匕首', 1000101005: '法杖', 1000101006: '短杖',
            1000101007: '双手剑', 1000101008: '双手斧', 1000101009: '双手钝器',
            1000101010: '长枪', 1000101011: '弓', 1000101012: '弩',
            1000101013: '拳套', 1000101014: '指虎', 1000101015: '枪',
            
            // 特殊道具
            1000401001: '宠物',
            1000501001: '椅子',
            1000502001: '坐骑',
            1000503001: '伤害皮肤',
            1000504001: '弓箭', 1000504002: '弩箭', 1000504003: '标', 1000504004: '子弹',
            
            // 默认分类（基于itemId前缀智能判断）
            100: '帽子', 102: '脸部装饰', 103: '眼部装饰', 104: '耳环', 105: '上衣',
            106: '下装', 107: '鞋子', 108: '手套', 109: '盾牌', 110: '披风',
            111: '戒指', 112: '项链', 113: '腰带', 114: '徽章', 115: '肩膀',
            130: '单手剑', 131: '单手斧', 132: '单手钝器', 133: '匕首',
            134: '双手剑', 135: '双手斧', 136: '双手钝器', 137: '长枪',
            138: '弓', 139: '弩', 140: '拳套', 141: '指虎', 142: '枪',
            143: '法杖', 144: '短杖', 145: '盾牌',
            170: '弓箭', 171: '标', 172: '子弹',
            180: '徽章', 190: '机械心脏', 301: '椅子', 302: '坐骑',
            313: '伤害皮肤', 314: '称号', 315: '弓箭', 316: '标', 317: '子弹',
            500: '宠物', 501: '宠物装备'
        };
        
        // 首先尝试精确匹配
        if (categoryMap[category]) {
            return categoryMap[category];
        }
        
        // 如果没有精确匹配，尝试基于itemId前缀判断（用于向下兼容）
        const categoryStr = String(category);
        if (categoryStr.length >= 7) {
            // 提取前三位数字作为类型判断
            const prefix = Math.floor(category / 10000);
            if (categoryMap[prefix]) {
                return categoryMap[prefix];
            }
        }
        
        return `道具类型${category}`;
    }

    async preloadImages() {
        // 收集所有需要预加载的UI图片资源
        const imagesToPreload = [
            // 主要UI背景
            'UIEquipEnchant.img/Main/Background.png',
            
            // 装备槽
            'UIEquipEnchant.img/Main/Equip/Slot/Starforce.png',
            'UIEquipEnchant.img/Main/Equip/Slot/Potential.png',
            'UIEquipEnchant.img/Main/Equip/Slot/Bonusstat.png',
            
            // 徽章
            'UIEquipEnchant.img/Main/Equip/Badge/0.png',
            'UIEquipEnchant.img/Main/Equip/Badge/1.png',
            'UIEquipEnchant.img/Main/Equip/Badge/2.png',
            
            // 通知框
            'UIEquipEnchant.img/Main/Equip/Notice/Background.png',
            
            // 信息面板
            'UIEquipEnchant.img/Main/Info/Starforce/Background.png',
            
            // Tab分割线
            'UIEquipEnchant.img/Main/Tab/Image_Line.png',
            
            // 标签页按钮 - 星力强化
            'UIEquipEnchant.img/Main/Tab/Starforce/normal/0.png',
            'UIEquipEnchant.img/Main/Tab/Starforce/selected/0.png',
            'UIEquipEnchant.img/Main/Tab/Starforce/disabled/0.png',
            
            // 标签页按钮 - 潜能
            'UIEquipEnchant.img/Main/Tab/Potential/normal/0.png',
            'UIEquipEnchant.img/Main/Tab/Potential/selected/0.png',
            'UIEquipEnchant.img/Main/Tab/Potential/disabled/0.png',
            
            // 标签页按钮 - 额外属性
            'UIEquipEnchant.img/Main/Tab/Bonusstat/normal/0.png',
            'UIEquipEnchant.img/Main/Tab/Bonusstat/selected/0.png',
            'UIEquipEnchant.img/Main/Tab/Bonusstat/disabled/0.png',
            
            // 主要按钮
            'UIEquipEnchant.img/Main/button_Enhance/normal/0.png',
            'UIEquipEnchant.img/Main/button_Enhance/mouseOver/0.png',
            'UIEquipEnchant.img/Main/button_Enhance/pressed/0.png',
            'UIEquipEnchant.img/Main/button_Enhance/disabled/0.png',
            
            'UIEquipEnchant.img/Main/button_Cancel/normal/0.png',
            'UIEquipEnchant.img/Main/button_Cancel/mouseOver/0.png',
            'UIEquipEnchant.img/Main/button_Cancel/pressed/0.png',
            
            // 顶部按钮
            'UIEquipEnchant.img/Main/button_Guide/normal/0.png',
            'UIEquipEnchant.img/Main/button_Guide/mouseOver/0.png',
            'UIEquipEnchant.img/Main/button_Guide/pressed/0.png',
            'UIEquipEnchant.img/Main/button_Guide/disabled/0.png',
            
            'UIEquipEnchant.img/Main/button_Close/normal/0.png',
            'UIEquipEnchant.img/Main/button_Close/mouseOver/0.png',
            'UIEquipEnchant.img/Main/button_Close/pressed/0.png',
            'UIEquipEnchant.img/Main/button_Close/disabled/0.png',
            
            // 迷你游戏
            'UIEquipEnchant.img/Common/miniGame/backgrnd.png',
            'UIEquipEnchant.img/Common/miniGame/button_stop/normal/0.png',
            'UIEquipEnchant.img/Common/miniGame/button_stop/mouseOver/0.png',
            'UIEquipEnchant.img/Common/miniGame/button_stop/pressed/0.png',
            
            // 弹窗
            'UIEquipEnchant.img/Common/dialog/popUp/backgrnd.png',
            'UIEquipEnchant.img/Common/dialog/popUp/button_confirm/normal/0.png',
            'UIEquipEnchant.img/Common/dialog/popUp/button_confirm/mouseOver/0.png',
            'UIEquipEnchant.img/Common/dialog/popUp/button_confirm/pressed/0.png',
            'UIEquipEnchant.img/Common/dialog/popUp/button_cancel/normal/0.png',
            'UIEquipEnchant.img/Common/dialog/popUp/button_cancel/mouseOver/0.png',
            'UIEquipEnchant.img/Common/dialog/popUp/button_cancel/pressed/0.png'
        ];

        // 预加载所有特效动画帧
        for (let i = 0; i < 16; i++) {
            imagesToPreload.push(`UIEquipEnchant.img/Main/Equip/Effect/Starforce/Standby/${i}.png`);
            imagesToPreload.push(`UIEquipEnchant.img/Main/Equip/Effect/Starforce/Progress/Loop/${i}.png`);
        }

        for (let i = 0; i < 18; i++) {
            imagesToPreload.push(`UIEquipEnchant.img/Main/Equip/Effect/Starforce/Result/Success/${i}.png`);
        }

        // 预加载图片
        return this.loadImageBatch(imagesToPreload, (loaded, total) => {
            const progressStart = 30;
            const progressEnd = 70;
            const progress = progressStart + (progressEnd - progressStart) * (loaded / total);
            this.updateLoadingProgress(Math.round(progress), `加载UI资源... (${loaded}/${total})`);
        });
    }

    async preloadEquipImages() {
        // 预加载测试装备的图片
        const equipImages = this.testEquipItems.map(item => `itempic/${item.id}.png`);
        
        return this.loadImageBatch(equipImages, (loaded, total) => {
            const progressStart = 70;
            const progressEnd = 90;
            const progress = progressStart + (progressEnd - progressStart) * (loaded / total);
            this.updateLoadingProgress(Math.round(progress), `加载装备图片... (${loaded}/${total})`);
        });
    }

    loadImageBatch(imageUrls, progressCallback) {
        return new Promise((resolve) => {
            const totalImages = imageUrls.length;
            let loadedImages = 0;
            let hasError = false;
            
            if (totalImages === 0) {
                resolve();
                return;
            }
            
            const checkComplete = () => {
                loadedImages++;
                if (progressCallback) {
                    progressCallback(loadedImages, totalImages);
                }
                
                if (loadedImages >= totalImages) {
                    resolve();
                }
            };
            
            imageUrls.forEach(src => {
                const img = new Image();
                
                img.onload = () => {
                    console.log(`✓ 图片加载成功: ${src}`);
                    checkComplete();
                };
                
                img.onerror = () => {
                    console.warn(`✗ 图片加载失败: ${src}`);
                    hasError = true;
                    checkComplete(); // 即使失败也继续
                };
                
                img.src = src;
            });
        });
    }

    bindEvents() {
        // 分层控制事件
        this.bindLayerControlEvents();
        
        // 标签页切换
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', (e) => {
                // 检查是否被禁用
                if (e.target.classList.contains('disabled')) {
                    e.preventDefault();
                    return;
                }
                
                // 检查是否已经是当前激活的标签页
                if (e.target.classList.contains('active')) {
                    return;
                }
                
                // 切换到新标签页
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 主要按钮
        document.getElementById('enhance-btn').addEventListener('click', () => {
            this.startEnhance();
        });

        document.getElementById('cancel-btn').addEventListener('click', () => {
            this.cancelEnhance();
        });

        // 顶部按钮
        document.getElementById('guide-btn').addEventListener('click', () => {
            this.showGuide();
        });

        document.getElementById('close-btn').addEventListener('click', () => {
            this.closeSimulator();
        });

        // 装备槽点击
        document.getElementById('equip-slot-bg').addEventListener('click', () => {
            this.selectEquip();
        });

        document.getElementById('equip-item').addEventListener('click', () => {
            this.selectEquip();
        });
        
        // 装备槽右键清除装备
        document.getElementById('equip-slot-bg').addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.clearEquip();
        });

        document.getElementById('equip-item').addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.clearEquip();
        });

        // 弹窗按钮
        document.getElementById('popup-confirm').addEventListener('click', () => {
            this.confirmEnhance();
        });

        document.getElementById('popup-cancel').addEventListener('click', () => {
            this.hidePopup();
        });

        document.getElementById('result-ok').addEventListener('click', () => {
            this.hideResult();
        });

        // 迷你游戏停止按钮
        document.getElementById('stop-btn').addEventListener('click', () => {
            this.stopMinigame();
        });

        // 镇护选项
        document.getElementById('starcatch-checkbox').addEventListener('change', () => {
            this.updateUI();
        });

        document.getElementById('prevent-checkbox').addEventListener('change', () => {
            this.updateUI();
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && this.minigameActive) {
                e.preventDefault();
                this.stopMinigame();
            }
        });
    }

    bindLayerControlEvents() {
        // 绑定每个层级复选框的事件
        const layerCheckboxes = document.querySelectorAll('.layer-controls input[type="checkbox"]');
        layerCheckboxes.forEach(checkbox => {
            // 跳过道具选择器的复选框，单独处理
            if (checkbox.id === 'item-selector-toggle') return;
            
            checkbox.addEventListener('change', (e) => {
                const layerNumber = e.target.id.replace('layer-', '');
                this.toggleLayer(layerNumber, e.target.checked);
            });
        });

        // 道具选择器显示控制
        const itemSelectorToggle = document.getElementById('item-selector-toggle');
        if (itemSelectorToggle) {
            itemSelectorToggle.addEventListener('change', (e) => {
                const itemSelector = document.getElementById('item-selector');
                if (itemSelector) {
                    itemSelector.style.display = e.target.checked ? 'block' : 'none';
                }
            });
        }

        // 显示全部按钮
        document.getElementById('show-all-layers').addEventListener('click', () => {
            this.showAllLayers();
        });

        // 隐藏全部按钮
        document.getElementById('hide-all-layers').addEventListener('click', () => {
            this.hideAllLayers();
        });
    }

    toggleLayer(layerNumber, visible) {
        const elements = document.querySelectorAll(`.layer-z-${layerNumber}`);
        elements.forEach(element => {
            if (visible) {
                element.classList.remove('layer-hidden');
            } else {
                element.classList.add('layer-hidden');
            }
        });
        
        console.log(`Z-${layerNumber}层 ${visible ? '显示' : '隐藏'}, 影响${elements.length}个元素`);
    }

    showAllLayers() {
        const layerCheckboxes = document.querySelectorAll('.layer-controls input[type="checkbox"]');
        layerCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            if (checkbox.id === 'item-selector-toggle') {
                // 道具选择器控制
                const itemSelector = document.getElementById('item-selector');
                if (itemSelector) {
                    itemSelector.style.display = 'block';
                }
            } else {
                // 普通层级控制
                const layerNumber = checkbox.id.replace('layer-', '');
                this.toggleLayer(layerNumber, true);
            }
        });
    }

    hideAllLayers() {
        const layerCheckboxes = document.querySelectorAll('.layer-controls input[type="checkbox"]');
        layerCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            if (checkbox.id === 'item-selector-toggle') {
                // 道具选择器控制
                const itemSelector = document.getElementById('item-selector');
                if (itemSelector) {
                    itemSelector.style.display = 'none';
                }
            } else {
                // 普通层级控制
                const layerNumber = checkbox.id.replace('layer-', '');
                this.toggleLayer(layerNumber, false);
            }
        });
    }

    switchTab(tab) {
        if (this.enhancing) return; // 强化中不允许切换
        
        console.log(`切换到${tab}页面`);
        this.currentTab = tab;
        
        // 更新标签页样式
        this.updateTabStates(tab);

        // 更新装备槽样式
        const slotBackground = document.getElementById('equip-slot-bg');
        if (slotBackground) {
            slotBackground.className = `equip-slot-background layer-z-2 ${tab}`;
        }

        // 更新UI内容
        this.updateUI();
        this.updateNotice();
        this.stopAllEffects(); // 停止当前特效
        
        // 只有星力强化才播放特效
        if (tab === 'starforce') {
            setTimeout(() => {
                this.playStandbyEffect();
            }, 300);
        }
        
        // 重新渲染道具列表，显示当前页面支持的道具
        if (this.itemList && this.itemList.length > 0) {
            this.renderItemList();
        }
    }

    updateTabStates(activeTab) {
        const tabs = ['starforce', 'potential', 'bonusstat'];
        
        tabs.forEach(tab => {
            const tabElement = document.querySelector(`[data-tab="${tab}"]`);
            if (!tabElement) return;
            
            // 清除所有状态类
            tabElement.classList.remove('active', 'disabled');
            
            if (tab === activeTab) {
                // 激活状态
                tabElement.classList.add('active');
            } else {
                // 检查是否应该禁用
                const shouldDisable = this.shouldDisableTab(tab);
                if (shouldDisable) {
                    tabElement.classList.add('disabled');
                }
                // 否则保持可选择状态（CSS默认处理）
            }
        });
    }

    shouldDisableTab(tab) {
        // 根据业务逻辑决定是否禁用标签页
        // 例如：没有装备时禁用某些功能，或者特定条件下禁用
        
        switch (tab) {
            case 'starforce':
                // 星力强化：当前无装备或正在强化时禁用其他选项
                return this.enhancing && this.currentTab !== 'starforce';
                
            case 'potential':
                // 潜能重设：可以根据装备类型或其他条件禁用
                return this.enhancing && this.currentTab !== 'potential';
                
            case 'bonusstat':
                // 额外属性：可以根据装备等级或其他条件禁用
                return this.enhancing && this.currentTab !== 'bonusstat';
                
            default:
                return false;
        }
    }

    setTabEnabled(tab, enabled) {
        const tabElement = document.querySelector(`[data-tab="${tab}"]`);
        if (!tabElement) return;
        
        if (enabled) {
            tabElement.classList.remove('disabled');
        } else {
            tabElement.classList.add('disabled');
        }
    }

    setAllTabsEnabled(enabled) {
        const tabs = ['starforce', 'potential', 'bonusstat'];
        tabs.forEach(tab => {
            this.setTabEnabled(tab, enabled);
        });
    }

    updateUI() {
        this.updateProbabilities();
        this.updateCosts();
        this.updateEquipBadge();
        this.updateStarProgress();
        this.updateStats();
        this.updateButtons();
    }

    updateProbabilities() {
        if (this.currentTab === 'starforce') {
            const probs = this.starforceProbabilities[this.equipLevel] || 
                         { success: 0, failure: 0, major_failure: 0, failure_drop: 0 };
            
            // 获取镇护选项状态
            const starcatchEnabled = document.getElementById('starcatch-checkbox').checked;
            const preventEnabled = document.getElementById('prevent-checkbox').checked;
            
            // 调整概率（如果启用镇护）
            let adjustedProbs = { ...probs };
            if (preventEnabled && probs.major_failure > 0) {
                adjustedProbs.failure += adjustedProbs.major_failure;
                adjustedProbs.major_failure = 0;
            }
            
            document.getElementById('success-rate').textContent = `${adjustedProbs.success.toFixed(2)}%`;
            document.getElementById('failure-rate').textContent = `${adjustedProbs.failure.toFixed(2)}%`;
            document.getElementById('major-failure-rate').textContent = `${adjustedProbs.major_failure.toFixed(2)}%`;
            document.getElementById('failure-drop-rate').textContent = `${adjustedProbs.failure_drop.toFixed(2)}%`;
        } else {
            // 潜能和额外属性的概率
            document.getElementById('success-rate').textContent = '100.00%';
            document.getElementById('failure-rate').textContent = '0.00%';
            document.getElementById('major-failure-rate').textContent = '0.00%';
            document.getElementById('failure-drop-rate').textContent = '0.00%';
        }
    }

    updateCosts() {
        if (this.currentTab === 'starforce') {
            const cost = this.enhanceCosts[this.equipLevel] || 0;
            document.getElementById('required-cost').textContent = this.formatNumber(cost);
        } else {
            document.getElementById('required-cost').textContent = '500,000';
        }
    }

    updateEquipBadge() {
        const badge = document.getElementById('equip-badge');
        const badgeText = document.querySelector('.badge-text');
        
        badgeText.textContent = this.equipLevel;
        
        // 更新徽章图片
        badge.className = 'equip-badge layer-z-3';
        if (this.equipLevel >= 15) {
            badge.classList.add('level-2');
        } else if (this.equipLevel >= 10) {
            badge.classList.add('level-1');
        } else {
            badge.classList.add('level-0');
        }
    }

    updateStarProgress() {
        document.getElementById('current-star').textContent = this.equipLevel;
        document.getElementById('next-star').textContent = Math.min(this.equipLevel + 1, this.equipMaxLevel);
    }

    updateStats() {
        // 根据星力等级计算属性增长
        const currentStats = this.calculateStats(this.equipLevel);
        const nextStats = this.calculateStats(this.equipLevel + 1);
        
        // 更新属性显示
        const statItems = document.querySelectorAll('.stat-item');
        const statTypes = ['STR', 'DEX', 'INT', 'LUK', 'MaxHP', 'DEF'];
        
        statItems.forEach((item, index) => {
            if (index < statTypes.length) {
                const statType = statTypes[index];
                const currentValue = currentStats[statType] || 0;
                const nextValue = nextStats[statType] || 0;
                
                item.querySelector('.stat-current').textContent = currentValue;
                item.querySelector('.stat-next').textContent = nextValue;
            }
        });
    }

    calculateStats(level) {
        // 简化的属性计算（每级+2主属性，+5HP，+6防御）
        return {
            STR: level * 2,
            DEX: level * 2,
            INT: level * 2,
            LUK: level * 2,
            MaxHP: level * 5,
            DEF: level * 6
        };
    }

    updateButtons() {
        const enhanceBtn = document.getElementById('enhance-btn');
        if (this.enhancing) {
            enhanceBtn.classList.add('disabled');
        } else {
            enhanceBtn.classList.remove('disabled');
        }
        
        if (this.equipLevel >= this.equipMaxLevel && this.currentTab === 'starforce') {
            enhanceBtn.classList.add('disabled');
        }
        
        // 更新标签页状态
        this.updateTabStates(this.currentTab);
    }

    updateNotice() {
        const noticeContent = document.getElementById('equip-notice').querySelector('span');
        let message = '';
        
        if (this.currentEquip.itemId) {
            // 如果有装备，显示装备信息
            const itemData = this.getItemInfo(this.currentEquip.itemId);
            let typeName = '未知类型';
            
            if (itemData) {
                // 传递完整的itemData对象来获取正确的类型名称
                typeName = this.getItemTypeName(itemData);
            } else {
                // 如果没有详细数据，使用category作为备用
                typeName = this.getItemTypeName(this.currentEquip.category);
            }
            
            message = `${this.currentEquip.name} [${typeName}] (ID: ${this.currentEquip.itemId})`;
        } else {
            // 如果没有装备，显示默认提示
            // 潜能和额外属性总是成功
            return { 
                type: 'success', 
                message: `${this.currentTab === 'potential' ? '潜能重设' : '额外属性强化'}成功！`,
                level: this.equipLevel
            };
        }
    }

    showEnhanceResult(result) {
        const resultOverlay = document.getElementById('result-overlay');
        const resultContent = document.getElementById('result-content');
        const resultMessage = document.getElementById('result-message');
        
        resultMessage.textContent = result.message;
        resultOverlay.style.display = 'block';
        resultContent.style.display = 'flex';
        resultOverlay.classList.add('fade-in');
        resultContent.classList.add('fade-in');
        
        // 播放相应的动画效果
        const equipSlot = document.getElementById('equip-slot-bg');
        if (result.type === 'success') {
            equipSlot.classList.add('enhance-success');
        } else {
            equipSlot.classList.add('enhance-fail');
        }
        
        setTimeout(() => {
            equipSlot.classList.remove('enhance-success', 'enhance-fail');
        }, 600);
    }

    // 特效动画系统
    playStandbyEffect() {
        if (this.currentTab !== 'starforce' || this.enhancing) return;
        
        this.currentEffectType = 'standby';
        this.effectAnimationFrame = 0;
        
        const starforceEffect = document.getElementById('starforce-effect');
        starforceEffect.classList.add('active');
        
        this.animateEffect();
    }

    playProgressEffect() {
        if (this.currentTab !== 'starforce') return;
        
        this.stopAllEffects();
        this.currentEffectType = 'progress';
        this.effectAnimationFrame = 0;
        
        const starforceEffect = document.getElementById('starforce-effect');
        starforceEffect.classList.add('active');
        
        this.animateEffect();
    }

    playResultEffect(resultType) {
        if (this.currentTab !== 'starforce') return;
        
        this.stopAllEffects();
        this.currentEffectType = resultType === 'success' ? 'success' : 'failed';
        this.effectAnimationFrame = 0;
        
        const starforceEffect = document.getElementById('starforce-effect');
        starforceEffect.classList.add('active');
        
        // 结果特效只播放一次，不循环
        this.animateEffect(false);
    }

    animateEffect(loop = true) {
        if (!this.enhancing && this.currentEffectType !== 'standby') return;
        
        const starforceEffect = document.getElementById('starforce-effect');
        const maxFrames = this.maxFrames[this.currentEffectType];
        
        // 如果是第一帧，记录开始时间
        if (!this.lastFrameTime) {
            this.lastFrameTime = performance.now();
        }
        
        // 清除之前的效果类
        this.clearEffectClasses(starforceEffect);
        
        // 添加当前帧的效果类
        starforceEffect.classList.add(`effect-${this.currentEffectType}-${this.effectAnimationFrame}`);
        
        this.effectAnimationFrame++;
        
        // 检查是否需要循环
        if (this.effectAnimationFrame >= maxFrames) {
            if (loop && (this.currentEffectType === 'standby' || this.currentEffectType === 'progress')) {
                this.effectAnimationFrame = 0;
            } else {
                // 结果特效播放完毕，停止动画
                this.lastFrameTime = null;
                return;
            }
        }
        
        // 使用requestAnimationFrame确保稳定的帧率
        const frameInterval = 90; // XML: delay="90" - 90ms/帧
        
        const nextFrame = () => {
            const currentTime = performance.now();
            const elapsed = currentTime - this.lastFrameTime;
            
            if (elapsed >= frameInterval) {
                this.lastFrameTime = currentTime;
                this.animateEffect(loop);
            } else {
                // 如果时间还没到，继续等待
                this.effectAnimationTimer = requestAnimationFrame(nextFrame);
            }
        };
        
        this.effectAnimationTimer = requestAnimationFrame(nextFrame);
    }

    clearEffectClasses(element) {
        // 移除所有特效类
        const effectTypes = ['standby', 'progress', 'success', 'failed'];
        effectTypes.forEach(type => {
            const maxFrame = this.maxFrames[type];
            for (let i = 0; i < maxFrame; i++) {
                element.classList.remove(`effect-${type}-${i}`);
            }
        });
    }

    stopAllEffects() {
        if (this.effectAnimationTimer) {
            cancelAnimationFrame(this.effectAnimationTimer);
            this.effectAnimationTimer = null;
        }
        
        // 重置时间戳
        this.lastFrameTime = null;
        
        const starforceEffect = document.getElementById('starforce-effect');
        starforceEffect.classList.remove('active');
        
        // 清除所有效果类
        this.clearEffectClasses(starforceEffect);
    }

    cancelEnhance() {
        this.enhancing = false;
        this.minigameActive = false;
        
        // 重置UI状态
        document.getElementById('minigame-overlay').style.display = 'none';
        document.getElementById('minigame-content').style.display = 'none';
        
        this.updateButtons();
        this.stopAllEffects();
        
        // 恢复待机特效
        if (this.currentTab === 'starforce') {
            setTimeout(() => {
                this.playStandbyEffect();
            }, 300);
        }
    }

    selectEquip() {
        // 获取当前筛选的道具列表
        const filter = this.getItemFilter();
        const filteredItems = this.itemList ? this.itemList.filter(filter.condition) : [];
        
        if (filteredItems.length === 0) {
            console.log('没有符合条件的道具可以选择');
            // 如果没有符合条件的道具，使用测试装备
            const randomEquip = this.testEquipItems[Math.floor(Math.random() * this.testEquipItems.length)];
            this.setEquip(randomEquip.id, randomEquip.name, randomEquip.type);
            return;
        }
        
        // 从符合条件的道具中随机选择
        const randomItem = filteredItems[Math.floor(Math.random() * filteredItems.length)];
        this.setEquip(randomItem.itemId, randomItem.itemName || randomItem.name, randomItem.category);
        
        console.log(`从${filteredItems.length}个符合条件的道具中随机选择了: ${randomItem.itemName || randomItem.name}`);
    }
    
    setEquip(itemId, name = '', type = '') {
        // 从数据库获取装备信息
        const itemInfo = this.getItemInfo(itemId);
        
        // 设置当前装备信息
        this.currentEquip.itemId = itemId;
        this.currentEquip.name = itemInfo ? itemInfo.name : (name || `装备 ${itemId}`);
        this.currentEquip.type = type;
        this.currentEquip.category = itemInfo ? itemInfo.category : '';
        
        // 控制台输出装备信息（调试用）
        if (itemInfo) {
            console.log(`装备装载成功: ${itemInfo.name} [${this.getItemTypeName(itemInfo.category)}] (ID: ${itemId})`);
        } else {
            console.log(`装备装载: ${name || `装备 ${itemId}`} (ID: ${itemId}) - 未在数据库中找到`);
        }
        
        // 更新装备UI显示
        const equipItem = document.getElementById('equip-item');
        equipItem.innerHTML = ''; // 清空内容
        equipItem.classList.add('has-equip');
        
        // 根据XML定义设置正确的装备容器位置和尺寸
        // XML: EquipSlot(209,168) vs Equip(206,167) = 偏移(-3,-1)
        // 装备槽90x90，装备容器64x64，居中偏移 (90-64)/2 = 13px
        equipItem.style.cssText = `
            position: absolute !important;
            top: 181px !important;
            left: 222px !important;
            width: 64px !important;
            height: 64px !important;
            border: none !important;
            background-color: transparent !important;
            display: grid !important;
            place-items: center !important;
            padding: 0 !important;
            margin: 0 !important;
            box-sizing: border-box !important;
            z-index: 8 !important;
            transition: none !important;
            animation: none !important;
        `;
        
        // 创建img元素来显示装备图片
        const imgElement = document.createElement('img');
        imgElement.src = `itempic/${itemId}.png`;
        imgElement.alt = this.currentEquip.name;
        
        // 设置图片样式 - 根据90x90容器调整图片大小为64x64（80%大小）
        imgElement.style.cssText = `
            width: 64px !important;
            height: 64px !important;
            border: none !important;
            padding: 0 !important;
            margin: 0 !important;
            display: block !important;
            transition: none !important;
            animation: none !important;
        `;
        
        // 图片加载完成后输出调试信息
        imgElement.onload = () => {
            console.log(`装备图片加载成功: itempic/${itemId}.png`);
            console.log('图片尺寸:', imgElement.naturalWidth, 'x', imgElement.naturalHeight);
            console.log('显示尺寸: 64x64 (拉伸)');
        };
        
        // 添加图片加载错误处理
        imgElement.onerror = () => {
            console.error(`装备图片加载失败: itempic/${itemId}.png`);
            imgElement.remove();
            equipItem.innerHTML = '?';
            equipItem.style.cssText += `
                font-size: 24px !important;
                color: #ff6666 !important;
                text-align: center !important;
                line-height: 90px !important;
            `;
        };
        
        // 将图片添加到装备槽
        equipItem.appendChild(imgElement);
        
        // 设置装备属性（保留用于CSS样式）
        equipItem.setAttribute('data-item-id', itemId);
        
        // 更新通知信息
        this.updateNotice();
        
        // 只在没有特效播放或者特效不是待机状态时才开始播放待机特效
        if (this.currentTab === 'starforce') {
            const isStandbyActive = this.currentEffectType === 'standby' && this.effectAnimationTimer;
            if (!isStandbyActive) {
                setTimeout(() => {
                    this.playStandbyEffect();
                }, 300);
            }
        }
        
        // 更新道具选择状态
        this.updateItemSelection(itemId);
    }
    
    clearEquip() {
        // 清除装备
        this.currentEquip.itemId = null;
        this.currentEquip.name = '';
        this.currentEquip.type = '';
        this.currentEquip.category = '';
        
        const equipItem = document.getElementById('equip-item');
        equipItem.innerHTML = '+'; // 恢复添加标识
        equipItem.classList.remove('has-equip');
        equipItem.removeAttribute('data-item-id');
        
        // 重置为默认样式
        equipItem.style.cssText = `
            position: absolute !important;
            top: 181px !important;
            left: 222px !important;
            width: 64px !important;
            height: 64px !important;
            border: 2px dashed rgba(255, 255, 255, 0.3) !important;
            border-radius: 6px !important;
            background-color: transparent !important;
            padding: 0 !important;
            margin: 0 !important;
            box-sizing: border-box !important;
            z-index: 8 !important;
            font-size: 24px !important;
            color: #ffffff !important;
            cursor: pointer !important;
        `;
        
        this.updateNotice();
        this.stopAllEffects();
        
        // 清除道具选择状态
        this.updateItemSelection(null);
    }

    showPopup(message) {
        const popupOverlay = document.getElementById('popup-overlay');
        const popupContent = document.getElementById('popup-content');
        const popupMessage = document.getElementById('popup-message');
        
        popupMessage.textContent = message;
        popupOverlay.style.display = 'block';
        popupContent.style.display = 'flex';
        popupOverlay.classList.add('fade-in');
        popupContent.classList.add('fade-in');
    }

    hidePopup() {
        const popupOverlay = document.getElementById('popup-overlay');
        const popupContent = document.getElementById('popup-content');
        popupOverlay.classList.remove('fade-in');
        popupContent.classList.remove('fade-in');
        popupOverlay.classList.add('fade-out');
        popupContent.classList.add('fade-out');
        
        setTimeout(() => {
            popupOverlay.style.display = 'none';
            popupContent.style.display = 'none';
            popupOverlay.classList.remove('fade-out');
            popupContent.classList.remove('fade-out');
        }, 300);
    }

    hideResult() {
        const resultOverlay = document.getElementById('result-overlay');
        const resultContent = document.getElementById('result-content');
        resultOverlay.classList.remove('fade-in');
        resultContent.classList.remove('fade-in');
        resultOverlay.classList.add('fade-out');
        resultContent.classList.add('fade-out');
        
        setTimeout(() => {
            resultOverlay.style.display = 'none';
            resultContent.style.display = 'none';
            resultOverlay.classList.remove('fade-out');
            resultContent.classList.remove('fade-out');
        }, 300);
    }

    showGuide() {
        alert('游戏指南：\n1. 选择强化类型（星力/潜能/额外属性）\n2. 点击装备槽放入装备（左键随机选择，右键清除）\n3. 点击强化按钮\n4. 高等级星力强化需要迷你游戏\n5. 可以启用镇护功能\n\n装备功能：\n- 左键点击装备槽：随机选择装备\n- 右键点击装备槽：清除当前装备\n- E键：随机选择装备\n- R键：清除装备\n- 装备名称从itemList.json自动加载\n- 支持装备类型识别和显示\n\n特效说明：\n- 待机特效：装备槽周围的持续光效\n- 强化特效：强化过程中的动画\n- 结果特效：强化成功/失败的特效\n\n分层控制：\n- 使用左上角的分层控制面板可以显示/隐藏不同的UI层级\n- 每个层级都是独立的，方便调试和观察\n\n快捷键：\n- 1/2/3：切换标签页\n- Enter：开始强化\n- Escape：取消强化\n- E：随机选择装备\n- R：清除装备\n- Space：迷你游戏中停止');
    }

    closeSimulator() {
        if (confirm('确定要关闭装备强化模拟器吗？')) {
            // 清理定时器
            this.stopAllEffects();
            window.close();
        }
    }

    initializeEffects() {
        // 初始化特效系统
        setTimeout(() => {
            if (this.currentTab === 'starforce') {
                this.playStandbyEffect();
            }
        }, 1000); // 延迟1秒开始播放，确保图片加载完成
    }

    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    initItemSelector() {
        console.log('初始化道具选择器...');
        console.log('当前数据状态 - itemList长度:', this.itemList ? this.itemList.length : '未加载');
        console.log('当前标签页:', this.currentTab);
        
        // 初始化道具选择器
        this.renderItemList();
        this.bindItemSelectorEvents();
        
        console.log('道具选择器初始化完成');
    }

    // 获取当前页面需要的道具筛选条件
    getItemFilter() {
        switch (this.currentTab) {
            case 'starforce':
                return {
                    condition: (item) => {
                        // 严格检查enableStarforce为true
                        if (item.detailInfo && 
                            item.detailInfo.metadata && 
                            item.detailInfo.metadata.common) {
                            const common = item.detailInfo.metadata.common;
                            // 现金道具不能进行星力强化
                            if (common.isCashItem === true) {
                                return false;
                            }
                            return common.enableStarforce === true;
                        }
                        return false; // 没有详细信息时不支持
                    },
                    displayName: '星力强化道具'
                };
            case 'potential':
                return {
                    condition: (item) => {
                        // 如果没有明确禁用潜能，就认为支持
                        if (!item.detailInfo || !item.detailInfo.metadata || !item.detailInfo.metadata.common) {
                            return true;
                        }
                        const common = item.detailInfo.metadata.common;
                        // 现金道具不能进行潜能重设
                        if (common.isCashItem === true) {
                            return false;
                        }
                        return common.blockUpgradePotential !== true;
                    },
                    displayName: '潜能重设道具'
                };
            case 'bonusstat':
                return {
                    condition: (item) => {
                        // 如果没有明确禁用额外属性，就认为支持
                        if (!item.detailInfo || !item.detailInfo.metadata || !item.detailInfo.metadata.common) {
                            return true;
                        }
                        const common = item.detailInfo.metadata.common;
                        // 现金道具不能进行额外属性强化
                        if (common.isCashItem === true) {
                            return false;
                        }
                        return common.blockUpgradeExtraOption !== true;
                    },
                    displayName: '额外属性道具'
                };
            default:
                return {
                    condition: (item) => {
                        // 默认情况下也过滤掉现金道具
                        if (item.detailInfo && 
                            item.detailInfo.metadata && 
                            item.detailInfo.metadata.common && 
                            item.detailInfo.metadata.common.isCashItem === true) {
                            return false;
                        }
                        return true;
                    },
                    displayName: '全部道具'
                };
        }
    }

    // 渲染道具列表
    renderItemList() {
        console.log('开始渲染道具列表...');
        
        if (!this.itemList || this.itemList.length === 0) {
            console.log('道具列表未加载或为空');
            return;
        }

        console.log(`总共有${this.itemList.length}个道具`);

        const filter = this.getItemFilter();
        console.log(`当前筛选条件: ${filter.displayName}`);
        
        const filteredItems = this.itemList.filter(filter.condition);
        console.log(`筛选后有${filteredItems.length}个道具`);

        // 按等级排序
        filteredItems.sort((a, b) => {
            const levelA = this.getItemLevel(a);
            const levelB = this.getItemLevel(b);
            return levelA - levelB;
        });

        // 更新筛选信息显示
        const filterInfo = document.getElementById('filter-info');
        if (filterInfo) {
            filterInfo.textContent = `${filter.displayName} (${filteredItems.length}个)`;
        }

        // 渲染道具图标
        const itemGrid = document.getElementById('item-grid');
        if (!itemGrid) {
            console.log('找不到item-grid元素');
            return;
        }

        itemGrid.innerHTML = '';

        // 显示全部道具，移除限制
        const itemsToShow = filteredItems;

        itemsToShow.forEach((item, index) => {
            const itemIcon = this.createItemIcon(item);
            itemGrid.appendChild(itemIcon);
        });

        console.log(`已显示${itemsToShow.length}个道具图标`);
    }

    // 创建道具图标元素
    createItemIcon(item) {
        const iconDiv = document.createElement('div');
        iconDiv.className = 'item-icon';
        iconDiv.dataset.itemId = item.itemId;

        // 道具图片
        const img = document.createElement('img');
        // 使用item.imageUrl或fallback到本地图片
        const imageUrl = item.imageUrl || `itempic/${item.itemId}.png`;
        
        img.src = imageUrl;
        img.alt = item.itemName || item.name;
        
        img.onerror = () => {
            // 图片加载失败时的处理
            img.style.display = 'none';
            iconDiv.textContent = item.itemName ? item.itemName.charAt(0) : '?';
            iconDiv.style.fontSize = '12px';
            iconDiv.style.color = '#fff';
            iconDiv.style.textAlign = 'center';
            iconDiv.style.lineHeight = '40px';
        };

        iconDiv.appendChild(img);

        // 等级徽章
        const level = this.getItemLevel(item);
        if (level > 0) {
            const levelBadge = document.createElement('div');
            levelBadge.className = 'item-level-badge';
            levelBadge.textContent = level.toString();
            iconDiv.appendChild(levelBadge);
        }

        // 如果是当前选中的装备，添加选中样式
        if (this.currentEquip.itemId == item.itemId) {
            iconDiv.classList.add('selected');
        }

        return iconDiv;
    }

    // 获取道具等级
    getItemLevel(item) {
        // 尝试多个可能的路径来获取required信息
        let requiredData = null;
        
        // 路径1: 在item.detailInfo.metadata.required (正确路径)
        if (item.detailInfo?.metadata?.required) {
            requiredData = item.detailInfo.metadata.required;
        }
        // 路径2: 直接在item.required (备用路径)
        else if (item.required) {
            requiredData = item.required;
        }
        // 路径3: 在item.detailInfo.required (备用路径)
        else if (item.detailInfo?.required) {
            requiredData = item.detailInfo.required;
        }

        // 最优先：从required.level获取装备等级要求
        if (requiredData && requiredData.level !== undefined) {
            return requiredData.level;
        }
        
        // 次优先：从detailInfo中获取等级
        if (item.detailInfo && 
            item.detailInfo.metadata && 
            item.detailInfo.metadata.common && 
            item.detailInfo.metadata.common.level) {
            return item.detailInfo.metadata.common.level;
        }
        
        // 如果没有级别信息，尝试根据itemId智能推断等级
        const itemId = parseInt(item.itemId);
        
        // 基于itemId范围的等级推断（MapleStory的一般规律）
        if (itemId >= 1000000 && itemId < 1010000) {
            // 帽子类：1000000-1009999
            return Math.floor((itemId - 1000000) / 1000) * 10;
        } else if (itemId >= 1010000 && itemId < 1020000) {
            // 脸部装饰：1010000-1019999
            return Math.floor((itemId - 1010000) / 100) * 5;
        } else if (itemId >= 1050000 && itemId < 1060000) {
            // 上衣：1050000-1059999
            return Math.floor((itemId - 1050000) / 1000) * 10 + 10;
        } else if (itemId >= 1060000 && itemId < 1070000) {
            // 下装：1060000-1069999
            return Math.floor((itemId - 1060000) / 1000) * 10 + 15;
        } else if (itemId >= 1070000 && itemId < 1080000) {
            // 鞋子：1070000-1079999
            return Math.floor((itemId - 1070000) / 1000) * 10 + 20;
        } else if (itemId >= 1080000 && itemId < 1090000) {
            // 手套：1080000-1089999
            return Math.floor((itemId - 1080000) / 1000) * 10 + 25;
        } else if (itemId >= 1100000 && itemId < 1110000) {
            // 披风：1100000-1109999
            return Math.floor((itemId - 1100000) / 1000) * 10 + 30;
        } else if (itemId >= 1110000 && itemId < 1120000) {
            // 戒指：1110000-1119999
            return Math.floor((itemId - 1110000) / 1000) * 10 + 35;
        } else if (itemId >= 1300000 && itemId < 1800000) {
            // 武器类：1300000-1799999
            const weaponBase = Math.floor((itemId - 1300000) / 10000);
            return Math.min(weaponBase * 5 + 10, 200);
        } else if (itemId >= 3000000) {
            // 消耗品等：通常等级较低或无等级要求
            return 1;
        } else {
            // 其他装备：基于ID尾数推断
            const lastDigits = itemId % 1000;
            return Math.floor(lastDigits / 10) + 1;
        }
    }

    // 绑定道具选择器事件
    bindItemSelectorEvents() {
        const itemGrid = document.getElementById('item-grid');
        const tooltip = document.getElementById('item-tooltip');

        if (!itemGrid || !tooltip) return;

        // 道具点击事件
        itemGrid.addEventListener('click', (e) => {
            const itemIcon = e.target.closest('.item-icon');
            if (!itemIcon) return;

            const itemId = itemIcon.dataset.itemId;
            const item = this.itemDatabase[itemId];
            if (item) {
                this.setEquipFromSelector(itemId, item.name);
                this.updateItemSelection(itemId);
            }
        });

        // 道具悬停事件
        itemGrid.addEventListener('mouseover', (e) => {
            const itemIcon = e.target.closest('.item-icon');
            if (!itemIcon) return;

            const itemId = itemIcon.dataset.itemId;
            const item = this.itemDatabase[itemId];
            if (item) {
                this.showItemTooltip(item, itemIcon);
            }
        });

        itemGrid.addEventListener('mouseout', (e) => {
            const itemIcon = e.target.closest('.item-icon');
            if (!itemIcon) return;

            this.hideItemTooltip();
        });
    }

    // 显示道具详情提示框
    showItemTooltip(item, iconElement) {
        const tooltip = document.getElementById('item-tooltip');
        if (!tooltip) return;

        // 调试：输出完整的item对象结构
        console.log('=== 道具信息调试 ===');
        console.log('Item ID:', item.itemId);
        console.log('Item Name:', item.itemName || item.name);
        console.log('完整item对象:', item);
        console.log('==================');

        // 更新提示框内容
        const titleEl = document.getElementById('tooltip-title');
        const itemIdEl = document.querySelector('.tooltip-item-id');
        const levelEl = document.getElementById('tooltip-level');
        const categoryEl = document.getElementById('tooltip-category');
        const cashItemEl = document.getElementById('tooltip-cash-item');
        const starforceEl = document.getElementById('feature-starforce');
        const potentialEl = document.getElementById('feature-potential');
        const extraOptionEl = document.getElementById('feature-extraoption');

        // 属性要求元素
        const strEl = document.getElementById('requirement-str');
        const dexEl = document.getElementById('requirement-dex');
        const intEl = document.getElementById('requirement-int');
        const lukEl = document.getElementById('requirement-luk');
        const jobEl = document.getElementById('requirement-job');

        // 装备名称和基本信息
        if (titleEl) titleEl.textContent = item.itemName || item.name || '未知道具';
        if (itemIdEl) itemIdEl.textContent = `#${item.itemId} (装备道具)`;
        
        // 需求等级 - 原版格式
        const requiredLevel = this.getItemLevel(item);
        if (levelEl) levelEl.textContent = `REQ LEV: ${requiredLevel}`;
        
        // 装备类型 - 原版格式
        const categoryLabel = this.getItemTypeName(item);
        if (categoryEl) categoryEl.textContent = `Equipment Type: ${categoryLabel}`;

        // 现金道具状态
        const isCash = item.detailInfo?.metadata?.common?.isCashItem === true;
        if (cashItemEl) {
            if (isCash) {
                cashItemEl.textContent = '现金道具 (无法强化)';
                cashItemEl.style.color = '#ff8888';
            } else {
                cashItemEl.textContent = '装备道具';
                cashItemEl.style.color = '#88ff88';
            }
        }

        // 尝试多个可能的路径来获取required信息
        let requiredData = null;
        
        // 路径1: 在item.detailInfo.metadata.required (正确路径)
        if (item.detailInfo?.metadata?.required) {
            requiredData = item.detailInfo.metadata.required;
            console.log('使用路径1: item.detailInfo.metadata.required');
        }
        // 路径2: 直接在item.required (备用路径)
        else if (item.required) {
            requiredData = item.required;
            console.log('使用路径2: item.required');
        }
        // 路径3: 在item.detailInfo.required (备用路径)
        else if (item.detailInfo?.required) {
            requiredData = item.detailInfo.required;
            console.log('使用路径3: item.detailInfo.required');
        }

        console.log('最终使用的requiredData:', requiredData);

        // 属性要求信息
        if (requiredData) {
            if (strEl) strEl.textContent = `STR: ${requiredData.str || 0}`;
            if (dexEl) dexEl.textContent = `DEX: ${requiredData.dex || 0}`;
            if (intEl) intEl.textContent = `INT: ${requiredData.int || 0}`;
            if (lukEl) lukEl.textContent = `LUK: ${requiredData.luk || 0}`;
            
            // 职业要求
            if (jobEl && requiredData.job) {
                const jobInfo = requiredData.job;
                const jobName = jobInfo.jobName || jobInfo.className || '全职业';
                jobEl.textContent = `职业: ${this.translateJobName(jobName)}`;
            } else if (jobEl) {
                jobEl.textContent = '职业: 全职业';
            }
        } else {
            // 如果没有required信息，显示默认值
            console.log('未找到required信息，使用默认值');
            if (strEl) strEl.textContent = 'STR: 0';
            if (dexEl) dexEl.textContent = 'DEX: 0';
            if (intEl) intEl.textContent = 'INT: 0';
            if (lukEl) lukEl.textContent = 'LUK: 0';
            if (jobEl) jobEl.textContent = '职业: 全职业';
        }

        // 功能支持状态
        const metadata = item.detailInfo?.metadata?.common;
        if (starforceEl) {
            const canStarforce = metadata?.enableStarforce === true && !isCash;
            starforceEl.textContent = `星力强化: ${canStarforce ? '支持' : '不支持'}`;
            starforceEl.className = canStarforce ? 'feature-supported' : 'feature-not-supported';
        }

        if (potentialEl) {
            const canPotential = metadata?.blockUpgradePotential !== true && !isCash;
            potentialEl.textContent = `潜能重设: ${canPotential ? '支持' : '不支持'}`;
            potentialEl.className = canPotential ? 'feature-supported' : 'feature-not-supported';
        }

        if (extraOptionEl) {
            const canExtraOption = metadata?.blockUpgradeExtraOption !== true && !isCash;
            extraOptionEl.textContent = `附加选项: ${canExtraOption ? '支持' : '不支持'}`;
            extraOptionEl.className = canExtraOption ? 'feature-supported' : 'feature-not-supported';
        }

        // 计算提示框位置 - 确保在右侧显示且不超出视窗
        const iconRect = iconElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let left = 290; // 相对于道具选择器的位置
        let top = iconRect.top - tooltip.parentElement.getBoundingClientRect().top;
        
        // 确保提示框不会超出视窗底部
        const maxTop = window.innerHeight - tooltipRect.height - 20;
        if (top > maxTop) {
            top = maxTop;
        }
        
        // 确保提示框不会超出视窗顶部
        if (top < 0) {
            top = 0;
        }

        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';
        tooltip.style.display = 'block';
    }

    // 翻译职业名称
    translateJobName(jobName) {
        const jobMap = {
            'Explorer': '全职业',
            'Warrior': '战士',
            'Magician': '魔法师',
            'Bowman': '弓箭手',
            'Thief': '飞侠',
            'Pirate': '海盗'
        };
        return jobMap[jobName] || jobName || '全职业';
    }

    // 隐藏道具详情提示框
    hideItemTooltip() {
        const tooltip = document.getElementById('item-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }

    // 从选择器设置装备
    setEquipFromSelector(itemId, name) {
        this.setEquip(itemId, name);
    }

    // 更新道具选择状态
    updateItemSelection(selectedItemId) {
        const itemIcons = document.querySelectorAll('.item-icon');
        itemIcons.forEach(icon => {
            if (icon.dataset.itemId == selectedItemId) {
                icon.classList.add('selected');
            } else {
                icon.classList.remove('selected');
            }
        });
    }

    // 测试函数：手动触发道具列表渲染
    testItemSelector() {
        console.log('=== 测试道具选择器 ===');
        console.log('itemList状态:', this.itemList ? `已加载${this.itemList.length}个道具` : '未加载');
        console.log('当前标签页:', this.currentTab);
        
        if (this.itemList && this.itemList.length > 0) {
            console.log('开始渲染道具列表...');
            this.renderItemList();
        } else {
            console.log('道具数据未加载，无法渲染');
        }
    }
}

// 页面加载完成后初始化模拟器
document.addEventListener('DOMContentLoaded', () => {
    window.simulator = new EquipEnhanceSimulator();
    
    // 添加一些视觉增强效果
    addVisualEnhancements();
});

function addVisualEnhancements() {
    // 先清除所有可能的滤镜效果
    clearAllFilters();
    
    // 添加鼠标悬停特效
    addHoverEffects();
    
    // 添加键盘快捷键提示
    addKeyboardShortcuts();
}

function clearAllFilters() {
    // 清除所有可交互元素的滤镜效果
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
        element.style.filter = 'none';
        element.style.boxShadow = 'none';
        element.style.textShadow = 'none';
    });
}

function addHoverEffects() {
    // 为所有可交互元素添加悬停特效
    const interactiveElements = [
        '.guide-button', '.close-button', 
        '.tab-starforce', '.tab-potential', '.tab-bonusstat',
        '.equip-slot-background', '.equip-item',
        '.enhance-button', '.cancel-button'
    ];
    
    interactiveElements.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                // 移除所有发光效果，只保留图片切换
                if (!element.classList.contains('disabled')) {
                    // 不添加任何滤镜效果，让CSS处理图片切换
                }
            });
            
            element.addEventListener('mouseleave', () => {
                // 确保清除任何可能的滤镜效果
                element.style.filter = 'none';
            });
        });
    });
}

function addKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
        
        switch (e.key) {
            case '1':
                window.simulator.switchTab('starforce');
                break;
            case '2':
                window.simulator.switchTab('potential');
                break;
            case '3':
                window.simulator.switchTab('bonusstat');
                break;
            case 'Enter':
                if (!window.simulator.enhancing) {
                    window.simulator.startEnhance();
                }
                break;
            case 'Escape':
                window.simulator.cancelEnhance();
                break;
            case 'e':
            case 'E':
                // E键：随机选择装备
                window.simulator.selectEquip();
                break;
            case 'r':
            case 'R':
                // R键：清除装备
                window.simulator.clearEquip();
                break;
        }
    });
} 