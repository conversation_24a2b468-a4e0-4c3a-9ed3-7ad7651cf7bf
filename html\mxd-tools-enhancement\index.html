<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MSU装备强化模拟器</title>
    <link rel="stylesheet" href="styles.css">
    <script src="itemList.js"></script>
</head>
<body>
    <div id="app">
        <!-- Loading界面 -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-container">
                <div class="loading-logo">
                    <h1>MSU 装备强化模拟器</h1>
                    <div class="loading-subtitle">MapleStory Universe Equipment Enhancement Simulator</div>
                </div>
                
                <div class="loading-progress">
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-text" id="progress-text">初始化中... 0%</div>
                    </div>
                    
                    <div class="loading-status" id="loading-status">准备加载资源...</div>
                </div>
                
                <div class="loading-tips">
                    <div class="tip-title">💡 使用提示</div>
                    <div class="tip-content">
                        <div class="tip-item">• 左键点击装备槽随机选择装备，右键清除装备</div>
                        <div class="tip-item">• 支持键盘快捷键：1/2/3切换标签页，E选择装备，R清除装备</div>
                        <div class="tip-item">• 高等级星力强化需要完成迷你游戏获得奖励</div>
                        <div class="tip-item">• 可以启用镇护功能防止装备破坏</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分层控制面板 -->
        <div class="layer-control-panel">
            <h3>分层显示控制</h3>
            <div class="layer-controls">
                <label><input type="checkbox" id="layer-0" checked> Z-0: 背景层</label>
                <label><input type="checkbox" id="layer-1" checked> Z-1: 按钮/Tab层</label>
                <label><input type="checkbox" id="layer-2" checked> Z-2: 装备槽背景层</label>
                <label><input type="checkbox" id="layer-3" checked> Z-3: 徽章层</label>
                <label><input type="checkbox" id="layer-4" checked> Z-4: 特效层</label>
                <label><input type="checkbox" id="layer-5" checked> Z-5: 通知框层</label>
                <label><input type="checkbox" id="layer-6" checked> Z-6: 信息面板层</label>
                <label><input type="checkbox" id="layer-7" checked> Z-7: 概率信息层</label>
                <label><input type="checkbox" id="layer-8" checked> Z-8: 装备物品层</label>
                <label><input type="checkbox" id="layer-10" checked> Z-10: 底部UI层</label>
                <label><input type="checkbox" id="layer-50" checked> Z-50: 迷你游戏层</label>
                <label><input type="checkbox" id="layer-1000" checked> Z-1000: 弹窗层</label>
                <label><input type="checkbox" id="item-selector-toggle" checked> 道具选择器</label>
            </div>
            <button id="show-all-layers">显示全部</button>
            <button id="hide-all-layers">隐藏全部</button>
        </div>

        <!-- 主界面容器 -->
        <div class="ui-container">
            
            <!-- 道具选择列表框 -->
            <div class="item-selector-panel layer-z-1" id="item-selector">
                <div class="item-selector-header">
                    <h3>道具选择</h3>
                    <div class="item-filter-info" id="filter-info">星力强化道具</div>
                </div>
                <div class="item-selector-content">
                    <div class="item-grid" id="item-grid">
                        <!-- 道具图标将通过JavaScript动态生成 -->
                    </div>
                </div>
                <!-- 道具详情提示框 -->
                <div class="item-tooltip" id="item-tooltip">
                    <!-- 装备名称和基本信息 -->
                    <div class="tooltip-header">
                        <div id="tooltip-title">Dea Sidus Earring</div>
                        <div class="tooltip-item-id">#7351 (Rare Item)</div>
                        <div class="tooltip-rarity">
                            <div class="star-rating">★★★★★ ★★★★★ ★★★★★</div>
                            <div class="rarity-text">RARE</div>
                        </div>
                    </div>
                    
                    <!-- 需求信息区域 -->
                    <div class="tooltip-requirements">
                        <div class="requirement-title">装备要求</div>
                        <div id="tooltip-level">REQ LEV: 130</div>
                        <div class="requirement-stats">
                            <div id="requirement-str">STR: 0</div>
                            <div id="requirement-dex">DEX: 0</div>
                            <div id="requirement-int">INT: 0</div>
                            <div id="requirement-luk">LUK: 0</div>
                        </div>
                        <div id="requirement-job">职业: 全职业</div>
                    </div>
                    
                    <!-- 装备类型 -->
                    <div class="tooltip-category">
                        <div id="tooltip-category">Equipment Type: Earrings</div>
                    </div>
                    
                    <!-- 现金道具状态 -->
                    <div class="tooltip-cash-status">
                        <div id="tooltip-cash-item">装备道具</div>
                    </div>
                    
                    <!-- 功能支持状态 -->
                    <div class="tooltip-features">
                        <div class="feature-title">强化功能支持</div>
                        <div class="feature-list">
                            <div id="feature-starforce">星力强化: 支持</div>
                            <div id="feature-potential">潜能重设: 支持</div>
                            <div id="feature-extraoption">附加选项: 支持</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Z-0: 主背景 -->
            <div class="main-background layer-z-0"></div>
            
            <!-- Z-1: 顶部按钮 -->
            <div class="guide-button layer-z-1" id="guide-btn"></div>
            <div class="close-button layer-z-1" id="close-btn"></div>
            
            <!-- Z-1: 标签页 -->
            <div class="tab-starforce layer-z-1" data-tab="starforce"></div>
            <div class="tab-potential layer-z-1" data-tab="potential"></div>
            <div class="tab-bonusstat layer-z-1" data-tab="bonusstat"></div>
            
            <!-- Z-1: Tab分割线 -->
            <div class="tab-line-background layer-z-1"></div>
            
            <!-- Z-2: 装备槽背景 -->
            <div class="equip-slot-background layer-z-2" id="equip-slot-bg"></div>
            
            <!-- Z-3: 装备徽章 -->
            <div class="equip-badge layer-z-3" id="equip-badge">
                <span class="badge-text">0</span>
            </div>
            
            <!-- Z-4: 强化特效 -->
            <div class="starforce-effect-background layer-z-4" id="starforce-effect"></div>
            
            <!-- Z-5: 通知框 -->
            <div class="notice-background layer-z-5"></div>
            <div class="notice-text layer-z-5" id="equip-notice">
                <span>可将想要进行星力强化的装备拖放到Power Crystals上。</span>
            </div>
            
            <!-- Z-6: 信息面板背景 -->
            <div class="info-panel-background layer-z-6"></div>
            <div class="stats-panel-background layer-z-6"></div>
            
            <!-- Z-7: 概率信息内容 -->
            <div class="probability-content layer-z-7">
                <div class="prob-row">
                    <div class="prob-item success">
                        <span class="prob-label">Success概率</span>
                        <span class="prob-value" id="success-rate">95.00%</span>
                    </div>
                    <div class="prob-item major-failure">
                        <span class="prob-label">Major Failure概率</span>
                        <span class="prob-value" id="major-failure-rate">0.00%</span>
                    </div>
                </div>
                <div class="prob-row">
                    <div class="prob-item failure">
                        <span class="prob-label">Failure(Keep)概率</span>
                        <span class="prob-value" id="failure-rate">5.00%</span>
                    </div>
                    <div class="prob-item failure-drop">
                        <span class="prob-label">Failure(Drop)概率</span>
                        <span class="prob-value" id="failure-drop-rate">0.00%</span>
                    </div>
                </div>
            </div>
            
            <!-- Z-7: Star Catching 开关 -->
            <div class="starcatch-toggle layer-z-7" id="starcatch-toggle">
                <input type="checkbox" id="starcatch-checkbox">
                <label for="starcatch-checkbox"></label>
            </div>
            
            <!-- Z-7: 防止Major Failure 开关 -->
            <div class="prevent-toggle layer-z-7" id="prevent-toggle">
                <input type="checkbox" id="prevent-checkbox">
                <label for="prevent-checkbox"></label>
            </div>
            
            <!-- Z-7: 属性变化信息 -->
            <div class="stats-content layer-z-7">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">• STR</span>
                        <div class="stat-values">
                            <span class="stat-current">0</span>
                            <span class="stat-arrow">⟩</span>
                            <span class="stat-next">2</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">• DEX</span>
                        <div class="stat-values">
                            <span class="stat-current">0</span>
                            <span class="stat-arrow">⟩</span>
                            <span class="stat-next">2</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">• INT</span>
                        <div class="stat-values">
                            <span class="stat-current">0</span>
                            <span class="stat-arrow">⟩</span>
                            <span class="stat-next">2</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">• LUK</span>
                        <div class="stat-values">
                            <span class="stat-current">0</span>
                            <span class="stat-arrow">⟩</span>
                            <span class="stat-next">6</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">• MaxHP</span>
                        <div class="stat-values">
                            <span class="stat-current">0</span>
                            <span class="stat-arrow">⟩</span>
                            <span class="stat-next">5</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">• DEF</span>
                        <div class="stat-values">
                            <span class="stat-current">0</span>
                            <span class="stat-arrow">⟩</span>
                            <span class="stat-next">6</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Z-8: 装备物品 -->
            <div class="equip-item layer-z-8" id="equip-item">+</div>
            
            <!-- Z-8: 星级进度显示 -->
            <div class="star-progress layer-z-8">
                <div class="star-level-display">
                    <div class="star-current">⭐ <span id="current-star">0</span></div>
                    <div class="star-arrow">→</div>
                    <div class="star-next">⭐ <span id="next-star">1</span></div>
                </div>
            </div>
            
            <!-- Z-10: 底部UI元素 -->
            <div class="cost-background layer-z-10"></div>
            <div class="cost-content layer-z-10">
                <div class="cost-row">
                    <div class="cost-item">
                        <div class="cost-icon meso-icon"></div>
                        <span class="cost-label">需要Power Crystals</span>
                    </div>
                    <div class="cost-value" id="required-cost">1,000,000</div>
                    <div class="cost-action">
                        <button class="cost-btn">充值</button>
                    </div>
                </div>
                <div class="cost-row">
                    <div class="cost-item">
                        <div class="cost-icon star-icon"></div>
                        <span class="cost-label">星力能量</span>
                    </div>
                    <div class="cost-value">1,967,768,508</div>
                    <div class="cost-action">
                        <button class="cost-btn">购买</button>
                        <button class="cost-btn">自动购买</button>
                    </div>
                </div>
            </div>
            
            <!-- Z-10: 主要按钮 -->
            <div class="enhance-button layer-z-10" id="enhance-btn"></div>
            <div class="cancel-button layer-z-10" id="cancel-btn"></div>
            
            <!-- Z-50: 迷你游戏覆盖层 -->
            <div class="minigame-background layer-z-50" id="minigame-overlay" style="display: none;"></div>
            <div class="minigame-content layer-z-50" id="minigame-content" style="display: none;">
                <div class="minigame-gauge">
                    <div class="gauge-fill" id="gauge-fill"></div>
                    <div class="moving-star">⭐</div>
                </div>
                <button class="stop-btn" id="stop-btn"></button>
            </div>
            
            <!-- Z-1000: 弹出对话框背景 -->
            <div class="popup-background layer-z-1000" id="popup-overlay" style="display: none;"></div>
            
            <!-- Z-1000: 弹出对话框内容 -->
            <div class="popup-content layer-z-1000" id="popup-content" style="display: none;">
                <div class="popup-message" id="popup-message">确定要强化这个装备吗？</div>
                <div class="popup-buttons">
                    <button class="popup-btn confirm" id="popup-confirm"></button>
                    <button class="popup-btn cancel" id="popup-cancel"></button>
                </div>
            </div>
            
            <!-- Z-1000: 结果对话框背景 -->
            <div class="result-background layer-z-1000" id="result-overlay" style="display: none;"></div>
            
            <!-- Z-1000: 结果对话框内容 -->
            <div class="result-content layer-z-1000" id="result-content" style="display: none;">
                <div class="result-message" id="result-message">强化成功！</div>
                <button class="result-btn" id="result-ok"></button>
            </div>
            
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 