/**
 * Authentication state management using Zustand
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { apiClient, UserResponse, UserLoginRequest, UserRegisterRequest } from '@/lib/api-client'

interface AuthState {
  // 状态
  user: UserResponse | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // 操作
  login: (credentials: UserLoginRequest) => Promise<boolean>
  register: (userData: UserRegisterRequest) => Promise<boolean>
  logout: () => Promise<void>
  verifyEmail: (token: string) => Promise<boolean>
  resendVerification: (email: string) => Promise<boolean>
  forgotPassword: (email: string) => Promise<boolean>
  resetPassword: (token: string, newPassword: string) => Promise<boolean>
  getCurrentUser: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // 登录
      login: async (credentials: UserLoginRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiClient.login(credentials)
          
          if (response.success && response.data) {
            set({
              user: response.data.user,
              isAuthenticated: true,
              isLoading: false,
              error: null
            })
            return true
          } else {
            set({
              error: '登录失败',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || '登录失败，请稍后重试',
            isLoading: false
          })
          return false
        }
      },

      // 注册
      register: async (userData: UserRegisterRequest) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiClient.register(userData)
          
          if (response.success) {
            set({
              isLoading: false,
              error: null
            })
            return true
          } else {
            set({
              error: '注册失败',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || '注册失败，请稍后重试',
            isLoading: false
          })
          return false
        }
      },

      // 登出
      logout: async () => {
        set({ isLoading: true })
        
        try {
          await apiClient.logout()
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      },

      // 邮箱验证
      verifyEmail: async (token: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiClient.verifyEmail({ token })
          
          if (response.success) {
            // 验证成功后更新用户信息
            await get().getCurrentUser()
            set({ isLoading: false })
            return true
          } else {
            set({
              error: '验证失败',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || '验证失败，请稍后重试',
            isLoading: false
          })
          return false
        }
      },

      // 重新发送验证邮件
      resendVerification: async (email: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiClient.resendVerification({ email })
          
          if (response.success) {
            set({ isLoading: false })
            return true
          } else {
            set({
              error: '发送失败',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || '发送失败，请稍后重试',
            isLoading: false
          })
          return false
        }
      },

      // 忘记密码
      forgotPassword: async (email: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiClient.forgotPassword({ email })
          
          if (response.success) {
            set({ isLoading: false })
            return true
          } else {
            set({
              error: '发送失败',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || '发送失败，请稍后重试',
            isLoading: false
          })
          return false
        }
      },

      // 重置密码
      resetPassword: async (token: string, newPassword: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiClient.resetPassword({ token, new_password: newPassword })
          
          if (response.success) {
            set({ isLoading: false })
            return true
          } else {
            set({
              error: '重置失败',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || '重置失败，请稍后重试',
            isLoading: false
          })
          return false
        }
      },

      // 获取当前用户信息
      getCurrentUser: async () => {
        try {
          const response = await apiClient.getCurrentUser()
          
          if (response.success && response.data) {
            set({
              user: response.data,
              isAuthenticated: true
            })
          }
        } catch (error) {
          console.error('Get current user error:', error)
          set({
            user: null,
            isAuthenticated: false
          })
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null })
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
