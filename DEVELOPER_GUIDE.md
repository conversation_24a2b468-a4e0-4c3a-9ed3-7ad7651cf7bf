# 🧑‍💻 冒险岛情报站开发者指南

[![Next.js](https://img.shields.io/badge/Next.js-14.2.16-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)

> 面向开发者的详细技术文档，包含架构设计、开发规范、API 说明等

## 📋 目录

- [项目架构](#-项目架构)
- [技术栈详解](#-技术栈详解)
- [开发环境设置](#-开发环境设置)
- [代码结构](#-代码结构)
- [组件系统](#-组件系统)
- [数据流管理](#-数据流管理)
- [API 设计](#-api-设计)
- [开发工作流](#-开发工作流)
- [测试策略](#-测试策略)
- [性能优化](#-性能优化)
- [部署流程](#-部署流程)
- [故障排除](#-故障排除)

## 🏗️ 项目架构

### 整体架构设计

```mermaid
graph TB
    A[用户界面层] --> B[组件层]
    B --> C[业务逻辑层]
    C --> D[数据访问层]
    D --> E[静态资源层]
    
    A1[页面路由] --> A
    A2[布局组件] --> A
    
    B1[UI组件库] --> B
    B2[业务组件] --> B
    
    C1[工具函数] --> C
    C2[状态管理] --> C
    
    D1[文件系统API] --> D
    D2[数据预处理] --> D
    
    E1[图片资源] --> E
    E2[数据文件] --> E
```

### 架构原则

1. **关注点分离**：页面、组件、逻辑、数据各司其职
2. **模块化设计**：每个功能模块独立开发和维护
3. **类型安全**：全面使用 TypeScript 确保类型安全
4. **性能优先**：静态生成和优化策略
5. **可扩展性**：支持新功能和工具的快速集成

### 目录结构设计

```
maplestory-info-station/
├── 📁 app/                     # Next.js App Router (页面层)
│   ├── 📄 layout.tsx          # 全局布局
│   ├── 📄 page.tsx            # 首页
│   ├── 📄 globals.css         # 全局样式
│   └── 📁 [feature]/          # 功能模块页面
├── 📁 components/             # 组件层
│   ├── 📁 ui/                 # 基础UI组件
│   ├── 📁 [feature]-simulator/ # 功能组件
│   └── 📄 [shared].tsx        # 共享组件
├── 📁 lib/                    # 业务逻辑层
│   ├── 📄 [feature]-utils.ts  # 功能工具函数
│   └── 📄 utils.ts            # 通用工具函数
├── 📁 types/                  # 类型定义层
│   └── 📄 [feature].ts        # 功能类型定义
├── 📁 hooks/                  # 自定义Hooks
│   └── 📄 use-[feature].tsx   # 功能Hooks
├── 📁 public/                 # 静态资源层
│   ├── 📁 images/             # 图片资源
│   └── 📁 data/               # 数据文件
└── 📁 scripts/                # 构建脚本
    └── 📄 [task].js           # 任务脚本
```

## 🔧 技术栈详解

### 核心框架

#### Next.js 14 (App Router)

**选择原因**：
- 现代化的 React 全栈框架
- App Router 提供更好的开发体验
- 内置优化和性能特性
- 静态生成支持

**关键配置**：
```javascript
// next.config.mjs
const nextConfig = {
  output: 'export',        // 静态导出
  trailingSlash: true,     // URL 尾部斜杠
  eslint: {
    ignoreDuringBuilds: true
  },
  typescript: {
    ignoreBuildErrors: true
  },
  images: {
    unoptimized: true      // 禁用图片优化（静态导出需要）
  }
}
```

#### React 18

**特性使用**：
- Hooks API 进行状态管理
- Suspense 和 Error Boundaries
- 并发特性（Concurrent Features）
- 服务端组件（Server Components）

#### TypeScript 5

**类型策略**：
- 严格模式启用
- 完整的类型覆盖
- 接口优先设计
- 泛型和高级类型使用

### UI 和样式

#### Tailwind CSS 3.4.17

**配置策略**：
```typescript
// tailwind.config.ts
const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Poppins', '"Helvetica Neue"', 'Helvetica', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', '"Fira Sans"', '"Droid Sans"', 'sans-serif'],
      },
      // 自定义颜色和样式
    },
  },
  plugins: [],
}
```

**使用规范**：
- 原子化类名组合
- 响应式设计优先
- 自定义组件样式
- 一致的设计令牌

#### shadcn/ui + Radix UI

**组件策略**：
- 基础组件库提供一致性
- 可访问性（Accessibility）支持
- 自定义主题能力
- 组合式组件设计

### 状态管理

#### React Hooks + Context API

**状态分层**：
```typescript
// 本地状态 - useState
const [localState, setLocalState] = useState(initialValue)

// 共享状态 - useContext
const sharedState = useContext(SharedContext)

// 副作用 - useEffect
useEffect(() => {
  // 副作用逻辑
}, [dependencies])

// 性能优化 - useMemo, useCallback
const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b])
const memoizedCallback = useCallback(() => doSomething(a, b), [a, b])
```

## 🚀 开发环境设置

### 环境要求

```json
{
  "node": ">=18.0.0",
  "pnpm": ">=8.0.0",
  "typescript": "^5.0.0"
}
```

### 开发工具配置

#### VSCode 推荐扩展

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

#### ESLint 配置

```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error"
  }
}
```

### 开发脚本

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test:build": "node scripts/build-test.js"
  }
}
```

## 📦 代码结构

### 文件命名规范

```
页面文件：     page.tsx, layout.tsx
组件文件：     PascalCase.tsx (如 EnhancementSimulator.tsx)
工具文件：     kebab-case.ts (如 enhancement-utils.ts)
类型文件：     kebab-case.ts (如 enhancement.ts)
Hook文件：     use-feature.tsx (如 use-mobile.tsx)
常量文件：     UPPER_CASE.ts (如 CONSTANTS.ts)
```

### 导入顺序规范

```typescript
// 1. React 和 Next.js 核心
import React from 'react'
import { Metadata } from 'next'

// 2. 第三方库
import { clsx } from 'clsx'
import { Lucide } from 'lucide-react'

// 3. 内部组件和工具
import { Button } from '@/components/ui/button'
import { calculateEnhancement } from '@/lib/enhancement-utils'

// 4. 类型定义
import type { Enhancement } from '@/types/enhancement'

// 5. 相对导入
import './styles.css'
```

### 组件文件结构

```typescript
"use client" // 客户端组件标识（如需要）

// 导入部分
import { useState, useCallback } from 'react'
import type { ComponentProps } from 'react'

// 类型定义
interface ComponentNameProps {
  prop1: string
  prop2?: number
  children?: React.ReactNode
}

// 常量定义
const DEFAULT_VALUES = {
  prop2: 0
}

// 主组件
export function ComponentName({ 
  prop1, 
  prop2 = DEFAULT_VALUES.prop2,
  children 
}: ComponentNameProps) {
  // 状态定义
  const [state, setState] = useState(initialState)
  
  // 事件处理函数
  const handleEvent = useCallback(() => {
    // 处理逻辑
  }, [dependencies])
  
  // 渲染
  return (
    <div className="component-wrapper">
      {children}
    </div>
  )
}

// 默认导出（如需要）
export default ComponentName

## 🧩 组件系统

### 组件分层架构

```
组件层次结构：
├── 📁 app/                    # 页面组件层
│   └── 📄 page.tsx           # 页面级组件
├── 📁 components/            # 业务组件层
│   ├── 📁 ui/               # 基础UI组件
│   │   ├── 📄 button.tsx    # 原子组件
│   │   ├── 📄 card.tsx      # 分子组件
│   │   └── 📄 dialog.tsx    # 有机体组件
│   ├── 📁 feature-simulator/ # 功能组件集
│   │   ├── 📄 Simulator.tsx # 主组件
│   │   ├── 📄 Panel.tsx     # 子组件
│   │   └── 📄 Controls.tsx  # 控制组件
│   └── 📄 shared.tsx        # 共享组件
```

### 组件设计原则

#### 1. 单一职责原则
每个组件只负责一个明确的功能：

```typescript
// ✅ 好的设计 - 单一职责
function EquipmentSlot({ equipment, onSelect, onClear }: EquipmentSlotProps) {
  return (
    <div className="equipment-slot">
      {equipment ? (
        <EquipmentDisplay equipment={equipment} onClear={onClear} />
      ) : (
        <EmptySlot onSelect={onSelect} />
      )}
    </div>
  )
}

// ❌ 不好的设计 - 职责过多
function EquipmentManager() {
  // 管理装备、处理强化、显示结果、处理动画...
}
```

#### 2. 组合优于继承

```typescript
// ✅ 组合模式
function EnhancementSimulator() {
  return (
    <div className="simulator">
      <TabSelector />
      <EquipmentSlot />
      <InfoPanel />
      <ControlPanel />
    </div>
  )
}

// 每个子组件独立且可复用
```

#### 3. Props 接口设计

```typescript
// 基础 Props 接口
interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

// 扩展 Props 接口
interface EquipmentSlotProps extends BaseComponentProps {
  equipment: Equipment | null
  onEquipmentSelect: (equipment: Equipment) => void
  onEquipmentClear: () => void
  disabled?: boolean
}

// 使用泛型的 Props
interface SelectorProps<T> {
  items: T[]
  selectedItem: T | null
  onSelect: (item: T) => void
  renderItem: (item: T) => React.ReactNode
}
```

### 核心组件详解

#### EnhancementSimulator 主组件

```typescript
// components/enhancement-simulator/EnhancementSimulator.tsx
export default function EnhancementSimulator() {
  // 状态管理
  const [state, setState] = useState<SimulatorState>(INITIAL_STATE)

  // 业务逻辑 Hooks
  const { itemList, loading } = useItemList()
  const { performEnhancement } = useEnhancement()
  const { playAnimation } = useAnimation()

  // 事件处理
  const handleEnhancement = useCallback(async () => {
    const result = await performEnhancement(state)
    playAnimation(result.type)
    setState(prev => ({ ...prev, ...result }))
  }, [state, performEnhancement, playAnimation])

  // 渲染逻辑
  return (
    <div className="enhancement-simulator">
      <LoadingScreen visible={loading} />
      <TabSelector
        currentTab={state.currentTab}
        onTabChange={handleTabChange}
      />
      <EquipmentSlot
        equipment={state.currentEquip}
        onSelect={handleEquipmentSelect}
        onClear={handleEquipmentClear}
      />
      <InfoPanel equipment={state.currentEquip} />
      <ControlPanel
        onEnhance={handleEnhancement}
        disabled={!state.currentEquip}
      />
      <EffectRenderer animation={state.animation} />
    </div>
  )
}
```

#### 自定义 Hooks 设计

```typescript
// hooks/use-enhancement.tsx
export function useEnhancement() {
  const performEnhancement = useCallback(async (
    type: EnhancementType,
    level: number,
    options: EnhancementOptions
  ): Promise<EnhancementResult> => {
    // 计算强化结果
    const result = calculateEnhancementResult(type, level, options)

    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 100))

    return result
  }, [])

  return { performEnhancement }
}

// hooks/use-item-list.tsx
export function useItemList() {
  const [itemList, setItemList] = useState<Item[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadItemList().then(items => {
      setItemList(items)
      setLoading(false)
    })
  }, [])

  return { itemList, loading }
}
```

## 📊 数据流管理

### 数据流架构

```mermaid
graph LR
    A[用户操作] --> B[事件处理器]
    B --> C[业务逻辑]
    C --> D[状态更新]
    D --> E[组件重渲染]
    E --> F[UI更新]

    G[静态数据] --> H[数据加载]
    H --> I[状态初始化]
    I --> D
```

### 状态管理策略

#### 1. 本地状态 (useState)

适用于：
- 组件内部状态
- 临时UI状态
- 表单输入状态

```typescript
function EquipmentSlot() {
  const [isHovered, setIsHovered] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)

  return (
    <div
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 组件内容 */}
    </div>
  )
}
```

#### 2. 共享状态 (useContext)

适用于：
- 跨组件共享的状态
- 主题设置
- 用户偏好

```typescript
// contexts/SimulatorContext.tsx
interface SimulatorContextType {
  currentEquipment: Equipment | null
  setCurrentEquipment: (equipment: Equipment | null) => void
  enhancementLevel: number
  setEnhancementLevel: (level: number) => void
}

const SimulatorContext = createContext<SimulatorContextType | null>(null)

export function SimulatorProvider({ children }: { children: React.ReactNode }) {
  const [currentEquipment, setCurrentEquipment] = useState<Equipment | null>(null)
  const [enhancementLevel, setEnhancementLevel] = useState(0)

  const value = {
    currentEquipment,
    setCurrentEquipment,
    enhancementLevel,
    setEnhancementLevel
  }

  return (
    <SimulatorContext.Provider value={value}>
      {children}
    </SimulatorContext.Provider>
  )
}

export function useSimulator() {
  const context = useContext(SimulatorContext)
  if (!context) {
    throw new Error('useSimulator must be used within SimulatorProvider')
  }
  return context
}
```

#### 3. 服务端状态 (Server State)

适用于：
- 静态数据加载
- 文件系统数据
- 预计算数据

```typescript
// lib/data-loader.ts
export async function loadItemList(): Promise<Item[]> {
  try {
    // 动态导入数据文件
    const { itemList } = await import('@/public/data/itemList.js')
    return itemList
  } catch (error) {
    console.error('Failed to load item list:', error)
    return []
  }
}

export async function loadCMS216Items(): Promise<ItemData[]> {
  try {
    const response = await fetch('/api/cms-216-items')
    return await response.json()
  } catch (error) {
    console.error('Failed to load CMS-216 items:', error)
    return []
  }
}
```

### 数据预处理

#### 装备数据处理

```typescript
// lib/item-filter-utils.ts
export function filterItemsByEnhancementType(
  items: Item[],
  enhancementType: EnhancementType
): Item[] {
  const compatibilityMap = {
    starforce: ['weapon', 'armor', 'accessory'],
    potential: ['weapon', 'armor', 'accessory'],
    bonusstat: ['weapon', 'armor']
  }

  const compatibleCategories = compatibilityMap[enhancementType]

  return items.filter(item =>
    compatibleCategories.includes(item.category)
  )
}

export function sortItemsByLevel(items: Item[]): Item[] {
  return [...items].sort((a, b) => {
    // 按等级排序，同等级按名称排序
    if (a.level !== b.level) {
      return a.level - b.level
    }
    return a.name.localeCompare(b.name)
  })
}
```

#### 图片资源处理

```typescript
// lib/resource-preloader.ts
export class ResourcePreloader {
  private loadedImages = new Set<string>()

  async preloadImage(src: string): Promise<void> {
    if (this.loadedImages.has(src)) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.loadedImages.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }

  async preloadImages(sources: string[]): Promise<void> {
    const promises = sources.map(src => this.preloadImage(src))
    await Promise.all(promises)
  }
}

export const resourcePreloader = new ResourcePreloader()
```

## 🔌 API 设计

### 内部 API 架构

#### 文件系统 API

```typescript
// app/api/cms-216-items/route.ts
import { NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

export async function GET() {
  try {
    const itemsDirectory = path.join(process.cwd(), 'public/images/cms-216')
    const filenames = await fs.readdir(itemsDirectory)

    const items = filenames
      .filter(name => /\.(png|jpg|jpeg|gif|webp)$/i.test(name))
      .map(filename => ({
        id: filename.replace(/\.[^/.]+$/, ''),
        name: formatItemName(filename),
        image: `/images/cms-216/${filename}`,
        filename
      }))

    return NextResponse.json(items)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to load items' },
      { status: 500 }
    )
  }
}

function formatItemName(filename: string): string {
  return filename
    .replace(/\.[^/.]+$/, '')  // 移除扩展名
    .replace(/_/g, ' ')        // 下划线转空格
    .replace(/\b\w/g, l => l.toUpperCase()) // 首字母大写
}
```

#### 数据转换 API

```typescript
// lib/data-transformers.ts
export interface RawItemData {
  itemId: number
  name: string
  category: number
  level: number
  // ... 其他原始字段
}

export interface ProcessedItem {
  id: string
  name: string
  type: string
  level: number
  category: string
  imageUrl: string
  // ... 处理后的字段
}

export function transformRawItem(raw: RawItemData): ProcessedItem {
  return {
    id: raw.itemId.toString(),
    name: raw.name,
    type: getCategoryName(raw.category),
    level: raw.level,
    category: raw.category.toString(),
    imageUrl: `/images/items/${raw.itemId}.png`
  }
}

export function transformItemList(rawItems: RawItemData[]): ProcessedItem[] {
  return rawItems.map(transformRawItem)
}
```

### 类型定义系统

#### 核心类型定义

```typescript
// types/enhancement.ts

// 基础类型
export type EnhancementType = 'starforce' | 'potential' | 'bonusstat'
export type EnhancementResultType = 'success' | 'failed' | 'major_failure'
export type EffectType = 'standby' | 'progress' | 'success' | 'failed'

// 装备相关类型
export interface Equipment {
  itemId: string | null
  name: string
  type: string
  category: string
  level?: number
  imageUrl?: string
}

export interface EquipmentStats {
  STR: number
  DEX: number
  INT: number
  LUK: number
  MaxHP: number
  DEF: number
  [key: string]: number // 允许动态属性
}

// 强化相关类型
export interface EnhancementProbability {
  success: number        // 成功概率 (%)
  failure: number        // 失败概率 (%)
  major_failure: number  // 大失败概率 (%)
  failure_drop: number   // 等级下降概率 (%)
}

export interface EnhancementOptions {
  starcatchEnabled: boolean
  preventEnabled: boolean
  mvpDiscount: boolean
  eventDiscount: boolean
}

export interface EnhancementResult {
  type: EnhancementResultType
  message: string
  level: number
  previousLevel?: number
  cost: number
  success: boolean
}

// 动画相关类型
export interface AnimationState {
  isActive: boolean
  currentFrame: number
  effectType: EffectType
  isLooping: boolean
  duration?: number
}

// 模拟器状态类型
export interface SimulatorState {
  currentTab: EnhancementType
  currentEquip: Equipment
  equipLevel: number
  itemList: ProcessedItem[]
  animation: AnimationState
  isEnhancing: boolean
  starcatchEnabled: boolean
  preventEnabled: boolean
  mvpDiscount: boolean
  eventDiscount: boolean
}
```

#### 工具类型定义

```typescript
// types/utils.ts

// 通用工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type Required<T, K extends keyof T> = T & Required<Pick<T, K>>

// 事件处理类型
export type EventHandler<T = void> = (event: T) => void
export type AsyncEventHandler<T = void> = (event: T) => Promise<void>

// 组件 Props 类型
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface InteractiveComponentProps extends BaseComponentProps {
  disabled?: boolean
  loading?: boolean
}

// API 响应类型
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

## 🔄 开发工作流

### Git 工作流

#### 分支策略

```
main                 # 主分支，生产环境代码
├── develop         # 开发分支，集成最新功能
├── feature/xxx     # 功能分支
├── hotfix/xxx      # 热修复分支
└── release/xxx     # 发布分支
```

#### 提交规范

```bash
# 功能开发
git commit -m "feat: 添加装备强化模拟器"
git commit -m "feat(simulator): 实现星之力强化逻辑"

# Bug 修复
git commit -m "fix: 修复装备选择器显示问题"
git commit -m "fix(ui): 解决移动端布局错乱"

# 文档更新
git commit -m "docs: 更新开发者指南"
git commit -m "docs(api): 添加API使用示例"

# 样式调整
git commit -m "style: 统一组件间距规范"

# 重构
git commit -m "refactor: 优化强化计算逻辑"

# 测试
git commit -m "test: 添加强化模拟器单元测试"

# 构建相关
git commit -m "chore: 更新依赖版本"
git commit -m "ci: 优化构建流程"
```

### 开发流程

#### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-simulator

# 2. 开发功能
# - 编写代码
# - 添加类型定义
# - 编写测试
# - 更新文档

# 3. 提交代码
git add .
git commit -m "feat: 添加新模拟器功能"

# 4. 推送分支
git push origin feature/new-simulator

# 5. 创建 Pull Request
# 6. 代码审查
# 7. 合并到 develop 分支
```

#### 2. 代码审查清单

- [ ] **功能完整性**：功能是否按需求实现
- [ ] **代码质量**：是否遵循编码规范
- [ ] **类型安全**：TypeScript 类型是否正确
- [ ] **性能考虑**：是否有性能问题
- [ ] **可访问性**：是否支持无障碍访问
- [ ] **响应式设计**：是否适配移动端
- [ ] **错误处理**：是否有适当的错误处理
- [ ] **测试覆盖**：是否有相应的测试

### 代码质量保证

#### ESLint 规则

```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    // TypeScript 规则
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/prefer-nullish-coalescing": "error",

    // React 规则
    "react-hooks/exhaustive-deps": "warn",
    "react/jsx-key": "error",

    // 通用规则
    "prefer-const": "error",
    "no-var": "error",
    "eqeqeq": "error"
  }
}
```

#### Prettier 配置

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

#### 预提交钩子

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{md,json}": [
      "prettier --write"
    ]
  }
}
```

## 🧪 测试策略

### 测试金字塔

```
    /\
   /  \     E2E Tests (少量)
  /____\
 /      \   Integration Tests (适量)
/________\  Unit Tests (大量)
```

### 单元测试

#### 工具函数测试

```typescript
// __tests__/lib/enhancement-utils.test.ts
import { calculateEnhancementResult, calculateCost } from '@/lib/enhancement-utils'

describe('enhancement-utils', () => {
  describe('calculateEnhancementResult', () => {
    it('should return success result for high probability', () => {
      const result = calculateEnhancementResult('starforce', 0, {
        starcatchEnabled: false,
        preventEnabled: false,
        mvpDiscount: false,
        eventDiscount: false
      })

      expect(result.type).toBe('success')
      expect(result.level).toBe(1)
    })

    it('should apply starcatch bonus correctly', () => {
      const resultWithStarcatch = calculateEnhancementResult('starforce', 15, {
        starcatchEnabled: true,
        preventEnabled: false,
        mvpDiscount: false,
        eventDiscount: false
      })

      // 验证抓星星加成效果
      expect(resultWithStarcatch.successRate).toBeGreaterThan(0)
    })
  })

  describe('calculateCost', () => {
    it('should apply MVP discount correctly', () => {
      const baseCost = calculateCost(10, { mvpDiscount: false })
      const discountedCost = calculateCost(10, { mvpDiscount: true })

      expect(discountedCost).toBe(baseCost * 0.7)
    })
  })
})
```

#### 组件测试

```typescript
// __tests__/components/EquipmentSlot.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { EquipmentSlot } from '@/components/enhancement-simulator/EquipmentSlot'

const mockEquipment = {
  itemId: '1',
  name: 'Test Sword',
  type: 'weapon',
  category: 'sword'
}

describe('EquipmentSlot', () => {
  it('renders empty slot when no equipment', () => {
    render(
      <EquipmentSlot
        equipment={null}
        onSelect={jest.fn()}
        onClear={jest.fn()}
      />
    )

    expect(screen.getByText('点击选择装备')).toBeInTheDocument()
  })

  it('renders equipment when provided', () => {
    render(
      <EquipmentSlot
        equipment={mockEquipment}
        onSelect={jest.fn()}
        onClear={jest.fn()}
      />
    )

    expect(screen.getByText('Test Sword')).toBeInTheDocument()
  })

  it('calls onSelect when empty slot is clicked', () => {
    const onSelect = jest.fn()

    render(
      <EquipmentSlot
        equipment={null}
        onSelect={onSelect}
        onClear={jest.fn()}
      />
    )

    fireEvent.click(screen.getByRole('button'))
    expect(onSelect).toHaveBeenCalled()
  })
})
```

### 集成测试

```typescript
// __tests__/integration/enhancement-flow.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import EnhancementSimulator from '@/components/enhancement-simulator/EnhancementSimulator'

describe('Enhancement Flow Integration', () => {
  it('completes full enhancement flow', async () => {
    render(<EnhancementSimulator />)

    // 1. 等待加载完成
    await waitFor(() => {
      expect(screen.queryByText('加载中')).not.toBeInTheDocument()
    })

    // 2. 选择装备
    fireEvent.click(screen.getByText('点击选择装备'))
    fireEvent.click(screen.getByText('随机选择'))

    // 3. 验证装备已选择
    await waitFor(() => {
      expect(screen.queryByText('点击选择装备')).not.toBeInTheDocument()
    })

    // 4. 开始强化
    fireEvent.click(screen.getByText('开始强化'))

    // 5. 验证强化结果
    await waitFor(() => {
      expect(screen.getByText(/强化/)).toBeInTheDocument()
    })
  })
})
```

### E2E 测试

```typescript
// e2e/enhancement-simulator.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Enhancement Simulator', () => {
  test('user can enhance equipment successfully', async ({ page }) => {
    // 访问页面
    await page.goto('/tools/enhancement')

    // 等待页面加载
    await page.waitForSelector('[data-testid="enhancement-simulator"]')

    // 选择装备
    await page.click('[data-testid="equipment-slot"]')
    await page.click('[data-testid="random-select"]')

    // 验证装备已选择
    await expect(page.locator('[data-testid="equipment-name"]')).toBeVisible()

    // 开始强化
    await page.click('[data-testid="enhance-button"]')

    // 等待强化动画完成
    await page.waitForSelector('[data-testid="enhancement-result"]')

    // 验证结果显示
    await expect(page.locator('[data-testid="enhancement-result"]')).toBeVisible()
  })
})
```

## ⚡ 性能优化

### 代码分割策略

#### 路由级别分割

```typescript
// app/tools/enhancement/page.tsx
import dynamic from 'next/dynamic'

// 动态导入大型组件
const EnhancementSimulator = dynamic(
  () => import('@/components/enhancement-simulator/EnhancementSimulator'),
  {
    loading: () => <div>加载中...</div>,
    ssr: false // 客户端渲染
  }
)

export default function EnhancementPage() {
  return (
    <div>
      <h1>装备强化模拟器</h1>
      <EnhancementSimulator />
    </div>
  )
}
```

#### 组件级别分割

```typescript
// components/enhancement-simulator/EnhancementSimulator.tsx
import { lazy, Suspense } from 'react'

// 懒加载重型组件
const ItemSelector = lazy(() => import('./ItemSelector'))
const EffectRenderer = lazy(() => import('./EffectRenderer'))

export default function EnhancementSimulator() {
  return (
    <div>
      <Suspense fallback={<div>加载装备选择器...</div>}>
        <ItemSelector />
      </Suspense>

      <Suspense fallback={<div>加载特效渲染器...</div>}>
        <EffectRenderer />
      </Suspense>
    </div>
  )
}
```

### 内存优化

#### React.memo 使用

```typescript
// components/ItemCard.tsx
import { memo } from 'react'

interface ItemCardProps {
  item: Item
  selected: boolean
  onSelect: (item: Item) => void
}

export const ItemCard = memo(function ItemCard({
  item,
  selected,
  onSelect
}: ItemCardProps) {
  return (
    <div
      className={`item-card ${selected ? 'selected' : ''}`}
      onClick={() => onSelect(item)}
    >
      <img src={item.imageUrl} alt={item.name} />
      <span>{item.name}</span>
    </div>
  )
}, (prevProps, nextProps) => {
  // 自定义比较函数
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.selected === nextProps.selected
  )
})
```

#### useMemo 和 useCallback 优化

```typescript
function ItemList({ items, filter, onSelect }: ItemListProps) {
  // 缓存过滤结果
  const filteredItems = useMemo(() => {
    return items.filter(item =>
      item.name.toLowerCase().includes(filter.toLowerCase())
    )
  }, [items, filter])

  // 缓存事件处理函数
  const handleSelect = useCallback((item: Item) => {
    onSelect(item)
  }, [onSelect])

  return (
    <div>
      {filteredItems.map(item => (
        <ItemCard
          key={item.id}
          item={item}
          onSelect={handleSelect}
        />
      ))}
    </div>
  )
}
```

### 资源优化

#### 图片懒加载

```typescript
// components/LazyImage.tsx
import { useState, useRef, useEffect } from 'react'

interface LazyImageProps {
  src: string
  alt: string
  className?: string
  placeholder?: string
}

export function LazyImage({ src, alt, className, placeholder }: LazyImageProps) {
  const [loaded, setLoaded] = useState(false)
  const [inView, setInView] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [])

  return (
    <div ref={imgRef} className={className}>
      {inView && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setLoaded(true)}
          style={{ opacity: loaded ? 1 : 0 }}
        />
      )}
      {!loaded && placeholder && (
        <div className="placeholder">{placeholder}</div>
      )}
    </div>
  )
}
```

#### 资源预加载

```typescript
// lib/resource-preloader.ts
export class ResourcePreloader {
  private cache = new Map<string, Promise<void>>()

  preloadImage(src: string): Promise<void> {
    if (this.cache.has(src)) {
      return this.cache.get(src)!
    }

    const promise = new Promise<void>((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })

    this.cache.set(src, promise)
    return promise
  }

  async preloadCriticalResources() {
    const criticalImages = [
      '/images/ui/enhancement-bg.png',
      '/images/ui/equipment-slot.png',
      '/images/ui/buttons.png'
    ]

    await Promise.all(
      criticalImages.map(src => this.preloadImage(src))
    )
  }
}
```

## 🚀 部署流程

### 构建配置

#### Next.js 配置

```javascript
// next.config.mjs
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 静态导出配置
  output: 'export',
  trailingSlash: true,

  // 构建优化
  swcMinify: true,

  // 图片配置
  images: {
    unoptimized: true,
    formats: ['image/webp', 'image/avif']
  },

  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react']
  },

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/old-path',
        destination: '/new-path',
        permanent: true,
      },
    ]
  }
}

export default nextConfig
```

#### 构建脚本

```json
{
  "scripts": {
    "build": "next build",
    "build:analyze": "ANALYZE=true next build",
    "build:production": "NODE_ENV=production next build",
    "export": "next export",
    "deploy": "npm run build && npm run export"
  }
}
```

### CI/CD 配置

#### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Build and Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'pnpm'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Run type check
      run: pnpm type-check

    - name: Run linting
      run: pnpm lint

    - name: Run tests
      run: pnpm test

    - name: Build project
      run: pnpm build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: out/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-files
        path: out/

    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./out
```

### 部署验证

#### 构建验证脚本

```javascript
// scripts/build-verification.js
const fs = require('fs')
const path = require('path')

function verifyBuild() {
  const outDir = path.join(process.cwd(), 'out')

  // 检查输出目录是否存在
  if (!fs.existsSync(outDir)) {
    throw new Error('Build output directory not found')
  }

  // 检查关键文件
  const requiredFiles = [
    'index.html',
    '_next/static',
    'tools/enhancement/index.html',
    'cms-216/index.html'
  ]

  for (const file of requiredFiles) {
    const filePath = path.join(outDir, file)
    if (!fs.existsSync(filePath)) {
      throw new Error(`Required file missing: ${file}`)
    }
  }

  // 检查静态资源
  const imagesDir = path.join(outDir, 'images')
  if (!fs.existsSync(imagesDir)) {
    console.warn('Images directory not found')
  }

  console.log('✅ Build verification passed')
}

verifyBuild()
```

## 🔧 故障排除

### 常见开发问题

#### 1. TypeScript 类型错误

```typescript
// 问题：Property 'xxx' does not exist on type 'yyy'
// 解决方案：正确定义类型

// ❌ 错误的类型定义
interface Equipment {
  name: string
}

// ✅ 正确的类型定义
interface Equipment {
  name: string
  level?: number  // 可选属性
  [key: string]: any  // 索引签名（谨慎使用）
}

// 或者使用类型断言
const equipment = data as Equipment
```

#### 2. 组件渲染问题

```typescript
// 问题：组件不更新
// 原因：依赖数组不正确

// ❌ 错误的依赖
useEffect(() => {
  updateComponent()
}, []) // 缺少依赖

// ✅ 正确的依赖
useEffect(() => {
  updateComponent()
}, [dependency1, dependency2])

// 或使用 useCallback
const memoizedCallback = useCallback(() => {
  updateComponent()
}, [dependency1, dependency2])
```

#### 3. 状态更新问题

```typescript
// 问题：状态更新不生效
// 原因：直接修改状态对象

// ❌ 错误的状态更新
const [state, setState] = useState({ items: [] })
state.items.push(newItem) // 直接修改
setState(state) // React 不会重新渲染

// ✅ 正确的状态更新
setState(prevState => ({
  ...prevState,
  items: [...prevState.items, newItem]
}))
```

### 性能问题诊断

#### 1. 组件重渲染分析

```typescript
// 使用 React DevTools Profiler
// 或添加调试代码

function MyComponent(props) {
  console.log('MyComponent rendered', props)

  // 使用 why-did-you-render 库
  return <div>...</div>
}

// 添加显示名称便于调试
MyComponent.displayName = 'MyComponent'
```

#### 2. 内存泄漏检测

```typescript
// 检查事件监听器清理
useEffect(() => {
  const handleResize = () => {
    // 处理逻辑
  }

  window.addEventListener('resize', handleResize)

  // 清理函数
  return () => {
    window.removeEventListener('resize', handleResize)
  }
}, [])

// 检查定时器清理
useEffect(() => {
  const timer = setInterval(() => {
    // 定时任务
  }, 1000)

  return () => {
    clearInterval(timer)
  }
}, [])
```

### 构建问题解决

#### 1. 依赖冲突

```bash
# 清理依赖
rm -rf node_modules
rm pnpm-lock.yaml

# 重新安装
pnpm install

# 检查依赖版本
pnpm list --depth=0
```

#### 2. 内存不足

```bash
# 增加 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
pnpm build

# 或在 package.json 中配置
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' next build"
  }
}
```

---

## 📚 参考资源

### 官方文档

- [Next.js 文档](https://nextjs.org/docs)
- [React 文档](https://react.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

### 社区资源

- [shadcn/ui 组件库](https://ui.shadcn.com/)
- [Radix UI 文档](https://www.radix-ui.com/)
- [Lucide 图标库](https://lucide.dev/)

### 开发工具

- [React DevTools](https://react.dev/learn/react-developer-tools)
- [TypeScript Playground](https://www.typescriptlang.org/play)
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)

---

**维护者**: 小帽子 (QQ: 499151029)
**最后更新**: 2025-06-20
**文档版本**: v1.0.0
```
```
