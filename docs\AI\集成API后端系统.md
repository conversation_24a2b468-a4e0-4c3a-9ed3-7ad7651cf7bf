# 1 全栈Next.js架构实施
我确定使用next.js全栈开发。集成API后端开发
**前置条件检查：**
1. 首先检查 `全栈Next.js架构实施方案.md` 目录是否存在且可访问
2. 能阅读`全栈Next.js架构实施方案.md`文档
3. 能理解`全栈Next.js架构实施方案.md`文档的内容。

我给你的任务：集成API后端开发
基于冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），实施全栈架构升级。
按`全栈Next.js架构实施方案.md`文档，集成用户认证、三级会员系统、虚拟货币系统和完整的API接口系统。

同时我确认了一下几个技术点：
数据库使 PostgreSQL 16
邮件服务使用 Resend
设备指纹使用免费的FingerprintJS。
只有用户个人资料页面需要登录才可以访问。 其他页面没有登录也可访问。


## rewrite
基于冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），按照《全栈Next.js架构实施方案.md》文档实施完整的全栈架构升级。

**前置条件验证：**
1. 确认能够访问和读取项目根目录下的 `全栈Next.js架构实施方案.md` 文件
2. 理解该文档中的技术架构设计和实施计划
3. 基于文档内容进行具体的代码实现

**技术栈确认：**
- 数据库：PostgreSQL 16
- 邮件服务：Resend
- 设备指纹：FingerprintJS（免费版）
- 认证系统：NextAuth.js v5
- ORM：Prisma
- 缓存：Redis

postgres开发配置：
host: 'localhost',
port: 5433,
database: 'mxd_info_db',
user: 'postgres',
password: 'postgres',


**核心功能实现要求：**

1. **用户认证系统**
    - 实现用户注册、登录、密码重置完整流程
    - 集成 NextAuth.js v5 和 Resend 邮件服务
    - 实现 JWT token 管理（access token + refresh token）
    - 添加用户、密码注册流程
    - 添加邮箱验证和账户激活流程

2. **三级会员系统**
    - 游客用户：集成免费版 FingerprintJS 进行设备指纹识别和追踪
    - 黄金用户：付费用户中级权限
    - 钻石用户：付费用户最高权限
    - 实现基于角色的权限控制（RBAC）系统

3. **虚拟货币系统（"欢乐豆"）**
    - 实现虚拟货币的获取、消费、余额管理
    - 添加充值、消费记录、交易日志功能
    - 实现防刷币、交易验证、余额保护等安全机制

4. **API接口系统**
    - 装备强化相关接口（星之力、潜能重设、附加属性）
    - 用户管理接口（注册、登录、权限验证、会员状态）
    - 虚拟货币接口（余额查询、消费扣除、充值记录）
    - 数据管理接口（用户配置、历史记录、统计数据）

**页面访问权限设置：**
- 仅用户个人资料页面（/profile 或 /dashboard）需要登录验证
- 其他所有页面（首页、工具页面、道具展示等）保持无需登录即可访问
- 保持现有 SSG 页面的性能优势

**实施要求：**
1. 严格按照文档中的目录结构重组项目
2. 修改 next.config.mjs 配置以支持 API Routes
3. 实现 Prisma 数据库模式和迁移
4. 添加必要的中间件和安全防护
5. 保持与现有装备强化模拟器等功能的兼容性
6. 提供完整的环境变量配置示例

**输出要求：**
- 提供具体的代码实现和文件修改
- 按照文档中的分阶段实施计划进行
- 确保代码质量和类型安全
- 包含必要的错误处理和安全措施

### 2
基于冒险岛情报站项目（Next.js 14 + TypeScript + SSG配置），按照《全栈Next.js架构实施方案.md》文档实施完整的全栈架构升级。

**前置条件验证：**
1. 首先使用 `view` 工具读取项目根目录下的 `全栈Next.js架构实施方案.md` 文件
2. 分析并理解该文档中的技术架构设计、目录结构重组方案和分阶段实施计划
3. 基于文档内容和当前项目结构，制定具体的代码实现策略

**技术栈配置确认：**
- 数据库：PostgreSQL 16 (开发环境配置: host: localhost, port: 5433, database: mxd_info_db, user: postgres, password: postgres)
- 邮件服务：Resend API
- 设备指纹：FingerprintJS 免费版
- 认证系统：NextAuth.js v5
- ORM：Prisma ORM
- 缓存：Redis
- 状态管理：Zustand
- 表单处理：React Hook Form + Zod 验证

**核心功能实现要求（按优先级排序）：**

1. **项目结构重组和配置修改**
    - 按照文档中的目录结构重组现有项目文件
    - 修改 `next.config.mjs` 移除 `output: 'export'` 配置以支持 API Routes
    - 创建新的目录结构：app/(auth)/, app/(dashboard)/, app/api/ 等
    - 设置环境变量配置文件和示例

2. **数据库设计和初始化**
    - 实现文档中完整的 Prisma Schema 设计
    - 创建用户、角色、权限、虚拟货币相关数据表
    - 编写数据库迁移文件和种子数据脚本
    - 配置数据库连接和 Prisma 客户端

3. **用户认证系统**
    - 集成 NextAuth.js v5 配置
    - 实现用户注册页面（/register）和登录页面（/login）
    - 集成 Resend 邮件服务实现邮箱验证流程
    - 实现密码重置功能（/reset-password）
    - 添加 JWT token 管理和刷新机制
    - 创建认证中间件和路由保护

4. **三级会员系统和权限控制**
    - 实现 RBAC 权限模型（游客、注册用户、黄金用户、钻石用户、管理员）
    - 集成免费版 FingerprintJS 进行设备指纹识别
    - 创建权限检查中间件和组件级权限控制
    - 实现用户仪表板页面（/dashboard）显示会员状态和权限

5. **虚拟货币系统**
    - 实现"欢乐豆"虚拟货币的数据模型和业务逻辑
    - 创建余额查询、消费扣除、充值记录的 API 接口
    - 实现交易安全机制：分布式锁、余额验证、交易日志
    - 添加防刷币检测和异常交易监控
    - 创建交易记录页面和余额管理界面

6. **API接口系统**
    - 创建装备强化相关 API：/api/enhancement/starforce, /api/enhancement/potential, /api/enhancement/additional
    - 实现用户管理 API：/api/users/profile, /api/users/membership
    - 创建虚拟货币 API：/api/currency/balance, /api/currency/consume, /api/currency/transactions
    - 添加速率限制、输入验证和错误处理中间件

**页面访问权限和渲染策略：**
- 保持现有页面的 SSG 渲染：首页（/）、工具页面（/tools/*）、道具展示（/cms-216）
- 仅以下页面需要登录验证：
    - 用户仪表板（/dashboard）- SSR 渲染
    - 个人资料（/profile）- SSR 渲染
    - 交易记录（/transactions）- SSR 渲染
    - 设置页面（/settings）- CSR 渲染
- 认证页面保持 SSG：登录（/login）、注册（/register）、密码重置（/reset-password）

**实施步骤和代码要求：**
1. **第一步**：读取并分析 `全栈Next.js架构实施方案.md` 文档内容
2. **第二步**：修改 `next.config.mjs` 配置文件，移除静态导出配置
3. **第三步**：按照文档重组项目目录结构，创建新的文件夹和移动现有文件
4. **第四步**：实现 Prisma 数据库模式和初始化脚本
5. **第五步**：逐步实现核心功能模块，每个模块包含完整的类型定义、业务逻辑和错误处理
6. **第六步**：创建必要的中间件、工具函数和安全防护措施
7. **第七步**：提供完整的环境变量配置示例和部署说明

**代码质量要求：**
- 使用 TypeScript 严格模式，确保完整的类型安全
- 遵循 Next.js 14 App Router 最佳实践
- 实现完整的错误处理和用户友好的错误提示
- 添加必要的安全措施：输入验证、SQL注入防护、XSS防护、CSRF防护
- 保持与现有装备强化模拟器等功能的完全兼容性
- 代码注释清晰，便于理解和维护

**输出格式要求：**
- 按照实施步骤顺序提供具体的代码实现
- 每个文件修改都要说明修改原因和作用
- 提供完整的文件路径和代码内容
- 包含必要的安装命令和配置说明
- 确保所有代码都经过语法检查和逻辑验证