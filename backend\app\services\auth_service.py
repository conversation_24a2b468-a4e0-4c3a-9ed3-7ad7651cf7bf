"""
Authentication service
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.models.user import User
from app.models.membership import MembershipTier, UserMembership
from app.models.currency import CurrencyAccount
from app.models.transaction import EmailVerification, PasswordReset, UserSession
from app.schemas.auth import UserRegisterRequest, UserLoginRequest
from app.schemas.user import UserResponse
from app.utils.security import (
    get_password_hash, 
    verify_password, 
    create_access_token, 
    create_refresh_token,
    generate_verification_token,
    generate_reset_token
)
from app.services.email_service import email_service
from app.core.config import settings

logger = logging.getLogger(__name__)

class AuthService:
    """认证服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def register_user(self, user_data: UserRegisterRequest) -> Tu<PERSON>[bool, str, Optional[UserResponse]]:
        """
        用户注册
        返回: (成功标志, 消息, 用户信息)
        """
        try:
            # 检查用户名是否已存在
            if user_data.username:
                existing_user = self.db.query(User).filter(User.username == user_data.username).first()
                if existing_user:
                    return False, "用户名已存在", None
            
            # 检查邮箱是否已存在
            if user_data.email:
                existing_user = self.db.query(User).filter(User.email == user_data.email).first()
                if existing_user:
                    if not existing_user.is_verified:
                        return False, "该邮箱已注册但未激活，请查收验证邮件或重新发送激活邮件", None
                    else:
                        return False, "邮箱已被注册", None
            
            # 创建新用户
            password_hash = get_password_hash(user_data.password)
            
            new_user = User(
                username=user_data.username,
                email=user_data.email,
                password_hash=password_hash,
                fingerprint_id=user_data.fingerprint_id,
                is_verified=not bool(user_data.email)  # 如果没有邮箱则直接验证
            )
            
            self.db.add(new_user)
            self.db.flush()  # 获取用户ID
            
            # 分配默认会员等级（注册用户）
            default_tier = self.db.query(MembershipTier).filter(MembershipTier.name == "registered").first()
            if default_tier:
                user_membership = UserMembership(
                    user_id=new_user.id,
                    tier_id=default_tier.id
                )
                self.db.add(user_membership)
            
            # 创建虚拟货币账户
            currency_account = CurrencyAccount(user_id=new_user.id)
            self.db.add(currency_account)
            
            # 如果有邮箱，发送验证邮件
            if user_data.email:
                verification_token = generate_verification_token()
                expires_at = datetime.now(timezone.utc) + timedelta(hours=24)
                
                email_verification = EmailVerification(
                    user_id=new_user.id,
                    email=user_data.email,
                    token=verification_token,
                    expires_at=expires_at
                )
                self.db.add(email_verification)
                self.db.commit()
                
                # 发送验证邮件
                await email_service.send_verification_email(
                    to_email=user_data.email,
                    username=user_data.username or "用户",
                    verification_token=verification_token
                )
                
                message = "注册成功！请查收邮箱验证邮件"
            else:
                self.db.commit()
                message = "注册成功！"
            
            user_response = UserResponse.from_orm(new_user)
            return True, message, user_response
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"User registration failed: {e}")
            return False, "注册失败，请稍后重试", None
    
    async def login_user(self, login_data: UserLoginRequest) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        用户登录
        返回: (成功标志, 消息, 令牌信息)
        """
        try:
            # 查找用户
            query = self.db.query(User)
            if login_data.email:
                query = query.filter(User.email == login_data.email)
            elif login_data.username:
                query = query.filter(User.username == login_data.username)
            else:
                return False, "请提供用户名或邮箱", None
            
            user = query.first()
            if not user:
                return False, "用户名或密码错误", None
            
            # 验证密码
            if not verify_password(login_data.password, user.password_hash):
                return False, "用户名或密码错误", None
            
            # 检查账户状态
            if not user.is_active:
                return False, "账户已被禁用", None
            
            # 检查邮箱验证状态（如果有邮箱）
            if user.email and not user.is_verified:
                return False, "请先验证邮箱后再登录", None
            
            # 更新设备指纹
            if login_data.fingerprint_id:
                user.fingerprint_id = login_data.fingerprint_id
            
            # 更新最后登录时间
            user.last_login = datetime.now(timezone.utc)
            
            # 获取用户会员信息
            membership = self.db.query(UserMembership).join(MembershipTier).filter(
                UserMembership.user_id == user.id,
                UserMembership.is_active == True
            ).first()
            
            membership_level = membership.tier.level if membership else 0
            permissions = membership.tier.permissions if membership else {}
            
            # 创建会话
            session_token = generate_verification_token(64)
            expires_at = datetime.now(timezone.utc) + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
            
            user_session = UserSession(
                user_id=user.id,
                session_token=session_token,
                expires_at=expires_at
            )
            self.db.add(user_session)
            self.db.flush()
            
            # 生成令牌
            user_data = {
                "user_id": user.id,
                "username": user.username,
                "email": user.email,
                "membership_level": membership_level,
                "permissions": permissions
            }
            
            access_token = create_access_token(str(user.id), user_data)
            refresh_token = create_refresh_token(str(user.id), str(user_session.id))
            
            # 更新会话的refresh_token
            user_session.refresh_token = refresh_token
            
            self.db.commit()
            
            return True, "登录成功", {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "user": UserResponse.from_orm(user)
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"User login failed: {e}")
            return False, "登录失败，请稍后重试", None

    async def verify_email(self, token: str) -> Tuple[bool, str]:
        """
        验证邮箱
        返回: (成功标志, 消息)
        """
        try:
            # 查找验证记录
            verification = self.db.query(EmailVerification).filter(
                EmailVerification.token == token,
                EmailVerification.verified_at.is_(None)
            ).first()

            if not verification:
                return False, "验证链接无效或已使用"

            # 检查是否过期
            if verification.expires_at < datetime.now(timezone.utc):
                return False, "验证链接已过期"

            # 更新用户验证状态
            user = self.db.query(User).filter(User.id == verification.user_id).first()
            if not user:
                return False, "用户不存在"

            user.is_verified = True
            user.email = verification.email  # 确保邮箱地址正确
            verification.verified_at = datetime.now(timezone.utc)

            self.db.commit()

            return True, "邮箱验证成功！"

        except Exception as e:
            self.db.rollback()
            logger.error(f"Email verification failed: {e}")
            return False, "验证失败，请稍后重试"

    async def resend_verification_email(self, email: str) -> Tuple[bool, str]:
        """
        重新发送验证邮件
        返回: (成功标志, 消息)
        """
        try:
            # 查找用户
            user = self.db.query(User).filter(User.email == email).first()
            if not user:
                return False, "邮箱地址不存在"

            if user.is_verified:
                return False, "邮箱已验证，无需重复验证"

            # 检查是否有未过期的验证记录
            existing_verification = self.db.query(EmailVerification).filter(
                EmailVerification.user_id == user.id,
                EmailVerification.verified_at.is_(None),
                EmailVerification.expires_at > datetime.now(timezone.utc)
            ).first()

            if existing_verification:
                # 重新发送现有的验证邮件
                await email_service.send_verification_email(
                    to_email=email,
                    username=user.username or "用户",
                    verification_token=existing_verification.token
                )
                return True, "验证邮件已重新发送"

            # 创建新的验证记录
            verification_token = generate_verification_token()
            expires_at = datetime.now(timezone.utc) + timedelta(hours=24)

            email_verification = EmailVerification(
                user_id=user.id,
                email=email,
                token=verification_token,
                expires_at=expires_at
            )
            self.db.add(email_verification)
            self.db.commit()

            # 发送验证邮件
            await email_service.send_verification_email(
                to_email=email,
                username=user.username or "用户",
                verification_token=verification_token
            )

            return True, "验证邮件已发送"

        except Exception as e:
            self.db.rollback()
            logger.error(f"Resend verification email failed: {e}")
            return False, "发送失败，请稍后重试"

    async def request_password_reset(self, email: str) -> Tuple[bool, str]:
        """
        请求密码重置
        返回: (成功标志, 消息)
        """
        try:
            # 查找用户
            user = self.db.query(User).filter(User.email == email).first()
            if not user:
                # 为了安全，不暴露邮箱是否存在
                return True, "如果邮箱存在，重置链接已发送"

            # 创建重置记录
            reset_token = generate_reset_token()
            expires_at = datetime.now(timezone.utc) + timedelta(hours=1)

            password_reset = PasswordReset(
                user_id=user.id,
                token=reset_token,
                expires_at=expires_at
            )
            self.db.add(password_reset)
            self.db.commit()

            # 发送重置邮件
            await email_service.send_password_reset_email(
                to_email=email,
                username=user.username or "用户",
                reset_token=reset_token
            )

            return True, "如果邮箱存在，重置链接已发送"

        except Exception as e:
            self.db.rollback()
            logger.error(f"Password reset request failed: {e}")
            return False, "请求失败，请稍后重试"

    async def reset_password(self, token: str, new_password: str) -> Tuple[bool, str]:
        """
        重置密码
        返回: (成功标志, 消息)
        """
        try:
            # 查找重置记录
            reset_record = self.db.query(PasswordReset).filter(
                PasswordReset.token == token,
                PasswordReset.used_at.is_(None)
            ).first()

            if not reset_record:
                return False, "重置链接无效或已使用"

            # 检查是否过期
            if reset_record.expires_at < datetime.now(timezone.utc):
                return False, "重置链接已过期"

            # 更新用户密码
            user = self.db.query(User).filter(User.id == reset_record.user_id).first()
            if not user:
                return False, "用户不存在"

            user.password_hash = get_password_hash(new_password)
            reset_record.used_at = datetime.now(timezone.utc)

            # 清除所有用户会话（强制重新登录）
            self.db.query(UserSession).filter(UserSession.user_id == user.id).delete()

            self.db.commit()

            return True, "密码重置成功！"

        except Exception as e:
            self.db.rollback()
            logger.error(f"Password reset failed: {e}")
            return False, "重置失败，请稍后重试"

    async def logout_user(self, refresh_token: Optional[str] = None) -> Tuple[bool, str]:
        """
        用户登出
        返回: (成功标志, 消息)
        """
        try:
            if refresh_token:
                # 删除特定会话
                session = self.db.query(UserSession).filter(
                    UserSession.refresh_token == refresh_token
                ).first()
                if session:
                    self.db.delete(session)

            self.db.commit()
            return True, "登出成功"

        except Exception as e:
            self.db.rollback()
            logger.error(f"User logout failed: {e}")
            return False, "登出失败"
