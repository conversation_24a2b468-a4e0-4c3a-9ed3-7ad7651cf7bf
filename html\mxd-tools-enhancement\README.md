# MSU 装备强化模拟器

## 📋 项目简介

MSU装备强化模拟器是一个高度还原MapleStory Universe游戏中装备强化系统的网页应用。项目使用纯前端技术栈实现，包含完整的UI界面、动画特效、游戏逻辑和数据管理。

### 🎯 核心特性

- **100% 原版UI还原**：使用真实游戏资源，像素级精确还原
- **三种强化系统**：星力强化(0-25星)、潜能重设、额外属性强化
- **完整装备数据库**：支持itemList.json中的所有装备数据
- **实时特效系统**：帧动画特效，90ms/帧精确时序
- **迷你游戏机制**：高等级星力强化的时机挑战
- **智能加载系统**：带进度显示的资源预加载

## 🚀 快速开始

### 环境要求

- 现代浏览器 (Chrome 88+, Firefox 85+, Safari 14+)
- 本地HTTP服务器 (推荐Live Server)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [repository-url]
   cd msu-equipment-enhancement-simulator
   ```

2. **启动本地服务器**
   ```bash
   # 使用Live Server (推荐)
   # 或使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   ```

3. **打开浏览器**
   ```
   访问 http://localhost:8000
   ```

## 🎮 使用指南

### 基本操作

#### 装备管理
- **左键点击装备槽**：随机选择装备
- **右键点击装备槽**：清除当前装备
- **键盘快捷键**：
  - `E` - 随机选择装备
  - `R` - 清除装备

#### 界面导航
- **标签页切换**：
  - `1` - 星力强化
  - `2` - 潜能重设
  - `3` - 额外属性强化
- **操作控制**：
  - `Enter` - 开始强化
  - `Escape` - 取消强化
  - `Space` - 迷你游戏中停止

### 功能详解

#### 🌟 星力强化系统
- **等级范围**：0-25星
- **概率机制**：基于官方数据的真实概率
- **镇护功能**：
  - Star Catching：捕获星星机制
  - 防止Major Failure：防止装备破坏
- **迷你游戏**：10星以上需要完成时机挑战

#### ⚡ 潜能重设
- **功能**：重新设定装备潜在能力
- **成功率**：100%
- **费用**：固定费用

#### 🎯 额外属性强化
- **功能**：提升装备额外能力
- **成功率**：100%
- **费用**：固定费用

## 📁 项目结构

```
msu-equipment-enhancement-simulator/
├── index.html                 # 主页面
├── styles.css                # 样式文件
├── script.js                 # 主要逻辑
├── itemList.json             # 装备数据库
├── UIEquipEnchant.img.xml    # UI资源映射
├── UIEquipEnchant.img/       # UI图片资源
│   ├── Main/                 # 主界面资源
│   └── Common/               # 通用资源
├── itempic/                  # 装备图片
├── README.md                 # 项目说明
└── development.md            # 开发文档
```

## 🎨 界面预览

### Loading界面
- 渐变背景设计
- 实时进度显示
- 使用提示展示
- 平滑过渡动画

### 主界面
- 分层UI系统 (Z-Index 0-1000)
- 响应式按钮状态
- 实时属性显示
- 帧动画特效

## 🔧 技术栈

- **前端框架**：原生 JavaScript (ES6+)
- **样式技术**：CSS3 (Grid, Flexbox, Animations)
- **资源管理**：异步加载 + 进度跟踪
- **数据格式**：JSON
- **兼容性**：现代浏览器

## 📊 功能特性

### 🎯 核心功能
- [x] 三种强化类型完整实现
- [x] 真实概率计算系统
- [x] 装备数据库集成
- [x] 特效动画系统
- [x] 迷你游戏机制
- [x] 镇护功能支持

### 🎨 用户体验
- [x] Loading界面与进度显示
- [x] 平滑过渡动画
- [x] 键盘快捷键支持
- [x] 响应式设计
- [x] 错误处理机制

### 🛠️ 开发工具
- [x] 分层控制面板
- [x] 控制台调试输出
- [x] 资源加载状态跟踪
- [x] 模块化代码结构

## 🎮 游戏机制

### 星力强化概率表
| 星级 | 成功率 | 失败率 | 重大失败率 |
|------|--------|--------|------------|
| 0-9  | 95%-55% | 5%-45% | 0% |
| 10-14 | 50%-30% | 45%-65% | 5% |
| 15-21 | 30% | 67.9%-61.5% | 2.1%-8.5% |
| 22-24 | 3%-1% | 77.6%-59.4% | 19.4%-39.6% |

### 费用计算
- **基础费用**：1,000,000 - 16,777,216,000,000 金币
- **镇护费用**：额外费用计算
- **失败惩罚**：装备等级下降或破坏

## 🚧 开发状态

### 已完成功能
- ✅ 完整UI界面实现
- ✅ 三种强化系统
- ✅ 装备数据库集成
- ✅ 特效动画系统
- ✅ Loading预加载系统
- ✅ 键盘快捷键
- ✅ 分层控制系统

### 计划功能
- 🔄 更多装备类型支持
- 🔄 用户数据持久化
- 🔄 统计数据分析
- 🔄 音效系统
- 🔄 移动端适配

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目仅供学习和研究使用。所有游戏资源版权归原厂商所有。

## 📞 联系方式

- 项目维护者：[Your Name]
- 项目地址：[Repository URL]
- 问题反馈：[Issues URL]

---

**注意**：本项目为模拟器，仅供娱乐和学习使用，不涉及真实游戏数据或账户。 