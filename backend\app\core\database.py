"""
Database configuration and session management
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import redis
from typing import Generator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# PostgreSQL Database Engine
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,
    echo=settings.DEBUG,
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()

# Redis connection
redis_client = redis.from_url(
    settings.REDIS_URL,
    password=settings.REDIS_PASSWORD,
    db=settings.REDIS_DB,
    decode_responses=True,
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True,
    health_check_interval=30,
)

def get_db() -> Generator:
    """
    Database session dependency
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_redis() -> redis.Redis:
    """
    Redis client dependency
    """
    return redis_client

async def init_db():
    """
    Initialize database tables
    """
    try:
        # Import all models to ensure they are registered
        from app.models import user, membership, currency, transaction
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
        # Initialize default data
        await init_default_data()
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

async def init_default_data():
    """
    Initialize default data (membership tiers, etc.)
    """
    try:
        from app.models.membership import MembershipTier
        from app.schemas.membership import MembershipTierCreate
        
        db = SessionLocal()
        
        # Check if membership tiers already exist
        existing_tiers = db.query(MembershipTier).count()
        if existing_tiers > 0:
            logger.info("Default membership tiers already exist")
            db.close()
            return
        
        # Create default membership tiers
        default_tiers = [
            {
                "name": "guest",
                "display_name": "游客",
                "level": 0,
                "description": "未注册用户，通过设备指纹识别",
                "permissions": {
                    "enhancement_daily_limit": 10,
                    "features": ["basic_tools"],
                    "currency_earning": False,
                    "save_progress": False,
                    "advanced_tools": False,
                    "premium_features": False
                }
            },
            {
                "name": "registered",
                "display_name": "注册用户",
                "level": 1,
                "description": "已注册但未付费用户",
                "permissions": {
                    "enhancement_daily_limit": 50,
                    "features": ["basic_tools", "save_progress"],
                    "currency_earning": True,
                    "save_progress": True,
                    "advanced_tools": False,
                    "premium_features": False
                }
            },
            {
                "name": "gold",
                "display_name": "黄金用户",
                "level": 2,
                "description": "付费黄金会员",
                "permissions": {
                    "enhancement_daily_limit": 200,
                    "features": ["basic_tools", "save_progress", "advanced_tools"],
                    "currency_earning": True,
                    "save_progress": True,
                    "advanced_tools": True,
                    "premium_features": False
                }
            },
            {
                "name": "diamond",
                "display_name": "钻石用户",
                "level": 3,
                "description": "付费钻石会员",
                "permissions": {
                    "enhancement_daily_limit": 1000,
                    "features": ["basic_tools", "save_progress", "advanced_tools", "premium_features"],
                    "currency_earning": True,
                    "save_progress": True,
                    "advanced_tools": True,
                    "premium_features": True
                }
            },
            {
                "name": "admin",
                "display_name": "管理员",
                "level": 4,
                "description": "系统管理员",
                "permissions": {
                    "enhancement_daily_limit": -1,
                    "features": ["all"],
                    "currency_earning": True,
                    "save_progress": True,
                    "advanced_tools": True,
                    "premium_features": True,
                    "admin_access": True
                }
            }
        ]
        
        for tier_data in default_tiers:
            tier = MembershipTier(**tier_data)
            db.add(tier)
        
        db.commit()
        logger.info("Default membership tiers created successfully")
        db.close()
        
    except Exception as e:
        logger.error(f"Failed to initialize default data: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()
        raise

async def check_db_connection():
    """
    Check database connection
    """
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        logger.info("Database connection successful")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

async def check_redis_connection():
    """
    Check Redis connection
    """
    try:
        redis_client.ping()
        logger.info("Redis connection successful")
        return True
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")
        return False
