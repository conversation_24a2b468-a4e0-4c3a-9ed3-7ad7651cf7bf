# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Redis
redis==5.0.1
hiredis==2.2.3

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Email
resend==0.6.0

# HTTP client
httpx==0.25.2

# Environment variables
python-dotenv==1.0.0

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# CORS (built into FastAPI)
# python-cors not needed

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Logging
structlog==23.2.0

# Date and time
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10
