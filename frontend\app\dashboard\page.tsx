'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useAuthStore } from '@/store/auth-store'
import { apiClient } from '@/lib/api-client'
import { 
  User, 
  Crown, 
  Coins, 
  Calendar,
  Mail,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface UserProfile {
  id: number
  username: string
  email?: string
  is_active: boolean
  is_verified: boolean
  created_at: string
  last_login?: string
  membership_level: number
  membership_name: string
  currency_balance: number
}

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuthStore()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await apiClient.getCurrentUser()
        if (response.success) {
          // 获取详细的用户资料
          const profileResponse = await fetch('/api/v1/users/profile', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
          })
          
          if (profileResponse.ok) {
            const profileData = await profileResponse.json()
            if (profileData.success) {
              setProfile(profileData.data)
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch profile:', error)
      } finally {
        setLoading(false)
      }
    }

    if (isAuthenticated) {
      fetchProfile()
    }
  }, [isAuthenticated])

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const membershipColors = {
    0: 'bg-gray-100 text-gray-800',
    1: 'bg-blue-100 text-blue-800',
    2: 'bg-yellow-100 text-yellow-800',
    3: 'bg-purple-100 text-purple-800',
    4: 'bg-red-100 text-red-800'
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">用户仪表板</h1>
          <p className="text-muted-foreground">
            欢迎回来，{user?.username}！
          </p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* 用户信息卡片 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">用户信息</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">用户名</span>
                <span className="font-medium">{user?.username}</span>
              </div>
              {user?.email && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">邮箱</span>
                  <div className="flex items-center gap-1">
                    <span className="text-sm">{user.email}</span>
                    {user.is_verified ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-yellow-500" />
                    )}
                  </div>
                </div>
              )}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">状态</span>
                <Badge variant={user?.is_active ? 'default' : 'secondary'}>
                  {user?.is_active ? '活跃' : '禁用'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 会员等级卡片 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">会员等级</CardTitle>
            <Crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">当前等级</span>
                <Badge className={membershipColors[profile?.membership_level as keyof typeof membershipColors] || membershipColors[0]}>
                  {profile?.membership_name || '游客'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">等级数值</span>
                <span className="font-medium">Level {profile?.membership_level || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 欢乐豆余额卡片 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">欢乐豆余额</CardTitle>
            <Coins className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold">
                {profile?.currency_balance?.toLocaleString() || '0'}
              </div>
              <p className="text-xs text-muted-foreground">
                可用于装备强化模拟等功能
              </p>
              <Button size="sm" className="w-full">
                充值欢乐豆
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 账户创建时间 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">注册时间</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-sm">
                {profile?.created_at ? new Date(profile.created_at).toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }) : '未知'}
              </div>
              <p className="text-xs text-muted-foreground">
                感谢您成为冒险岛情报站的一员
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 最后登录时间 */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">最后登录</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-sm">
                {profile?.last_login ? new Date(profile.last_login).toLocaleString('zh-CN') : '首次登录'}
              </div>
              <p className="text-xs text-muted-foreground">
                上次访问时间
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 邮箱验证状态 */}
        {user?.email && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">邮箱验证</CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  {user.is_verified ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-600">已验证</span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm text-yellow-600">未验证</span>
                    </>
                  )}
                </div>
                {!user.is_verified && (
                  <Button size="sm" variant="outline" className="w-full">
                    重新发送验证邮件
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
