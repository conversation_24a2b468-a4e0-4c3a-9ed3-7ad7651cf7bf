# ===================
# 应用基础配置
# ===================
APP_NAME=MapleStory Info Station API
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production

# ===================
# 数据库配置
# ===================
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/mxd_info_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# ===================
# Redis 配置
# ===================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# ===================
# 认证配置
# ===================
JWT_SECRET=change-this-to-a-random-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# ===================
# 邮件服务配置
# ===================
RESEND_API_KEY=your-resend-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Your App Name

# ===================
# CORS 配置
# ===================
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# ===================
# 安全配置
# ===================
SECRET_KEY=another-random-secret-key
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# ===================
# 限流配置
# ===================
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# ===================
# 文件上传配置
# ===================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# ===================
# 外部服务配置
# ===================
FINGERPRINT_WEBHOOK_SECRET=your-fingerprint-webhook-secret
