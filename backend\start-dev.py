#!/usr/bin/env python3
"""
Development server startup script with virtual environment support
"""

import uvicorn
import logging
import sys
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def ensure_venv():
    """Ensure we're running in the virtual environment"""
    backend_dir = Path(__file__).parent
    venv_python = backend_dir / ".venv" / "Scripts" / "python.exe"

    # Check if we're already in the virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.info("✓ Running in virtual environment")
        return True

    # Check if virtual environment exists
    if venv_python.exists():
        logger.info("Virtual environment found, but not activated")
        logger.info("Please run: .venv\\Scripts\\activate")
        return False
    else:
        logger.error("Virtual environment not found. Please run: python -m venv .venv")
        return False

async def startup_checks():
    """Perform startup checks"""
    logger.info("Performing startup checks...")

    # Check virtual environment
    if not ensure_venv():
        return False

    # For now, skip database checks to test basic functionality
    logger.info("⚠ Skipping database checks for initial testing")

    return True

if __name__ == "__main__":
    # Run startup checks
    import asyncio
    startup_success = asyncio.run(startup_checks())

    if not startup_success:
        logger.error("Startup checks failed. Please fix the issues and try again.")
        sys.exit(1)

    logger.info("Starting development server...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        reload_dirs=["app"]
    )
