#!/usr/bin/env python3
"""
Development server startup script
"""

import uvicorn
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def startup_checks():
    """Perform startup checks"""
    logger.info("Performing startup checks...")

    # For now, skip database checks to test basic functionality
    logger.info("⚠ Skipping database checks for initial testing")

    return True

if __name__ == "__main__":
    logger.info("Starting development server...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        reload_dirs=["app"]
    )
