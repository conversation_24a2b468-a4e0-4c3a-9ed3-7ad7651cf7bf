#!/usr/bin/env python3
"""
Development server startup script
"""

import uvicorn
import asyncio
import logging
from app.core.database import check_db_connection, check_redis_connection, init_db

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def startup_checks():
    """Perform startup checks"""
    logger.info("Performing startup checks...")
    
    # Check database connection
    if await check_db_connection():
        logger.info("✓ Database connection successful")
    else:
        logger.error("✗ Database connection failed")
        return False
    
    # Check Redis connection
    if await check_redis_connection():
        logger.info("✓ Redis connection successful")
    else:
        logger.error("✗ Redis connection failed")
        return False
    
    # Initialize database
    try:
        await init_db()
        logger.info("✓ Database initialization successful")
    except Exception as e:
        logger.error(f"✗ Database initialization failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    # Run startup checks
    startup_success = asyncio.run(startup_checks())
    
    if not startup_success:
        logger.error("Startup checks failed. Please fix the issues and try again.")
        exit(1)
    
    logger.info("Starting development server...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        reload_dirs=["app"]
    )
