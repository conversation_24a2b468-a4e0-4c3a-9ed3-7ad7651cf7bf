'use client'

import { ResultOverlayProps } from '@/types/enhancement'

export default function ResultOverlay({ isVisible, result, onClose }: ResultOverlayProps) {
  if (!isVisible || !result) {
    return null
  }
  
  const getResultColor = () => {
    switch (result.type) {
      case 'success':
        return 'text-green-400'
      case 'failed':
        return 'text-red-400'
      case 'major_failure':
        return 'text-red-600'
      default:
        return 'text-gray-400'
    }
  }
  
  const getResultIcon = () => {
    switch (result.type) {
      case 'success':
        return '✅'
      case 'failed':
        return '❌'
      case 'major_failure':
        return '💥'
      default:
        return '❓'
    }
  }
  
  const getResultTitle = () => {
    switch (result.type) {
      case 'success':
        return '强化成功！'
      case 'failed':
        return '强化失败'
      case 'major_failure':
        return '装备破坏！'
      default:
        return '强化结果'
    }
  }
  
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg p-8 max-w-md w-full mx-4 text-center">
        {/* 结果图标 */}
        <div className="text-6xl mb-4">
          {getResultIcon()}
        </div>
        
        {/* 结果标题 */}
        <h3 className={`text-2xl font-bold mb-4 ${getResultColor()}`}>
          {getResultTitle()}
        </h3>
        
        {/* 结果消息 */}
        <p className="text-gray-300 text-lg mb-6">
          {result.message}
        </p>
        
        {/* 等级变化信息 */}
        {result.previousLevel !== undefined && result.previousLevel !== result.level && (
          <div className="mb-6 p-4 bg-gray-800 rounded-lg">
            <div className="text-sm text-gray-400 mb-2">等级变化</div>
            <div className="flex items-center justify-center space-x-4">
              <span className="text-blue-400 font-bold">
                {result.previousLevel} 星
              </span>
              <span className="text-gray-400">→</span>
              <span className={`font-bold ${result.level > result.previousLevel ? 'text-green-400' : 'text-red-400'}`}>
                {result.level} 星
              </span>
            </div>
          </div>
        )}
        
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
        >
          确定
        </button>
      </div>
    </div>
  )
}
