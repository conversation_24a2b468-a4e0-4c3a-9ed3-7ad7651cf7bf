'use client'

import { useEffect, useState } from 'react'
import { EffectRendererProps } from '@/types/enhancement'

const MAX_FRAMES = {
  standby: 16,
  progress: 16,
  success: 18,
  failed: 16,
}

export default function EffectRenderer({ animation, enhancementType }: EffectRendererProps) {
  const [currentFrame, setCurrentFrame] = useState(0)
  
  useEffect(() => {
    if (!animation.isActive || enhancementType !== 'starforce') {
      return
    }
    
    const maxFrames = MAX_FRAMES[animation.effectType]
    const frameInterval = 90 // 90ms per frame
    
    const timer = setInterval(() => {
      setCurrentFrame(prev => {
        const nextFrame = prev + 1
        
        if (nextFrame >= maxFrames) {
          if (animation.isLooping && (animation.effectType === 'standby' || animation.effectType === 'progress')) {
            return 0 // Loop back to start
          } else {
            // Stop animation for result effects
            return prev
          }
        }
        
        return nextFrame
      })
    }, frameInterval)
    
    return () => clearInterval(timer)
  }, [animation.isActive, animation.effectType, animation.isLooping, enhancementType])
  
  // Reset frame when effect type changes
  useEffect(() => {
    setCurrentFrame(0)
  }, [animation.effectType])
  
  if (!animation.isActive || enhancementType !== 'starforce') {
    return null
  }
  
  const getEffectImagePath = () => {
    const { effectType } = animation
    
    switch (effectType) {
      case 'standby':
        return `/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Standby/${currentFrame}.png`
      case 'progress':
        return `/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Progress/Loop/${currentFrame}.png`
      case 'success':
        return `/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Result/Success/${currentFrame}.png`
      case 'failed':
        return `/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Result/Failed/${currentFrame}.png`
      default:
        return ''
    }
  }
  
  return (
    <div
      className={`
        absolute w-[509px] h-[153px] bg-cover bg-center bg-no-repeat z-[4] pointer-events-none
        transition-opacity duration-300
        ${animation.isActive ? 'opacity-100' : 'opacity-0'}
      `}
      style={{
        top: '126px',
        left: '-4px',
        backgroundImage: `url(${getEffectImagePath()})`,
      }}
    />
  )
}
