"""
Authentication API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import time

from app.core.deps import get_db
from app.schemas.auth import (
    UserRegisterRequest,
    UserLoginRequest,
    EmailVerificationRequest,
    ResendVerificationRequest,
    PasswordResetRequest,
    PasswordResetConfirmRequest,
    LogoutRequest
)
from app.services.auth_service import AuthService

router = APIRouter()

@router.post("/register", response_model=dict)
async def register(
    user_data: UserRegisterRequest,
    db: Session = Depends(get_db)
):
    """用户注册"""
    auth_service = AuthService(db)
    success, message, user = await auth_service.register_user(user_data)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )

    return {
        "success": True,
        "data": {
            "user": user,
            "message": message
        },
        "timestamp": time.time()
    }

@router.post("/login", response_model=dict)
async def login(
    login_data: UserLoginRequest,
    db: Session = Depends(get_db)
):
    """用户登录"""
    auth_service = AuthService(db)
    success, message, token_data = await auth_service.login_user(login_data)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=message
        )

    return {
        "success": True,
        "data": token_data,
        "timestamp": time.time()
    }

@router.post("/verify-email", response_model=dict)
async def verify_email(
    verification_data: EmailVerificationRequest,
    db: Session = Depends(get_db)
):
    """邮箱验证"""
    auth_service = AuthService(db)
    success, message = await auth_service.verify_email(verification_data.token)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )

    return {
        "success": True,
        "data": {
            "message": message
        },
        "timestamp": time.time()
    }

@router.post("/resend-verification", response_model=dict)
async def resend_verification(
    resend_data: ResendVerificationRequest,
    db: Session = Depends(get_db)
):
    """重新发送验证邮件"""
    auth_service = AuthService(db)
    success, message = await auth_service.resend_verification_email(resend_data.email)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )

    return {
        "success": True,
        "data": {
            "message": message
        },
        "timestamp": time.time()
    }

@router.post("/forgot-password", response_model=dict)
async def forgot_password(
    reset_data: PasswordResetRequest,
    db: Session = Depends(get_db)
):
    """请求密码重置"""
    auth_service = AuthService(db)
    _, message = await auth_service.request_password_reset(reset_data.email)

    return {
        "success": True,
        "data": {
            "message": message
        },
        "timestamp": time.time()
    }

@router.post("/reset-password", response_model=dict)
async def reset_password(
    reset_data: PasswordResetConfirmRequest,
    db: Session = Depends(get_db)
):
    """确认密码重置"""
    auth_service = AuthService(db)
    success, message = await auth_service.reset_password(
        reset_data.token,
        reset_data.new_password
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )

    return {
        "success": True,
        "data": {
            "message": message
        },
        "timestamp": time.time()
    }

@router.post("/logout", response_model=dict)
async def logout(
    logout_data: LogoutRequest,
    db: Session = Depends(get_db)
):
    """用户登出"""
    auth_service = AuthService(db)
    _, message = await auth_service.logout_user(logout_data.refresh_token)

    return {
        "success": True,
        "data": {
            "message": message
        },
        "timestamp": time.time()
    }
