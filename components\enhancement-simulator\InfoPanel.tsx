'use client'

import { InfoPanelProps } from '@/types/enhancement'
import { formatNumber } from '@/lib/enhancement-utils'

export default function InfoPanel({
  enhancementType,
  level,
  probabilities,
  cost,
  stats,
  starcatchEnabled,
  preventEnabled,
  onStarcatchChange,
  onPreventChange,
}: InfoPanelProps) {
  return (
    <>
      {/* 信息面板背景 */}
      <div
        className="absolute w-[466px] h-[254px] bg-cover bg-no-repeat z-[6]"
        style={{
          top: '280px',
          left: '21px',
          backgroundImage: 'url(/images/UIEquipEnchant/Main/Info/Starforce/Background.png)',
        }}
      />
      
      {/* 概率信息内容 - 严格按照原版位置 */}
      <div
        className="absolute z-[7]"
        style={{
          top: '310px',
          left: '21px',
          width: '466px',
          height: '100px',
        }}
      >
        {/* 第一行概率 */}
        <div className="relative h-[26px] mb-0">
          {/* Success概率 */}
          <div
            className="absolute flex items-center justify-between bg-transparent border-none p-0 text-[11px] w-[170px]"
            style={{
              top: '16px',
              left: '18px',
            }}
          >
            <span className="text-white whitespace-nowrap">Success概率</span>
            <span className="text-white font-bold">
              {probabilities.success.toFixed(2)}%
            </span>
          </div>

          {/* Major Failure概率 */}
          <div
            className="absolute flex items-center justify-between bg-transparent border-none p-0 text-[11px] w-[170px]"
            style={{
              top: '16px',
              left: '253px',
            }}
          >
            <span className="text-white whitespace-nowrap">Major Failure概率</span>
            <span className="text-white font-bold">
              {probabilities.major_failure.toFixed(2)}%
            </span>
          </div>
        </div>

        {/* 第二行概率 */}
        <div className="relative h-[26px]">
          {/* Failure(Keep)概率 */}
          <div
            className="absolute flex items-center justify-between bg-transparent border-none p-0 text-[11px] w-[170px]"
            style={{
              top: '16px',
              left: '19px',
            }}
          >
            <span className="text-white whitespace-nowrap">Failure(Keep)概率</span>
            <span className="text-white font-bold">
              {probabilities.failure.toFixed(2)}%
            </span>
          </div>

          {/* Failure(Drop)概率 */}
          <div
            className="absolute flex items-center justify-between bg-transparent border-none p-0 text-[11px] w-[170px]"
            style={{
              top: '16px',
              left: '253px',
            }}
          >
            <span className="text-white whitespace-nowrap">Failure(Drop)概率</span>
            <span className="text-white font-bold">
              {probabilities.failure_drop.toFixed(2)}%
            </span>
          </div>
        </div>
      </div>
      
      {/* 属性变化信息 - 按照原版布局 */}
      <div
        className="absolute z-[7]"
        style={{
          top: '450px',
          left: '90px',
          width: '426px',
        }}
      >
        <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-[11px]">
          <div className="flex items-center">
            <span className="text-white">• STR</span>
            <span className="text-white ml-4">{stats.current.STR}</span>
            <span className="text-white mx-2">⟩</span>
            <span className="text-green-500">{stats.next.STR}</span>
          </div>
          <div className="flex items-center">
            <span className="text-white">• DEX</span>
            <span className="text-white ml-4">{stats.current.DEX}</span>
            <span className="text-white mx-2">⟩</span>
            <span className="text-green-500">{stats.next.DEX}</span>
          </div>
          <div className="flex items-center">
            <span className="text-white">• INT</span>
            <span className="text-white ml-4">{stats.current.INT}</span>
            <span className="text-white mx-2">⟩</span>
            <span className="text-green-500">{stats.next.INT}</span>
          </div>
          <div className="flex items-center">
            <span className="text-white">• LUK</span>
            <span className="text-white ml-4">{stats.current.LUK}</span>
            <span className="text-white mx-2">⟩</span>
            <span className="text-green-500">{stats.next.LUK}</span>
          </div>
          <div className="flex items-center">
            <span className="text-white">• MaxHP</span>
            <span className="text-white ml-2">{stats.current.MaxHP}</span>
            <span className="text-white mx-2">⟩</span>
            <span className="text-green-500">{stats.next.MaxHP}</span>
          </div>
          <div className="flex items-center">
            <span className="text-white">• DEF</span>
            <span className="text-white ml-4">{stats.current.DEF}</span>
            <span className="text-white mx-2">⟩</span>
            <span className="text-green-500">{stats.next.DEF}</span>
          </div>
        </div>
      </div>
      
      {/* Star Catching 复选框 - 精确对齐背景图片文字 */}
      {enhancementType === 'starforce' && (
        <div
          className="absolute z-[7]"
          style={{
            top: '380px',
            left: '195px',
            width: '16px',
            height: '16px',
          }}
        >
          <input
            type="checkbox"
            id="starcatch-checkbox"
            checked={starcatchEnabled}
            onChange={(e) => onStarcatchChange(e.target.checked)}
            className="opacity-0 w-0 h-0"
          />
          <label
            htmlFor="starcatch-checkbox"
            className="absolute cursor-pointer top-0 left-0 w-4 h-4 bg-gray-800 border-2 border-gray-600 rounded-sm transition-all duration-200 hover:border-blue-400"
            style={{
              boxShadow: starcatchEnabled
                ? '0 0 0 1px rgba(59, 130, 246, 0.5), inset 0 0 0 2px rgba(59, 130, 246, 0.8)'
                : 'inset 0 1px 2px rgba(0, 0, 0, 0.3)'
            }}
          >
            {starcatchEnabled && (
              <span
                className="absolute w-2 h-2 left-0.5 top-0.5 bg-blue-400 rounded-sm"
                style={{
                  boxShadow: '0 0 4px rgba(59, 130, 246, 0.8)'
                }}
              />
            )}
          </label>
        </div>
      )}

      {/* Major Failure Prevention 复选框 - 精确对齐背景图片文字 */}
      {enhancementType === 'starforce' && (
        <div
          className="absolute z-[7]"
          style={{
            top: '380px',
            left: '445px',
            width: '16px',
            height: '16px',
          }}
        >
          <input
            type="checkbox"
            id="prevent-checkbox"
            checked={preventEnabled}
            onChange={(e) => onPreventChange(e.target.checked)}
            className="opacity-0 w-0 h-0"
          />
          <label
            htmlFor="prevent-checkbox"
            className="absolute cursor-pointer top-0 left-0 w-4 h-4 bg-gray-800 border-2 border-gray-600 rounded-sm transition-all duration-200 hover:border-blue-400"
            style={{
              boxShadow: preventEnabled
                ? '0 0 0 1px rgba(59, 130, 246, 0.5), inset 0 0 0 2px rgba(59, 130, 246, 0.8)'
                : 'inset 0 1px 2px rgba(0, 0, 0, 0.3)'
            }}
          >
            {preventEnabled && (
              <span
                className="absolute w-2 h-2 left-0.5 top-0.5 bg-blue-400 rounded-sm"
                style={{
                  boxShadow: '0 0 4px rgba(59, 130, 246, 0.8)'
                }}
              />
            )}
          </label>
        </div>
      )}
    </>
  )
}
