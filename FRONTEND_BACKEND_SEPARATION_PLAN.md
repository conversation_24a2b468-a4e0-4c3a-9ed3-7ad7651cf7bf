# 🏗️ 冒险岛情报站前后端分离架构实施方案

[![Next.js](https://img.shields.io/badge/Next.js-14+-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-009688?style=flat-square&logo=fastapi)](https://fastapi.tiangolo.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-16-336791?style=flat-square&logo=postgresql)](https://postgresql.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-3178C6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)

> 基于现有 Next.js SSG 项目的前后端分离架构升级方案，实现用户认证、会员系统、虚拟货币等功能

## 📋 目录

- [项目概述](#-项目概述)
- [技术架构设计](#-技术架构设计)
- [项目结构重组](#-项目结构重组)
- [数据库设计](#-数据库设计)
- [API 接口设计](#-api-接口设计)
- [用户认证系统](#-用户认证系统)
- [会员权限系统](#-会员权限系统)
- [虚拟货币系统](#-虚拟货币系统)
- [环境配置](#-环境配置)
- [部署方案](#-部署方案)
- [迁移策略](#-迁移策略)
- [开发指南](#-开发指南)

## 🎯 项目概述

### 现状分析

**当前技术栈**：
- 前端：Next.js 14 + TypeScript + Tailwind CSS + shadcn/ui
- 架构：SSG (静态站点生成)
- 状态管理：React Hooks + Context API
- 数据存储：静态文件 (JSON/JS)
- 部署：静态托管 (Vercel/Netlify)

**项目特点**：
- ✅ 纯前端项目，无后端服务
- ✅ 所有数据通过静态文件提供
- ✅ 客户端渲染和计算
- ✅ 无用户系统和数据库

**核心功能**：
- 装备强化模拟器 ✅
- 星之力强化模拟器 ✅
- 道具数据库 ✅
- CMS-216 版本支持 ✅

### 升级目标

**新增功能需求**：
1. **用户认证系统** - 注册、登录、邮箱验证、密码重置
2. **五级权限模型** - 游客、注册用户、黄金用户、钻石用户、管理员
3. **虚拟货币系统** - "欢乐豆"充值、消费、交易记录
4. **设备指纹识别** - FingerprintJS 游客用户追踪
5. **RESTful API** - 完整的后端服务接口

## 🏗️ 技术架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "用户层"
        A[Web浏览器]
        B[移动设备]
    end
    
    subgraph "前端层 (Next.js SSG)"
        C[静态页面]
        D[客户端组件]
        E[API客户端]
    end
    
    subgraph "API网关层"
        F[CORS处理]
        G[请求路由]
        H[认证中间件]
    end
    
    subgraph "后端服务层 (FastAPI)"
        I[用户认证服务]
        J[会员权限服务]
        K[虚拟货币服务]
        L[装备模拟服务]
    end
    
    subgraph "数据层"
        M[PostgreSQL 16]
        N[Redis缓存]
        O[文件存储]
    end
    
    subgraph "外部服务"
        P[Resend邮件]
        Q[FingerprintJS]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    H --> J
    H --> K
    H --> L
    I --> M
    J --> M
    K --> M
    L --> M
    I --> N
    J --> N
    K --> N
    I --> P
    D --> Q
```

### 技术栈选择

#### 前端技术栈 (保持现有)
- **Next.js 14+**: 保持 SSG 配置，添加客户端功能
- **TypeScript 5**: 严格类型检查
- **Tailwind CSS**: 原子化CSS框架
- **shadcn/ui**: 组件库
- **React Hook Form**: 表单处理
- **Zustand**: 轻量级状态管理 (新增)
- **Axios**: HTTP客户端 (新增)

#### 后端技术栈 (新增)
- **FastAPI**: 现代Python Web框架
- **SQLAlchemy**: ORM框架
- **Alembic**: 数据库迁移工具
- **Pydantic**: 数据验证
- **JWT**: 认证令牌
- **Passlib**: 密码加密
- **Redis**: 缓存和会话存储

#### 数据库和存储
- **PostgreSQL 16**: 主数据库 (端口 5433)
- **Redis**: 缓存和会话存储
- **本地文件系统**: 静态资源存储

#### 外部服务
- **Resend API**: 邮件发送服务
- **FingerprintJS**: 设备指纹识别

## 📁 项目结构重组

### 新的目录结构

```
maplestory-info-station/
├── 📁 frontend/                    # Next.js 前端 (重命名现有内容)
│   ├── 📁 app/                     # Next.js App Router
│   │   ├── 📄 layout.tsx          # 全局布局
│   │   ├── 📄 page.tsx            # 首页
│   │   ├── 📁 auth/               # 认证相关页面
│   │   │   ├── 📁 login/          # 登录页面
│   │   │   ├── 📁 register/       # 注册页面
│   │   │   ├── 📁 reset-password/ # 密码重置
│   │   │   └── 📁 verify-email/   # 邮箱验证
│   │   ├── 📁 dashboard/          # 用户仪表板
│   │   ├── 📁 profile/            # 个人资料
│   │   ├── 📁 transactions/       # 交易记录
│   │   ├── 📁 settings/           # 用户设置
│   │   └── 📁 tools/              # 工具页面 (保持现有)
│   ├── 📁 components/             # React 组件
│   │   ├── 📁 auth/               # 认证组件
│   │   ├── 📁 dashboard/          # 仪表板组件
│   │   ├── 📁 currency/           # 虚拟货币组件
│   │   ├── 📁 ui/                 # 基础UI组件 (保持现有)
│   │   └── 📁 enhancement-simulator/ # 强化模拟器 (保持现有)
│   ├── 📁 lib/                    # 工具函数
│   │   ├── 📄 api-client.ts       # API客户端
│   │   ├── 📄 auth.ts             # 认证工具
│   │   ├── 📄 fingerprint.ts     # 设备指纹
│   │   └── 📄 currency.ts         # 虚拟货币工具
│   ├── 📁 store/                  # 状态管理 (新增)
│   │   ├── 📄 auth-store.ts       # 认证状态
│   │   ├── 📄 user-store.ts       # 用户状态
│   │   └── 📄 currency-store.ts   # 货币状态
│   ├── 📁 types/                  # 类型定义
│   │   ├── 📄 auth.ts             # 认证类型
│   │   ├── 📄 user.ts             # 用户类型
│   │   ├── 📄 currency.ts         # 货币类型
│   │   └── 📄 enhancement.ts      # 强化类型 (保持现有)
│   ├── 📁 middleware/             # 中间件 (新增)
│   │   └── 📄 auth.ts             # 认证中间件
│   ├── 📄 next.config.mjs         # Next.js 配置
│   ├── 📄 package.json            # 前端依赖
│   └── 📄 .env.local              # 前端环境变量
├── 📁 backend/                    # FastAPI 后端 (新增)
│   ├── 📁 app/                    # 应用主目录
│   │   ├── 📄 __init__.py
│   │   ├── 📄 main.py             # FastAPI 应用入口
│   │   ├── 📁 core/               # 核心配置
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 config.py       # 应用配置
│   │   │   ├── 📄 security.py     # 安全配置
│   │   │   ├── 📄 database.py     # 数据库配置
│   │   │   └── 📄 deps.py         # 依赖注入
│   │   ├── 📁 models/             # 数据模型
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 user.py         # 用户模型
│   │   │   ├── 📄 membership.py   # 会员模型
│   │   │   ├── 📄 currency.py     # 货币模型
│   │   │   └── 📄 transaction.py  # 交易模型
│   │   ├── 📁 schemas/            # Pydantic 模式
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 user.py         # 用户模式
│   │   │   ├── 📄 auth.py         # 认证模式
│   │   │   ├── 📄 currency.py     # 货币模式
│   │   │   └── 📄 enhancement.py  # 强化模式
│   │   ├── 📁 api/                # API 路由
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 v1/             # API v1
│   │   │   │   ├── 📄 __init__.py
│   │   │   │   ├── 📄 auth.py     # 认证接口
│   │   │   │   ├── 📄 users.py    # 用户接口
│   │   │   │   ├── 📄 currency.py # 货币接口
│   │   │   │   └── 📄 enhancement.py # 强化接口
│   │   │   └── 📄 router.py       # 路由汇总
│   │   ├── 📁 services/           # 业务逻辑
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 auth_service.py # 认证服务
│   │   │   ├── 📄 user_service.py # 用户服务
│   │   │   ├── 📄 currency_service.py # 货币服务
│   │   │   ├── 📄 email_service.py # 邮件服务
│   │   │   └── 📄 fingerprint_service.py # 指纹服务
│   │   ├── 📁 utils/              # 工具函数
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 security.py     # 安全工具
│   │   │   ├── 📄 email.py        # 邮件工具
│   │   │   └── 📄 validators.py   # 验证工具
│   │   └── 📁 middleware/         # 中间件
│   │       ├── 📄 __init__.py
│   │       ├── 📄 cors.py         # CORS中间件
│   │       ├── 📄 auth.py         # 认证中间件
│   │       └── 📄 rate_limit.py   # 限流中间件
│   ├── 📁 alembic/                # 数据库迁移
│   │   ├── 📄 env.py
│   │   ├── 📄 script.py.mako
│   │   └── 📁 versions/           # 迁移版本
│   ├── 📁 tests/                  # 测试文件
│   │   ├── 📄 __init__.py
│   │   ├── 📄 conftest.py
│   │   ├── 📁 api/
│   │   └── 📁 services/
│   ├── 📄 requirements.txt        # Python 依赖
│   ├── 📄 alembic.ini            # Alembic 配置
│   └── 📄 .env                   # 后端环境变量
├── 📁 shared/                     # 共享资源 (新增)
│   ├── 📁 types/                  # 共享类型定义
│   ├── 📁 constants/              # 共享常量
│   └── 📁 utils/                  # 共享工具函数
├── 📁 docs/                       # 文档 (保持现有)
├── 📁 scripts/                    # 脚本 (保持现有)
├── 📁 deployment/                 # 部署脚本 (新增)
│   ├── 📄 rocky-setup.sh         # Rocky Linux 环境配置
│   ├── 📄 deploy.sh              # 自动化部署脚本
│   └── 📄 nginx.conf             # Nginx 配置文件
├── 📄 .env.example               # 环境变量示例
├── 📄 README.md                  # 项目说明
└── 📄 package.json               # 根目录脚本管理
```

## 🗄️ 数据库设计

### 数据库表结构

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    fingerprint_id VARCHAR(255),  -- FingerprintJS ID
    
    -- 索引
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_fingerprint (fingerprint_id)
);
```

#### 2. 会员等级表 (membership_tiers)
```sql
CREATE TABLE membership_tiers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,  -- guest, registered, gold, diamond, admin
    display_name VARCHAR(100) NOT NULL,  -- 游客, 注册用户, 黄金用户, 钻石用户, 管理员
    level INTEGER NOT NULL,  -- 0, 1, 2, 3, 4
    description TEXT,
    permissions JSON,  -- 权限配置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 用户会员关系表 (user_memberships)
```sql
CREATE TABLE user_memberships (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    tier_id INTEGER REFERENCES membership_tiers(id),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,  -- NULL 表示永久
    is_active BOOLEAN DEFAULT TRUE,

    -- 索引
    INDEX idx_user_membership (user_id, is_active),
    INDEX idx_tier_active (tier_id, is_active)
);
```

#### 4. 虚拟货币账户表 (currency_accounts)
```sql
CREATE TABLE currency_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    balance DECIMAL(15,2) DEFAULT 0.00,  -- 欢乐豆余额
    total_earned DECIMAL(15,2) DEFAULT 0.00,  -- 总获得
    total_spent DECIMAL(15,2) DEFAULT 0.00,   -- 总消费
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    CONSTRAINT positive_balance CHECK (balance >= 0),

    -- 索引
    UNIQUE INDEX idx_user_currency (user_id)
);
```

#### 5. 交易记录表 (transactions)
```sql
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL,  -- earn, spend, refund
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    reference_type VARCHAR(50),  -- enhancement, purchase, etc.
    reference_id VARCHAR(100),   -- 关联的业务ID
    metadata JSON,  -- 额外信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_user_transactions (user_id, created_at DESC),
    INDEX idx_transaction_type (type),
    INDEX idx_reference (reference_type, reference_id)
);
```

#### 6. 邮箱验证表 (email_verifications)
```sql
CREATE TABLE email_verifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    UNIQUE INDEX idx_verification_token (token),
    INDEX idx_user_verification (user_id, verified_at)
);
```

#### 7. 密码重置表 (password_resets)
```sql
CREATE TABLE password_resets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    UNIQUE INDEX idx_reset_token (token),
    INDEX idx_user_reset (user_id, used_at)
);
```

#### 8. 设备指纹表 (device_fingerprints)
```sql
CREATE TABLE device_fingerprints (
    id SERIAL PRIMARY KEY,
    fingerprint_id VARCHAR(255) NOT NULL,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    device_info JSON,  -- 设备信息
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    visit_count INTEGER DEFAULT 1,

    -- 索引
    UNIQUE INDEX idx_fingerprint_id (fingerprint_id),
    INDEX idx_user_fingerprint (user_id)
);
```

#### 9. 用户会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL,
    refresh_token VARCHAR(255),
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,

    -- 索引
    UNIQUE INDEX idx_session_token (session_token),
    UNIQUE INDEX idx_refresh_token (refresh_token),
    INDEX idx_user_sessions (user_id, expires_at)
);
```

### 数据库初始化脚本

#### 初始化会员等级数据
```sql
-- 插入默认会员等级
INSERT INTO membership_tiers (name, display_name, level, description, permissions) VALUES
('guest', '游客', 0, '未注册用户，通过设备指纹识别', '{"enhancement_daily_limit": 10, "features": ["basic_tools"]}'),
('registered', '注册用户', 1, '已注册但未付费用户', '{"enhancement_daily_limit": 50, "features": ["basic_tools", "save_progress"]}'),
('gold', '黄金用户', 2, '付费黄金会员', '{"enhancement_daily_limit": 200, "features": ["basic_tools", "save_progress", "advanced_tools"]}'),
('diamond', '钻石用户', 3, '付费钻石会员', '{"enhancement_daily_limit": 1000, "features": ["basic_tools", "save_progress", "advanced_tools", "premium_features"]}'),
('admin', '管理员', 4, '系统管理员', '{"enhancement_daily_limit": -1, "features": ["all"]}');
```

## 🔌 API 接口设计

### API 架构设计

#### RESTful API 规范
- **基础URL**: `https://api.mxd.dvg.cn/v1`
- **认证方式**: JWT Bearer Token
- **响应格式**: JSON
- **状态码**: 标准 HTTP 状态码
- **错误处理**: 统一错误响应格式

#### 统一响应格式
```typescript
// 成功响应
interface ApiResponse<T> {
  success: true
  data: T
  message?: string
  timestamp: string
}

// 错误响应
interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}
```

### 核心 API 接口

#### 1. 认证接口 (/api/v1/auth)

##### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "string",
  "password": "string",
  "fingerprint_id": "string"  // FingerprintJS ID
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "is_verified": false,
      "membership": {
        "tier": "registered",
        "level": 1
      }
    },
    "tokens": {
      "access_token": "eyJ...",
      "refresh_token": "eyJ...",
      "expires_in": 3600
    }
  }
}
```

##### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "string",
  "password": "string",
  "fingerprint_id": "string"
}

Response: 同注册响应
```

##### 刷新令牌
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "string"
}

Response:
{
  "success": true,
  "data": {
    "access_token": "eyJ...",
    "expires_in": 3600
  }
}
```

##### 邮箱验证
```http
POST /api/v1/auth/verify-email
Content-Type: application/json

{
  "token": "string"
}

Response:
{
  "success": true,
  "data": {
    "message": "邮箱验证成功"
  }
}
```

##### 密码重置请求
```http
POST /api/v1/auth/forgot-password
Content-Type: application/json

{
  "email": "string"
}

Response:
{
  "success": true,
  "data": {
    "message": "密码重置邮件已发送"
  }
}
```

##### 密码重置确认
```http
POST /api/v1/auth/reset-password
Content-Type: application/json

{
  "token": "string",
  "new_password": "string"
}

Response:
{
  "success": true,
  "data": {
    "message": "密码重置成功"
  }
}
```

#### 2. 用户接口 (/api/v1/users)

##### 获取用户信息
```http
GET /api/v1/users/me
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "is_verified": true,
    "membership": {
      "tier": "gold",
      "level": 2,
      "expires_at": "2024-12-31T23:59:59Z"
    },
    "currency": {
      "balance": 1500.00,
      "total_earned": 2000.00,
      "total_spent": 500.00
    },
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

##### 更新用户信息
```http
PUT /api/v1/users/me
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "email": "<EMAIL>"
}

Response:
{
  "success": true,
  "data": {
    "message": "用户信息更新成功"
  }
}
```

#### 3. 虚拟货币接口 (/api/v1/currency)

##### 获取余额
```http
GET /api/v1/currency/balance
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "balance": 1500.00,
    "total_earned": 2000.00,
    "total_spent": 500.00,
    "last_transaction": {
      "id": 123,
      "type": "spend",
      "amount": -10.00,
      "description": "星之力强化模拟",
      "created_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

##### 消费欢乐豆
```http
POST /api/v1/currency/consume
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "amount": 10.00,
  "description": "星之力强化模拟",
  "reference_type": "enhancement",
  "reference_id": "starforce_sim_001"
}

Response:
{
  "success": true,
  "data": {
    "transaction_id": 124,
    "balance_before": 1500.00,
    "balance_after": 1490.00,
    "amount": -10.00
  }
}
```

##### 获取交易记录
```http
GET /api/v1/currency/transactions?page=1&limit=20&type=spend
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": 124,
        "type": "spend",
        "amount": -10.00,
        "balance_before": 1500.00,
        "balance_after": 1490.00,
        "description": "星之力强化模拟",
        "reference_type": "enhancement",
        "reference_id": "starforce_sim_001",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

#### 4. 装备强化接口 (/api/v1/enhancement)

##### 星之力强化
```http
POST /api/v1/enhancement/starforce
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "equipment_id": "1001",
  "current_level": 10,
  "target_level": 11,
  "options": {
    "starcatch_enabled": true,
    "prevent_enabled": false,
    "mvp_discount": true,
    "event_discount": false
  }
}

Response:
{
  "success": true,
  "data": {
    "result": "success",
    "new_level": 11,
    "cost": 15.00,
    "currency_consumed": 10.00,
    "probability": {
      "success": 30.0,
      "failure": 67.7,
      "major_failure": 2.3
    },
    "transaction_id": 125
  }
}
```

##### 潜能强化
```http
POST /api/v1/enhancement/potential
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "equipment_id": "1001",
  "cube_type": "red_cube",
  "options": {
    "mvp_discount": true
  }
}

Response:
{
  "success": true,
  "data": {
    "result": "success",
    "new_potential": {
      "line1": "STR +12%",
      "line2": "攻击力 +9%",
      "line3": "全属性 +6%"
    },
    "cost": 25.00,
    "currency_consumed": 20.00,
    "transaction_id": 126
  }
}
```

#### 5. 管理员接口 (/api/v1/admin)

##### 用户管理
```http
GET /api/v1/admin/users?page=1&limit=20&search=username
Authorization: Bearer {admin_token}

Response:
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "membership_tier": "gold",
        "currency_balance": 1500.00,
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

### API 错误处理

#### 错误代码规范
```typescript
enum ApiErrorCode {
  // 认证错误 (1000-1099)
  INVALID_CREDENTIALS = 'AUTH_1001',
  TOKEN_EXPIRED = 'AUTH_1002',
  TOKEN_INVALID = 'AUTH_1003',
  EMAIL_NOT_VERIFIED = 'AUTH_1004',

  // 用户错误 (1100-1199)
  USER_NOT_FOUND = 'USER_1101',
  USERNAME_EXISTS = 'USER_1102',
  EMAIL_EXISTS = 'USER_1103',

  // 货币错误 (1200-1299)
  INSUFFICIENT_BALANCE = 'CURRENCY_1201',
  INVALID_AMOUNT = 'CURRENCY_1202',
  TRANSACTION_FAILED = 'CURRENCY_1203',

  // 权限错误 (1300-1399)
  PERMISSION_DENIED = 'PERMISSION_1301',
  MEMBERSHIP_REQUIRED = 'PERMISSION_1302',
  DAILY_LIMIT_EXCEEDED = 'PERMISSION_1303',

  // 系统错误 (9000-9999)
  INTERNAL_ERROR = 'SYSTEM_9001',
  DATABASE_ERROR = 'SYSTEM_9002',
  EXTERNAL_SERVICE_ERROR = 'SYSTEM_9003'
}
```

#### 错误响应示例
```json
{
  "success": false,
  "error": {
    "code": "CURRENCY_1201",
    "message": "欢乐豆余额不足",
    "details": {
      "required": 50.00,
      "available": 30.00
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔐 用户认证系统

### JWT 认证架构

#### Token 设计
```typescript
// Access Token Payload
interface AccessTokenPayload {
  sub: string          // 用户ID
  username: string     // 用户名
  membership_level: number  // 会员等级
  permissions: string[]     // 权限列表
  iat: number          // 签发时间
  exp: number          // 过期时间 (15分钟)
  type: 'access'       // Token类型
}

// Refresh Token Payload
interface RefreshTokenPayload {
  sub: string          // 用户ID
  session_id: string   // 会话ID
  iat: number          // 签发时间
  exp: number          // 过期时间 (7天)
  type: 'refresh'      // Token类型
}
```

#### 认证流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant F as 前端
    participant A as API网关
    participant B as 后端服务
    participant D as 数据库
    participant R as Redis

    C->>F: 用户登录
    F->>A: POST /auth/login
    A->>B: 验证用户凭据
    B->>D: 查询用户信息
    D-->>B: 返回用户数据
    B->>R: 存储会话信息
    B-->>A: 返回JWT Token
    A-->>F: 返回认证结果
    F->>F: 存储Token到localStorage
    F-->>C: 登录成功

    Note over F,R: 后续请求携带Token
    C->>F: 访问受保护资源
    F->>A: 请求 + Authorization Header
    A->>A: 验证JWT Token
    A->>B: 转发请求
    B-->>A: 返回数据
    A-->>F: 返回响应
    F-->>C: 显示数据
```

### 前端认证实现

#### 认证状态管理 (Zustand)
```typescript
// store/auth-store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: number
  username: string
  email?: string
  membership: {
    tier: string
    level: number
    expires_at?: string
  }
  currency: {
    balance: number
    total_earned: number
    total_spent: number
  }
}

interface AuthState {
  user: User | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  refreshAuth: () => Promise<void>
  updateUser: (user: Partial<User>) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials) => {
        set({ isLoading: true })
        try {
          const response = await apiClient.post('/auth/login', credentials)
          const { user, tokens } = response.data

          set({
            user,
            accessToken: tokens.access_token,
            refreshToken: tokens.refresh_token,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false
        })
      },

      refreshAuth: async () => {
        const { refreshToken } = get()
        if (!refreshToken) throw new Error('No refresh token')

        try {
          const response = await apiClient.post('/auth/refresh', {
            refresh_token: refreshToken
          })

          set({
            accessToken: response.data.access_token
          })
        } catch (error) {
          get().logout()
          throw error
        }
      },

      updateUser: (userData) => {
        set(state => ({
          user: state.user ? { ...state.user, ...userData } : null
        }))
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
```

#### API 客户端配置
```typescript
// lib/api-client.ts
import axios from 'axios'
import { useAuthStore } from '@/store/auth-store'

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证头
apiClient.interceptors.request.use(
  (config) => {
    const { accessToken } = useAuthStore.getState()
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器 - 处理Token过期
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        await useAuthStore.getState().refreshAuth()
        const { accessToken } = useAuthStore.getState()
        originalRequest.headers.Authorization = `Bearer ${accessToken}`
        return apiClient(originalRequest)
      } catch (refreshError) {
        useAuthStore.getState().logout()
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)

export default apiClient
```

#### 认证中间件
```typescript
// middleware/auth.ts
import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify } from 'jose'

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key'
)

export async function authMiddleware(request: NextRequest) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '')

  if (!token) {
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }

  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)

    // 将用户信息添加到请求头
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user-id', payload.sub as string)
    requestHeaders.set('x-user-level', payload.membership_level as string)

    return NextResponse.next({
      request: {
        headers: requestHeaders
      }
    })
  } catch (error) {
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }
}

// 受保护的路由
export const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/transactions',
  '/settings'
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    return authMiddleware(request)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/profile/:path*',
    '/transactions/:path*',
    '/settings/:path*'
  ]
}
```

### 后端认证实现

#### JWT 工具类
```python
# app/core/security.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import jwt
from passlib.context import CryptContext
from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_access_token(
    subject: str,
    user_data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    payload = {
        "sub": subject,
        "username": user_data.get("username"),
        "membership_level": user_data.get("membership_level", 0),
        "permissions": user_data.get("permissions", []),
        "iat": datetime.utcnow(),
        "exp": expire,
        "type": "access"
    }

    return jwt.encode(payload, settings.JWT_SECRET, algorithm="HS256")

def create_refresh_token(
    subject: str,
    session_id: str,
    expires_delta: Optional[timedelta] = None
) -> str:
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)

    payload = {
        "sub": subject,
        "session_id": session_id,
        "iat": datetime.utcnow(),
        "exp": expire,
        "type": "refresh"
    }

    return jwt.encode(payload, settings.JWT_SECRET, algorithm="HS256")

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    try:
        payload = jwt.decode(token, settings.JWT_SECRET, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)
```

## 👑 会员权限系统

### 五级权限模型

#### 权限等级定义
```python
# app/models/membership.py
from enum import Enum
from typing import Dict, List, Any

class MembershipTier(Enum):
    GUEST = ("guest", 0, "游客")
    REGISTERED = ("registered", 1, "注册用户")
    GOLD = ("gold", 2, "黄金用户")
    DIAMOND = ("diamond", 3, "钻石用户")
    ADMIN = ("admin", 4, "管理员")

    def __init__(self, name: str, level: int, display_name: str):
        self.tier_name = name
        self.level = level
        self.display_name = display_name

# 权限配置
MEMBERSHIP_PERMISSIONS = {
    MembershipTier.GUEST: {
        "enhancement_daily_limit": 10,
        "features": ["basic_tools"],
        "currency_earning": False,
        "save_progress": False,
        "advanced_tools": False,
        "premium_features": False
    },
    MembershipTier.REGISTERED: {
        "enhancement_daily_limit": 50,
        "features": ["basic_tools", "save_progress"],
        "currency_earning": True,
        "save_progress": True,
        "advanced_tools": False,
        "premium_features": False
    },
    MembershipTier.GOLD: {
        "enhancement_daily_limit": 200,
        "features": ["basic_tools", "save_progress", "advanced_tools"],
        "currency_earning": True,
        "save_progress": True,
        "advanced_tools": True,
        "premium_features": False
    },
    MembershipTier.DIAMOND: {
        "enhancement_daily_limit": 1000,
        "features": ["basic_tools", "save_progress", "advanced_tools", "premium_features"],
        "currency_earning": True,
        "save_progress": True,
        "advanced_tools": True,
        "premium_features": True
    },
    MembershipTier.ADMIN: {
        "enhancement_daily_limit": -1,  # 无限制
        "features": ["all"],
        "currency_earning": True,
        "save_progress": True,
        "advanced_tools": True,
        "premium_features": True,
        "admin_access": True
    }
}
```

#### 权限检查装饰器
```python
# app/utils/permissions.py
from functools import wraps
from typing import List, Optional
from fastapi import HTTPException, Depends
from app.models.membership import MembershipTier, MEMBERSHIP_PERMISSIONS
from app.core.deps import get_current_user

def require_membership(
    min_level: int = 0,
    features: Optional[List[str]] = None
):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(status_code=401, detail="未认证")

            user_level = current_user.membership.level
            if user_level < min_level:
                raise HTTPException(
                    status_code=403,
                    detail=f"需要 {MembershipTier(min_level).display_name} 或更高等级"
                )

            if features:
                user_permissions = MEMBERSHIP_PERMISSIONS.get(
                    MembershipTier(user_level)
                )
                user_features = user_permissions.get("features", [])

                if "all" not in user_features:
                    for feature in features:
                        if feature not in user_features:
                            raise HTTPException(
                                status_code=403,
                                detail=f"缺少功能权限: {feature}"
                            )

            return await func(*args, **kwargs)
        return wrapper
    return decorator

def check_daily_limit(user_id: int, action_type: str) -> bool:
    """检查用户每日操作限制"""
    # 从Redis获取今日操作次数
    # 实现逻辑...
    pass

def consume_daily_quota(user_id: int, action_type: str, count: int = 1):
    """消费每日配额"""
    # 更新Redis中的操作次数
    # 实现逻辑...
    pass
```

#### 前端权限组件
```typescript
// components/auth/PermissionGuard.tsx
import { useAuthStore } from '@/store/auth-store'
import { ReactNode } from 'react'

interface PermissionGuardProps {
  children: ReactNode
  minLevel?: number
  features?: string[]
  fallback?: ReactNode
}

export function PermissionGuard({
  children,
  minLevel = 0,
  features = [],
  fallback = <div>权限不足</div>
}: PermissionGuardProps) {
  const { user, isAuthenticated } = useAuthStore()

  if (!isAuthenticated || !user) {
    return <div>请先登录</div>
  }

  if (user.membership.level < minLevel) {
    return fallback
  }

  // 检查功能权限
  if (features.length > 0) {
    const userFeatures = getMembershipFeatures(user.membership.tier)
    const hasAllFeatures = features.every(feature =>
      userFeatures.includes(feature) || userFeatures.includes('all')
    )

    if (!hasAllFeatures) {
      return fallback
    }
  }

  return <>{children}</>
}

// 使用示例
function AdvancedToolsSection() {
  return (
    <PermissionGuard
      minLevel={2}
      features={['advanced_tools']}
      fallback={
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p>此功能需要黄金会员或更高等级</p>
          <Button>升级会员</Button>
        </div>
      }
    >
      <AdvancedEnhancementTools />
    </PermissionGuard>
  )
}
```

### 设备指纹识别

#### FingerprintJS 集成
```typescript
// lib/fingerprint.ts
import FingerprintJS from '@fingerprintjs/fingerprintjs'

class FingerprintService {
  private fp: any = null
  private visitorId: string | null = null

  async initialize() {
    if (!this.fp) {
      this.fp = await FingerprintJS.load()
    }
  }

  async getVisitorId(): Promise<string> {
    if (!this.visitorId) {
      await this.initialize()
      const result = await this.fp.get()
      this.visitorId = result.visitorId
    }
    return this.visitorId
  }

  async getDeviceInfo() {
    await this.initialize()
    const result = await this.fp.get()

    return {
      visitorId: result.visitorId,
      confidence: result.confidence,
      components: {
        userAgent: result.components.userAgent?.value,
        language: result.components.language?.value,
        screen: result.components.screenResolution?.value,
        timezone: result.components.timezone?.value,
        platform: result.components.platform?.value
      }
    }
  }
}

export const fingerprintService = new FingerprintService()

// 在应用启动时初始化
export async function initializeFingerprint() {
  try {
    await fingerprintService.initialize()
    const visitorId = await fingerprintService.getVisitorId()

    // 存储到本地存储
    localStorage.setItem('device_fingerprint', visitorId)

    return visitorId
  } catch (error) {
    console.error('Failed to initialize fingerprint:', error)
    return null
  }
}
```

## 💰 虚拟货币系统

### 欢乐豆系统设计

#### 货币服务类
```python
# app/services/currency_service.py
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from app.models.currency import CurrencyAccount, Transaction
from app.models.user import User
from app.core.database import get_db

class CurrencyService:
    def __init__(self, db: Session):
        self.db = db

    async def get_balance(self, user_id: int) -> Optional[CurrencyAccount]:
        """获取用户余额"""
        return self.db.query(CurrencyAccount).filter(
            CurrencyAccount.user_id == user_id
        ).first()

    async def create_account(self, user_id: int) -> CurrencyAccount:
        """创建货币账户"""
        account = CurrencyAccount(
            user_id=user_id,
            balance=Decimal('0.00'),
            total_earned=Decimal('0.00'),
            total_spent=Decimal('0.00')
        )
        self.db.add(account)
        self.db.commit()
        self.db.refresh(account)
        return account

    async def consume_currency(
        self,
        user_id: int,
        amount: Decimal,
        description: str,
        reference_type: str = None,
        reference_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """消费欢乐豆"""
        account = await self.get_balance(user_id)
        if not account:
            account = await self.create_account(user_id)

        if account.balance < amount:
            raise ValueError("余额不足")

        # 开始事务
        balance_before = account.balance
        balance_after = balance_before - amount

        # 更新账户余额
        account.balance = balance_after
        account.total_spent += amount

        # 创建交易记录
        transaction = Transaction(
            user_id=user_id,
            type='spend',
            amount=-amount,
            balance_before=balance_before,
            balance_after=balance_after,
            description=description,
            reference_type=reference_type,
            reference_id=reference_id,
            metadata=metadata
        )

        self.db.add(transaction)
        self.db.commit()
        self.db.refresh(transaction)

        return {
            'transaction_id': transaction.id,
            'balance_before': float(balance_before),
            'balance_after': float(balance_after),
            'amount': float(-amount)
        }

    async def add_currency(
        self,
        user_id: int,
        amount: Decimal,
        description: str,
        reference_type: str = None,
        reference_id: str = None
    ) -> Dict[str, Any]:
        """增加欢乐豆"""
        account = await self.get_balance(user_id)
        if not account:
            account = await self.create_account(user_id)

        balance_before = account.balance
        balance_after = balance_before + amount

        # 更新账户余额
        account.balance = balance_after
        account.total_earned += amount

        # 创建交易记录
        transaction = Transaction(
            user_id=user_id,
            type='earn',
            amount=amount,
            balance_before=balance_before,
            balance_after=balance_after,
            description=description,
            reference_type=reference_type,
            reference_id=reference_id
        )

        self.db.add(transaction)
        self.db.commit()
        self.db.refresh(transaction)

        return {
            'transaction_id': transaction.id,
            'balance_before': float(balance_before),
            'balance_after': float(balance_after),
            'amount': float(amount)
        }

    async def get_transactions(
        self,
        user_id: int,
        page: int = 1,
        limit: int = 20,
        transaction_type: str = None
    ) -> Dict[str, Any]:
        """获取交易记录"""
        query = self.db.query(Transaction).filter(
            Transaction.user_id == user_id
        )

        if transaction_type:
            query = query.filter(Transaction.type == transaction_type)

        total = query.count()
        transactions = query.order_by(
            Transaction.created_at.desc()
        ).offset((page - 1) * limit).limit(limit).all()

        return {
            'transactions': transactions,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'total_pages': (total + limit - 1) // limit
            }
        }
```

#### 前端货币组件
```typescript
// components/currency/CurrencyBalance.tsx
import { useEffect, useState } from 'react'
import { useCurrencyStore } from '@/store/currency-store'
import { Coins, TrendingUp, TrendingDown } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function CurrencyBalance() {
  const { balance, totalEarned, totalSpent, fetchBalance, isLoading } = useCurrencyStore()

  useEffect(() => {
    fetchBalance()
  }, [fetchBalance])

  if (isLoading) {
    return <div>加载中...</div>
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">当前余额</CardTitle>
          <Coins className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">
            {balance.toFixed(2)} 欢乐豆
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总获得</CardTitle>
          <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {totalEarned.toFixed(2)}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总消费</CardTitle>
          <TrendingDown className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {totalSpent.toFixed(2)}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// store/currency-store.ts
import { create } from 'zustand'
import apiClient from '@/lib/api-client'

interface CurrencyState {
  balance: number
  totalEarned: number
  totalSpent: number
  isLoading: boolean

  fetchBalance: () => Promise<void>
  consumeCurrency: (amount: number, description: string) => Promise<void>
}

export const useCurrencyStore = create<CurrencyState>((set, get) => ({
  balance: 0,
  totalEarned: 0,
  totalSpent: 0,
  isLoading: false,

  fetchBalance: async () => {
    set({ isLoading: true })
    try {
      const response = await apiClient.get('/currency/balance')
      const { balance, total_earned, total_spent } = response.data.data

      set({
        balance,
        totalEarned: total_earned,
        totalSpent: total_spent,
        isLoading: false
      })
    } catch (error) {
      set({ isLoading: false })
      throw error
    }
  },

  consumeCurrency: async (amount: number, description: string) => {
    try {
      await apiClient.post('/currency/consume', {
        amount,
        description
      })

      // 重新获取余额
      await get().fetchBalance()
    } catch (error) {
      throw error
    }
  }
}))
```

## ⚙️ 环境配置

### 环境变量配置

#### 前端环境变量 (.env.local)
```bash
# API 配置
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws

# 认证配置
NEXT_PUBLIC_JWT_SECRET=your-jwt-secret-key
NEXT_PUBLIC_TOKEN_EXPIRE_TIME=900  # 15分钟

# FingerprintJS 配置
NEXT_PUBLIC_FINGERPRINT_API_KEY=your-fingerprintjs-api-key

# 应用配置
NEXT_PUBLIC_APP_NAME=冒险岛情报站
NEXT_PUBLIC_APP_VERSION=2.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# 功能开关
NEXT_PUBLIC_ENABLE_REGISTRATION=true
NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION=true
NEXT_PUBLIC_ENABLE_CURRENCY_SYSTEM=true

# 分析和监控
NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/xxx
```

#### 后端环境变量 (.env)
```bash
# 应用配置
APP_NAME=MapleStory Info Station API
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/mxd_info_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis 配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# 邮件服务配置 (Resend)
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx
FROM_EMAIL=<EMAIL>
FROM_NAME=冒险岛情报站

# CORS 配置
ALLOWED_ORIGINS=http://localhost:3000,https://mxd.dvg.cn
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# 安全配置
SECRET_KEY=your-super-secret-key-for-encryption
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# 限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# 监控配置
SENTRY_DSN=https://<EMAIL>/xxx
LOG_LEVEL=INFO
LOG_FORMAT=json

# 外部服务配置
FINGERPRINT_WEBHOOK_SECRET=your-fingerprint-webhook-secret
```

#### 环境变量示例文件 (.env.example)
```bash
# 复制此文件为 .env 并填入实际值

# ===================
# 应用基础配置
# ===================
APP_NAME=MapleStory Info Station API
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production

# ===================
# 数据库配置
# ===================
DATABASE_URL=postgresql://username:password@localhost:5433/mxd_info_db
REDIS_URL=redis://localhost:6379/0

# ===================
# 认证配置
# ===================
JWT_SECRET=change-this-to-a-random-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=7

# ===================
# 邮件服务配置
# ===================
RESEND_API_KEY=your-resend-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Your App Name

# ===================
# 前端配置
# ===================
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
NEXT_PUBLIC_FINGERPRINT_API_KEY=your-fingerprintjs-api-key

# ===================
# 安全配置
# ===================
SECRET_KEY=another-random-secret-key
ALLOWED_ORIGINS=https://yourdomain.com
```

### 系统服务配置

#### PostgreSQL 配置
```bash
# /var/lib/pgsql/16/data/postgresql.conf
listen_addresses = 'localhost'
port = 5433
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 日志配置
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'all'
log_min_duration_statement = 1000
```

#### Redis 配置
```bash
# /etc/redis/redis.conf
bind 127.0.0.1
port 6379
timeout 300
tcp-keepalive 300
daemonize yes
supervised systemd
pidfile /var/run/redis_6379.pid
loglevel notice
logfile /var/log/redis/redis-server.log
databases 16
save 900 1
save 300 10
save 60 10000
maxmemory 256mb
maxmemory-policy allkeys-lru
```

#### Nginx 配置
```nginx
# /etc/nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 前端静态文件
    server {
        listen 80;
        server_name mxd.dvg.cn;
        root /usr/share/nginx/html/mxd;
        index index.html;

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # SPA 路由支持
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API 代理
        location /api/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

#### 系统服务配置
```ini
# /etc/systemd/system/mxd-backend.service
[Unit]
Description=MapleStory Info Station Backend
After=network.target postgresql-16.service redis.service

[Service]
Type=exec
User=mxd
Group=mxd
WorkingDirectory=/app/maplestory-info-station/backend
Environment=PATH=/usr/bin:/usr/local/bin
Environment=DATABASE_URL=postgresql://postgres:postgres@localhost:5433/mxd_info_db
Environment=REDIS_URL=redis://localhost:6379/0
ExecStart=/usr/bin/python3.11 -m gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 🚀 部署方案

### 部署架构

#### 生产环境架构图
```mermaid
graph TB
    subgraph "CDN/负载均衡"
        A[Cloudflare/阿里云CDN]
    end

    subgraph "Web服务器"
        B[Nginx反向代理]
    end

    subgraph "应用服务器"
        C[Next.js静态文件]
        D[FastAPI应用]
        E[Gunicorn/Uvicorn]
    end

    subgraph "数据库服务器"
        F[PostgreSQL 16]
        G[Redis缓存]
    end

    subgraph "外部服务"
        H[Resend邮件服务]
        I[FingerprintJS]
    end

    A --> B
    B --> C
    B --> D
    D --> E
    E --> F
    E --> G
    D --> H
    C --> I
```

#### 部署选项对比

| 部署方式 | 优势 | 劣势 | 适用场景 |
|---------|------|------|----------|
| **单服务器部署** | 成本低、配置简单 | 性能有限、单点故障 | 开发测试、小规模应用 |
| **容器化部署** | 环境一致、易扩展 | 需要容器知识 | 中等规模、团队开发 |
| **云服务部署** | 高可用、自动扩展 | 成本较高 | 生产环境、大规模应用 |
| **混合部署** | 灵活性高、成本可控 | 管理复杂 | 特定需求场景 |

### 推荐部署方案

#### 方案一：单服务器部署 (适合初期)
```bash
# 服务器要求
- CPU: 4核心
- 内存: 8GB
- 存储: 100GB SSD
- 带宽: 10Mbps
- 操作系统: Rocky Linux 9.5

# 部署步骤
1. 安装系统依赖 (Python 3.11+, Node.js 18+, PostgreSQL 16, Redis, Nginx)
2. 克隆项目代码
3. 配置环境变量
4. 构建前端静态文件
5. 启动后端服务 (Gunicorn + Uvicorn)
6. 配置 Nginx 反向代理
7. 设置 SSL 证书
8. 配置域名解析
9. 设置系统服务和自启动
```

#### 方案二：云服务部署 (推荐生产环境)
```yaml
# 阿里云/腾讯云部署配置
前端部署:
  服务: 对象存储 OSS + CDN
  配置:
    - 静态网站托管
    - 全球CDN加速
    - HTTPS证书自动续期

后端部署:
  服务: 云服务器 ECS
  配置:
    - Rocky Linux 9.5
    - 2个实例 (高可用)
    - 负载均衡 SLB
    - 自动备份和监控

数据库:
  服务: RDS PostgreSQL 16
  配置:
    - 主从复制
    - 自动备份
    - 监控告警

缓存:
  服务: Redis云数据库
  配置:
    - 高可用版
    - 数据持久化
```

### 部署脚本

#### Rocky Linux 9.5 系统环境准备
```bash
#!/bin/bash
# setup-rocky-environment.sh - Rocky Linux 9.5 环境准备脚本

set -e

echo "开始配置 Rocky Linux 9.5 生产环境..."

# 1. 更新系统
sudo dnf update -y

# 2. 安装基础工具
sudo dnf install -y wget curl git vim htop

# 3. 安装 Node.js 18+
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install -y nodejs

# 4. 安装 Python 3.11+
sudo dnf install -y python3.11 python3.11-pip python3.11-devel

# 5. 安装 PostgreSQL 16
sudo dnf install -y https://download.postgresql.org/pub/repos/yum/repopackages/EL-9-x86_64/pgdg-redhat-repo-latest.noarch.rpm
sudo dnf install -y postgresql16-server postgresql16

# 初始化数据库
sudo /usr/pgsql-16/bin/postgresql-16-setup initdb
sudo systemctl enable postgresql-16
sudo systemctl start postgresql-16

# 6. 安装 Redis
sudo dnf install -y redis
sudo systemctl enable redis
sudo systemctl start redis

# 7. 安装 Nginx
sudo dnf install -y nginx
sudo systemctl enable nginx

echo "Rocky Linux 9.5 环境配置完成！"
```

#### 自动化部署脚本
```bash
#!/bin/bash
# deploy.sh - 自动化部署脚本 (Rocky Linux 9.5)

set -e

# 配置变量
PROJECT_NAME="maplestory-info-station"
DEPLOY_ENV=${1:-production}
BACKUP_DIR="/backup"
LOG_FILE="/var/log/deploy.log"
APP_DIR="/app/$PROJECT_NAME"
FRONTEND_BUILD_DIR="$APP_DIR/frontend/out"
NGINX_ROOT="/usr/share/nginx/html"

echo "开始部署 $PROJECT_NAME ($DEPLOY_ENV 环境)" | tee -a $LOG_FILE

# 1. 备份当前版本
echo "备份当前版本..." | tee -a $LOG_FILE
if [ -d "$APP_DIR" ]; then
    sudo cp -r "$APP_DIR" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
fi

# 2. 拉取最新代码
echo "拉取最新代码..." | tee -a $LOG_FILE
cd "$APP_DIR"
git pull origin main

# 3. 构建前端
echo "构建前端..." | tee -a $LOG_FILE
cd frontend
npm ci
npm run build

# 4. 部署前端静态文件
echo "部署前端静态文件..." | tee -a $LOG_FILE
sudo rm -rf "$NGINX_ROOT/mxd"
sudo cp -r "$FRONTEND_BUILD_DIR" "$NGINX_ROOT/mxd"
sudo chown -R nginx:nginx "$NGINX_ROOT/mxd"

# 5. 安装后端依赖
echo "安装后端依赖..." | tee -a $LOG_FILE
cd ../backend
python3.11 -m pip install -r requirements.txt

# 6. 数据库迁移
echo "执行数据库迁移..." | tee -a $LOG_FILE
alembic upgrade head

# 7. 重启后端服务
echo "重启后端服务..." | tee -a $LOG_FILE
sudo systemctl restart mxd-backend
sudo systemctl restart nginx

# 8. 健康检查
echo "执行健康检查..." | tee -a $LOG_FILE
sleep 10
curl -f http://localhost:8000/health || {
    echo "健康检查失败，回滚部署" | tee -a $LOG_FILE
    # 回滚逻辑
    exit 1
}

echo "部署完成！" | tee -a $LOG_FILE
```

#### Rocky Linux 9.5 生产环境完整部署指南

##### 1. 系统初始化
```bash
#!/bin/bash
# rocky-production-setup.sh - Rocky Linux 9.5 生产环境完整配置

set -e

echo "开始配置 Rocky Linux 9.5 生产环境..."

# 更新系统
sudo dnf update -y

# 配置防火墙
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload

# 创建应用用户
sudo useradd -m -s /bin/bash mxd
sudo usermod -aG wheel mxd

# 创建应用目录
sudo mkdir -p /app/maplestory-info-station
sudo mkdir -p /backup
sudo mkdir -p /var/log/mxd
sudo chown -R mxd:mxd /app/maplestory-info-station
sudo chown -R mxd:mxd /var/log/mxd

echo "系统初始化完成"
```

##### 2. 数据库配置
```bash
#!/bin/bash
# setup-database.sh - 配置 PostgreSQL 16

# 初始化数据库
sudo /usr/pgsql-16/bin/postgresql-16-setup initdb

# 配置 PostgreSQL
sudo tee /var/lib/pgsql/16/data/pg_hba.conf > /dev/null <<EOF
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                peer
local   all             all                                     peer
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
EOF

# 修改端口配置
sudo sed -i "s/#port = 5432/port = 5433/" /var/lib/pgsql/16/data/postgresql.conf
sudo sed -i "s/#listen_addresses = 'localhost'/listen_addresses = 'localhost'/" /var/lib/pgsql/16/data/postgresql.conf

# 启动并设置自启动
sudo systemctl enable postgresql-16
sudo systemctl start postgresql-16

# 创建数据库和用户
sudo -u postgres psql -p 5433 -c "CREATE DATABASE mxd_info_db;"
sudo -u postgres psql -p 5433 -c "CREATE USER mxd_user WITH PASSWORD 'secure_password_here';"
sudo -u postgres psql -p 5433 -c "GRANT ALL PRIVILEGES ON DATABASE mxd_info_db TO mxd_user;"

echo "数据库配置完成"
```

##### 3. 应用部署
```bash
#!/bin/bash
# deploy-application.sh - 部署应用

APP_DIR="/app/maplestory-info-station"
REPO_URL="https://github.com/your-org/maplestory-info-station.git"

# 克隆代码
sudo -u mxd git clone $REPO_URL $APP_DIR
cd $APP_DIR

# 安装前端依赖并构建
cd frontend
sudo -u mxd npm ci
sudo -u mxd npm run build

# 部署前端文件
sudo cp -r out/* /usr/share/nginx/html/
sudo chown -R nginx:nginx /usr/share/nginx/html/

# 安装后端依赖
cd ../backend
sudo -u mxd python3.11 -m pip install --user -r requirements.txt

# 配置环境变量
sudo -u mxd cp .env.example .env
# 手动编辑 .env 文件

# 初始化数据库
sudo -u mxd python3.11 -m alembic upgrade head
sudo -u mxd python3.11 scripts/init_data.py

echo "应用部署完成"
```

##### 4. 服务配置和启动
```bash
#!/bin/bash
# setup-services.sh - 配置系统服务

# 安装 Gunicorn
sudo -u mxd python3.11 -m pip install --user gunicorn uvicorn[standard]

# 创建系统服务文件
sudo tee /etc/systemd/system/mxd-backend.service > /dev/null <<EOF
[Unit]
Description=MapleStory Info Station Backend
After=network.target postgresql-16.service redis.service

[Service]
Type=exec
User=mxd
Group=mxd
WorkingDirectory=/app/maplestory-info-station/backend
Environment=PATH=/home/<USER>/.local/bin:/usr/bin:/usr/local/bin
EnvironmentFile=/app/maplestory-info-station/backend/.env
ExecStart=/home/<USER>/.local/bin/gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd 并启动服务
sudo systemctl daemon-reload
sudo systemctl enable mxd-backend
sudo systemctl start mxd-backend

# 启动 Nginx
sudo systemctl enable nginx
sudo systemctl start nginx

# 检查服务状态
sudo systemctl status mxd-backend
sudo systemctl status nginx

echo "服务配置完成"
```

##### 5. SSL 证书配置
```bash
#!/bin/bash
# setup-ssl.sh - 配置 SSL 证书

# 安装 Certbot
sudo dnf install -y certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d mxd.dvg.cn

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -

echo "SSL 证书配置完成"
```

##### 6. 监控和日志配置
```bash
#!/bin/bash
# setup-monitoring.sh - 配置监控和日志

# 配置日志轮转
sudo tee /etc/logrotate.d/mxd > /dev/null <<EOF
/var/log/mxd/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 mxd mxd
    postrotate
        systemctl reload mxd-backend
    endscript
}
EOF

# 配置系统监控脚本
sudo tee /usr/local/bin/mxd-health-check.sh > /dev/null <<'EOF'
#!/bin/bash
# 健康检查脚本

LOG_FILE="/var/log/mxd/health-check.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查后端服务
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "[$DATE] Backend service: OK" >> $LOG_FILE
else
    echo "[$DATE] Backend service: FAILED" >> $LOG_FILE
    # 发送告警邮件或短信
fi

# 检查数据库连接
if sudo -u postgres psql -p 5433 -d mxd_info_db -c "SELECT 1;" > /dev/null 2>&1; then
    echo "[$DATE] Database: OK" >> $LOG_FILE
else
    echo "[$DATE] Database: FAILED" >> $LOG_FILE
fi

# 检查 Redis
if redis-cli ping > /dev/null 2>&1; then
    echo "[$DATE] Redis: OK" >> $LOG_FILE
else
    echo "[$DATE] Redis: FAILED" >> $LOG_FILE
fi
EOF

sudo chmod +x /usr/local/bin/mxd-health-check.sh

# 添加定时任务
echo "*/5 * * * * /usr/local/bin/mxd-health-check.sh" | sudo crontab -

echo "监控配置完成"
```

## 📋 迁移策略

### 渐进式迁移方案

#### 阶段一：基础架构搭建 (1-2周)
```markdown
目标：建立前后端分离的基础架构

任务清单：
- [ ] 创建新的项目目录结构
- [ ] 搭建 FastAPI 后端框架
- [ ] 配置 PostgreSQL 数据库
- [ ] 设置 Redis 缓存
- [ ] 实现基础的 CORS 配置
- [ ] 创建 Docker 开发环境
- [ ] 编写基础的 API 文档

验收标准：
- 后端服务能正常启动
- 数据库连接正常
- 前端能成功调用后端 API
- 开发环境容器化完成
```

#### 阶段二：用户认证系统 (2-3周)
```markdown
目标：实现完整的用户认证和权限系统

任务清单：
- [ ] 实现用户注册/登录 API
- [ ] 集成 JWT 认证机制
- [ ] 开发邮箱验证功能
- [ ] 实现密码重置功能
- [ ] 集成 FingerprintJS
- [ ] 创建认证相关页面
- [ ] 实现权限中间件
- [ ] 开发会员等级系统

验收标准：
- 用户能正常注册和登录
- 邮箱验证流程正常
- JWT Token 自动刷新
- 权限控制正确生效
- 设备指纹识别正常
```

#### 阶段三：虚拟货币系统 (2周)
```markdown
目标：实现欢乐豆虚拟货币系统

任务清单：
- [ ] 设计货币数据模型
- [ ] 实现余额管理 API
- [ ] 开发交易记录功能
- [ ] 实现消费扣除逻辑
- [ ] 创建货币管理界面
- [ ] 实现交易安全机制
- [ ] 添加防刷币检测

验收标准：
- 货币余额显示正确
- 消费扣除功能正常
- 交易记录完整准确
- 安全机制有效防护
```

#### 阶段四：现有功能集成 (2-3周)
```markdown
目标：将现有的前端功能集成到新架构中

任务清单：
- [ ] 保持装备强化模拟器前端功能
- [ ] 保持星之力强化模拟器前端功能
- [ ] 保持道具数据库展示功能
- [ ] 集成新的权限系统到前端组件
- [ ] 添加货币消费逻辑到模拟器
- [ ] 优化用户体验和界面
- [ ] 添加使用统计和分析

验收标准：
- 所有现有前端功能正常工作
- 权限控制在前端正确显示
- 货币消费逻辑正确集成
- 用户体验保持或提升
- 新功能与现有功能无缝集成
```

#### 阶段五：测试和优化 (1-2周)
```markdown
目标：全面测试和性能优化

任务清单：
- [ ] 编写单元测试
- [ ] 执行集成测试
- [ ] 进行性能测试
- [ ] 安全性测试
- [ ] 用户体验测试
- [ ] 修复发现的问题
- [ ] 性能优化

验收标准：
- 测试覆盖率 > 80%
- 性能指标达标
- 安全漏洞修复
- 用户反馈良好
```

### 数据初始化方案

#### 静态数据处理
```markdown
说明：当前项目为纯前端项目，没有数据库，所有数据都是静态文件

现有数据资源：
- 装备数据：public/data/itemList.js
- CMS-216道具图片：public/images/cms-216/
- UI资源：public/images/UIEquipEnchant/
- 其他静态资源：public/images/

数据处理策略：
1. 保持现有静态文件结构不变
2. 新增数据库存储用户相关数据
3. 静态资源继续通过文件系统访问
4. 逐步将部分静态数据迁移到数据库（可选）
```

#### 数据库初始化
```python
# 初始化基础数据
async def initialize_database():
    # 1. 创建默认会员等级
    await create_default_membership_tiers()

    # 2. 创建系统管理员账户
    admin_user = await create_admin_user()

    # 3. 初始化货币系统配置
    await initialize_currency_settings()

    # 4. 创建默认权限配置
    await setup_default_permissions()

# 会员等级初始化
async def create_default_membership_tiers():
    tiers = [
        {"name": "guest", "level": 0, "display_name": "游客"},
        {"name": "registered", "level": 1, "display_name": "注册用户"},
        {"name": "gold", "level": 2, "display_name": "黄金用户"},
        {"name": "diamond", "level": 3, "display_name": "钻石用户"},
        {"name": "admin", "level": 4, "display_name": "管理员"}
    ]

    for tier_data in tiers:
        await create_membership_tier(**tier_data)
```

### 风险控制

#### 回滚方案
```bash
# 快速回滚脚本 (Rocky Linux 9.5)
#!/bin/bash
BACKUP_VERSION=$1

if [ -z "$BACKUP_VERSION" ]; then
    echo "请指定备份版本"
    exit 1
fi

echo "回滚到版本: $BACKUP_VERSION"

# 停止服务
sudo systemctl stop mxd-backend
sudo systemctl stop nginx

# 恢复代码备份
sudo cp -r "/backup/backup-$BACKUP_VERSION" "/app/maplestory-info-station"

# 恢复数据库
sudo -u postgres pg_restore -d mxd_info_db "/backup/db-backup-$BACKUP_VERSION.sql"

# 恢复前端文件
sudo cp -r "/backup/backup-$BACKUP_VERSION/frontend/out" "/usr/share/nginx/html/mxd"
sudo chown -R nginx:nginx "/usr/share/nginx/html/mxd"

# 重启服务
sudo systemctl start mxd-backend
sudo systemctl start nginx

# 健康检查
sleep 10
curl -f http://localhost:8000/health && echo "回滚成功" || echo "回滚失败"
```

#### 监控和告警
```yaml
# 监控配置
monitoring:
  metrics:
    - api_response_time
    - database_connection_count
    - redis_memory_usage
    - user_registration_rate
    - currency_transaction_rate

  alerts:
    - name: "API响应时间过长"
      condition: "avg_response_time > 2s"
      action: "发送邮件通知"

    - name: "数据库连接异常"
      condition: "db_connection_errors > 10"
      action: "发送短信通知"

    - name: "货币系统异常"
      condition: "currency_errors > 5"
      action: "立即通知管理员"
```

## 📖 开发指南

### 开发环境搭建

#### 本地开发环境准备
```bash
# 1. 安装系统依赖 (以 macOS/Linux 为例)
# macOS
brew install postgresql@16 redis node@18 python@3.11

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-16 redis-server nodejs npm python3.11 python3.11-pip

# Rocky Linux 9.5 (开发环境)
sudo dnf install postgresql16-server redis nodejs npm python3.11 python3.11-pip

# 2. 启动数据库服务
sudo systemctl start postgresql-16
sudo systemctl start redis

# 3. 创建数据库
sudo -u postgres createdb mxd_info_db
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';"
```

#### 快速开始
```bash
# 1. 克隆项目
git clone https://github.com/your-org/maplestory-info-station.git
cd maplestory-info-station

# 2. 安装前端依赖
cd frontend
npm install

# 3. 安装后端依赖
cd ../backend
python3.11 -m pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入数据库连接信息

# 5. 初始化数据库
alembic upgrade head
python3.11 scripts/init_data.py

# 6. 启动开发服务器
# 终端1: 启动后端
cd backend && python3.11 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 终端2: 启动前端
cd frontend && npm run dev
```

#### 开发工具配置
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}

// .vscode/extensions.json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.pylint",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### 代码规范

#### 前端代码规范
```typescript
// 组件命名：PascalCase
export function UserProfile() {}

// 文件命名：kebab-case
// user-profile.tsx
// api-client.ts

// 常量命名：UPPER_SNAKE_CASE
const API_ENDPOINTS = {
  AUTH: '/auth',
  USERS: '/users'
}

// 接口命名：PascalCase + Interface后缀
interface UserProfileProps {
  userId: number
  onUpdate: (user: User) => void
}

// 类型命名：PascalCase
type MembershipTier = 'guest' | 'registered' | 'gold' | 'diamond' | 'admin'
```

#### 后端代码规范
```python
# 文件命名：snake_case
# user_service.py
# auth_middleware.py

# 类命名：PascalCase
class UserService:
    pass

# 函数命名：snake_case
async def get_user_by_id(user_id: int) -> Optional[User]:
    pass

# 常量命名：UPPER_SNAKE_CASE
MAX_LOGIN_ATTEMPTS = 5
TOKEN_EXPIRE_MINUTES = 15

# 私有方法：_开头
def _validate_password(password: str) -> bool:
    pass
```

### API 开发规范

#### RESTful API 设计原则
```python
# 资源命名：复数形式
GET    /api/v1/users          # 获取用户列表
POST   /api/v1/users          # 创建用户
GET    /api/v1/users/{id}     # 获取特定用户
PUT    /api/v1/users/{id}     # 更新用户
DELETE /api/v1/users/{id}     # 删除用户

# 嵌套资源
GET    /api/v1/users/{id}/transactions  # 获取用户交易记录
POST   /api/v1/users/{id}/transactions  # 创建交易记录

# 动作资源：使用动词
POST   /api/v1/auth/login     # 登录
POST   /api/v1/auth/logout    # 登出
POST   /api/v1/currency/consume  # 消费货币
```

#### 错误处理规范
```python
# app/utils/exceptions.py
from fastapi import HTTPException

class BusinessException(HTTPException):
    def __init__(self, code: str, message: str, details: dict = None):
        super().__init__(
            status_code=400,
            detail={
                "code": code,
                "message": message,
                "details": details or {}
            }
        )

# 使用示例
if user.balance < amount:
    raise BusinessException(
        code="INSUFFICIENT_BALANCE",
        message="欢乐豆余额不足",
        details={
            "required": float(amount),
            "available": float(user.balance)
        }
    )
```

### 测试规范

#### 前端测试
```typescript
// __tests__/components/UserProfile.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { UserProfile } from '@/components/UserProfile'

describe('UserProfile', () => {
  it('should display user information', () => {
    const mockUser = {
      id: 1,
      username: 'testuser',
      membership: { tier: 'gold', level: 2 }
    }

    render(<UserProfile user={mockUser} />)

    expect(screen.getByText('testuser')).toBeInTheDocument()
    expect(screen.getByText('黄金用户')).toBeInTheDocument()
  })

  it('should handle edit button click', () => {
    const mockOnEdit = jest.fn()

    render(<UserProfile user={mockUser} onEdit={mockOnEdit} />)

    fireEvent.click(screen.getByText('编辑'))
    expect(mockOnEdit).toHaveBeenCalledWith(mockUser)
  })
})
```

#### 后端测试
```python
# tests/test_auth.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_user_registration():
    response = client.post("/api/v1/auth/register", json={
        "username": "testuser",
        "password": "testpass123",
        "fingerprint_id": "test_fingerprint"
    })

    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]["tokens"]

def test_user_login():
    # 先注册用户
    client.post("/api/v1/auth/register", json={
        "username": "testuser2",
        "password": "testpass123",
        "fingerprint_id": "test_fingerprint2"
    })

    # 测试登录
    response = client.post("/api/v1/auth/login", json={
        "username": "testuser2",
        "password": "testpass123",
        "fingerprint_id": "test_fingerprint2"
    })

    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
```

### 性能优化指南

#### 前端性能优化
```typescript
// 1. 组件懒加载
const LazyComponent = lazy(() => import('./HeavyComponent'))

// 2. 图片懒加载
import { LazyImage } from '@/components/LazyImage'

// 3. 虚拟滚动
import { FixedSizeList as List } from 'react-window'

// 4. 状态优化
const memoizedValue = useMemo(() => {
  return expensiveCalculation(data)
}, [data])

// 5. 防抖处理
const debouncedSearch = useDebounce(searchTerm, 300)
```

#### 后端性能优化
```python
# 1. 数据库查询优化
async def get_user_with_membership(user_id: int):
    return await db.query(User).options(
        joinedload(User.membership),
        joinedload(User.currency_account)
    ).filter(User.id == user_id).first()

# 2. 缓存策略
from functools import lru_cache

@lru_cache(maxsize=128)
async def get_membership_permissions(tier: str):
    return MEMBERSHIP_PERMISSIONS.get(tier)

# 3. 异步处理
import asyncio

async def send_email_async(email_data):
    await asyncio.create_task(email_service.send(email_data))

# 4. 连接池配置
engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

## 📊 项目总结

### 技术架构优势

#### 🎯 核心优势
1. **技术栈现代化**
   - Next.js 14 + FastAPI 的现代化组合
   - TypeScript 全栈类型安全
   - Rocky Linux 9.5 生产环境部署

2. **架构设计合理**
   - 前后端职责分离清晰
   - RESTful API 设计规范
   - 传统部署架构稳定可靠

3. **用户体验优化**
   - SSG 保持快速加载
   - 渐进式功能增强
   - 响应式设计适配多端

4. **安全性保障**
   - JWT 认证机制
   - 权限分级控制
   - 数据加密和验证

5. **部署和运维**
   - Rocky Linux 9.5 企业级稳定性
   - 传统服务部署易于管理
   - 完整的监控和日志方案

### 实施建议

#### 🚀 推荐实施路径
1. **阶段性迁移** - 降低风险，确保稳定性
2. **保持兼容** - 现有前端功能无缝集成
3. **渐进增强** - 逐步添加后端功能
4. **持续优化** - 根据用户反馈调整

#### ⚠️ 注意事项
1. **环境准备** - Rocky Linux 9.5 环境配置
2. **服务管理** - 传统系统服务配置和监控
3. **用户沟通** - 及时告知用户变更
4. **团队培训** - 确保团队掌握新技术栈
5. **备份策略** - 代码和数据库的定期备份

### 预期收益

#### 📈 业务价值
- **用户增长**: 会员系统促进用户注册和留存
- **收入模式**: 虚拟货币系统支持商业化
- **数据洞察**: 用户行为分析和优化
- **品牌提升**: 现代化技术栈提升专业形象

#### 🔧 技术价值
- **可维护性**: 代码结构清晰，易于维护
- **可扩展性**: 前后端分离架构支持功能扩展
- **开发效率**: 现代化工具链提升开发效率
- **团队成长**: 新技术栈提升团队技能
- **运维简化**: 传统部署方式，运维团队容易掌握

### 🎯 最佳实践优化建议

#### 1. 性能优化建议
```markdown
前端性能优化：
- 使用 Next.js 的 Image 组件优化图片加载
- 实现虚拟滚动处理大量装备列表
- 使用 React.memo 和 useMemo 优化组件渲染
- 实现代码分割和懒加载
- 使用 Service Worker 缓存静态资源

后端性能优化：
- 使用连接池管理数据库连接
- 实现 Redis 缓存热点数据
- 使用异步处理提升并发性能
- 实现 API 响应压缩
- 添加数据库查询优化和索引
```

#### 2. 安全性增强
```markdown
认证安全：
- 实现账户锁定机制（连续失败登录）
- 添加验证码防止暴力破解
- 实现设备信任机制
- 添加异地登录提醒

数据安全：
- 敏感数据加密存储
- 实现 API 限流和防 DDoS
- 添加 SQL 注入防护
- 实现 CSRF 防护
- 定期安全审计和漏洞扫描
```

#### 3. 监控和运维
```markdown
应用监控：
- 集成 APM 工具（如 New Relic、DataDog）
- 实现自定义业务指标监控
- 添加错误追踪和报警
- 实现性能基准测试

日志管理：
- 结构化日志输出
- 集中化日志收集
- 日志分级和轮转
- 敏感信息脱敏
```

#### 4. 用户体验优化
```markdown
界面优化：
- 实现骨架屏加载效果
- 添加操作反馈和进度提示
- 优化移动端触摸体验
- 实现无障碍访问支持

功能优化：
- 添加操作撤销功能
- 实现数据本地缓存
- 添加离线模式支持
- 优化网络错误处理
```

---

## 📞 联系方式

**项目负责人**: 小帽子
**QQ**: 499151029
**网站**: Mxd.dvg.cn
**文档版本**: v1.0.0
**最后更新**: 2025-06-24

### 技术支持

如果在实施过程中遇到问题，欢迎通过以下方式获取支持：

1. **GitHub Issues**: 在项目仓库中提交技术问题
2. **QQ 联系**: 499151029 (工作时间快速响应)
3. **邮件咨询**: 通过项目页面联系表单

### 贡献指南

欢迎社区贡献代码和建议：

1. **Fork 项目** - 创建自己的分支
2. **提交 PR** - 遵循代码规范
3. **问题反馈** - 提交 Bug 报告和功能建议
4. **文档完善** - 帮助改进项目文档

感谢您对冒险岛情报站项目的关注和支持！🎮✨
