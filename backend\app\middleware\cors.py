"""
CORS middleware configuration
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings

def setup_cors(app: FastAPI) -> None:
    """
    Setup CORS middleware for the FastAPI application
    """
    # Parse comma-separated strings to lists
    origins = [origin.strip() for origin in settings.ALLOWED_ORIGINS.split(",")]
    methods = [method.strip() for method in settings.ALLOWED_METHODS.split(",")]
    headers = [header.strip() for header in settings.ALLOWED_HEADERS.split(",")]

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=methods,
        allow_headers=headers,
        expose_headers=["X-Process-Time", "X-Request-ID"],
    )
