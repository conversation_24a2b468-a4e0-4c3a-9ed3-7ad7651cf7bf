"""
Currency models
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, DECIMAL, DateTime, Boolean, ForeignKey, Index, CheckConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class CurrencyAccount(Base):
    __tablename__ = "currency_accounts"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True)
    balance = Column(DECIMAL(15, 2), default=0.00, nullable=False)  # 欢乐豆余额
    total_earned = Column(DECIMAL(15, 2), default=0.00, nullable=False)  # 总获得
    total_spent = Column(DECIMAL(15, 2), default=0.00, nullable=False)   # 总消费
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="currency_account")
    
    # Constraints
    __table_args__ = (
        CheckConstraint('balance >= 0', name='positive_balance'),
        Index('idx_currency_account_user', 'user_id'),
    )
