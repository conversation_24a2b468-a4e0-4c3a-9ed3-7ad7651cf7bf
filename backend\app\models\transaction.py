"""
Transaction models
"""

from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text, ForeignKey, Index, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class Transaction(Base):
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    type = Column(String(20), nullable=False)  # earn, spend, refund
    amount = Column(DECIMAL(15, 2), nullable=False)
    balance_before = Column(DECIMAL(15, 2), nullable=False)
    balance_after = Column(DECIMAL(15, 2), nullable=False)
    description = Column(Text, nullable=True)
    reference_type = Column(String(50), nullable=True)  # enhancement, purchase, etc.
    reference_id = Column(String(100), nullable=True)   # 关联的业务ID
    extra_data = Column(JSON, nullable=True)  # 额外信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="transactions")
    
    # Indexes
    __table_args__ = (
        Index('idx_transaction_user_created', 'user_id', 'created_at'),
        Index('idx_transaction_type', 'type'),
        Index('idx_transaction_reference', 'reference_type', 'reference_id'),
    )

class EmailVerification(Base):
    __tablename__ = "email_verifications"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    email = Column(String(255), nullable=False)
    token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    verified_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="email_verifications")
    
    # Indexes
    __table_args__ = (
        Index('idx_email_verification_token', 'token'),
        Index('idx_email_verification_user', 'user_id', 'verified_at'),
    )

class PasswordReset(Base):
    __tablename__ = "password_resets"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    used_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="password_resets")
    
    # Indexes
    __table_args__ = (
        Index('idx_password_reset_token', 'token'),
        Index('idx_password_reset_user', 'user_id', 'used_at'),
    )

class DeviceFingerprint(Base):
    __tablename__ = "device_fingerprints"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    fingerprint_id = Column(String(255), unique=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    device_info = Column(JSON, nullable=True)  # 设备信息
    first_seen = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    last_seen = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    visit_count = Column(Integer, default=1, nullable=False)
    
    # Indexes
    __table_args__ = (
        Index('idx_device_fingerprint_id', 'fingerprint_id'),
        Index('idx_device_fingerprint_user', 'user_id'),
    )

class UserSession(Base):
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False)
    refresh_token = Column(String(255), unique=True, nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    ip_address = Column(String(45), nullable=True)  # Support IPv6
    user_agent = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    # Indexes
    __table_args__ = (
        Index('idx_user_session_token', 'session_token'),
        Index('idx_user_session_refresh', 'refresh_token'),
        Index('idx_user_session_user_expires', 'user_id', 'expires_at'),
    )
