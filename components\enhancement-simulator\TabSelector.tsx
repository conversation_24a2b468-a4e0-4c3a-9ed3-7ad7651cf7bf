'use client'

import { TabSelectorProps, EnhancementType } from '@/types/enhancement'

const TAB_CONFIG = {
  starforce: {
    name: '星之力',
    position: { top: 81, left: 20 },
    images: {
      normal: '/images/UIEquipEnchant/Main/Tab/Starforce/normal/0.png',
      selected: '/images/UIEquipEnchant/Main/Tab/Starforce/selected/0.png',
      hover: '/images/UIEquipEnchant/Main/Tab/Starforce/disabled/0.png',
    },
  },
  potential: {
    name: '潜能',
    position: { top: 81, left: 178 },
    images: {
      normal: '/images/UIEquipEnchant/Main/Tab/Potential/normal/0.png',
      selected: '/images/UIEquipEnchant/Main/Tab/Potential/selected/0.png',
      hover: '/images/UIEquipEnchant/Main/Tab/Potential/disabled/0.png',
    },
  },
  bonusstat: {
    name: '额外属性',
    position: { top: 81, left: 336 },
    images: {
      normal: '/images/UIEquipEnchant/Main/Tab/Bonusstat/normal/0.png',
      selected: '/images/UIEquipEnchant/Main/Tab/Bonusstat/selected/0.png',
      hover: '/images/UIEquipEnchant/Main/Tab/Bonusstat/disabled/0.png',
    },
  },
}

interface TabButtonProps {
  type: EnhancementType
  isActive: boolean
  disabled: boolean
  onClick: () => void
}

function TabButton({ type, isActive, disabled, onClick }: TabButtonProps) {
  const config = TAB_CONFIG[type]
  
  return (
    <button
      className={`
        absolute w-[158px] h-[33px] bg-cover bg-no-repeat border-none outline-none cursor-pointer z-[100]
        transition-none
        ${disabled ? 'cursor-not-allowed opacity-50' : ''}
      `}
      style={{
        top: `${config.position.top}px`,
        left: `${config.position.left}px`,
        backgroundImage: `url(${isActive ? config.images.selected : config.images.normal})`,
      }}
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={(e) => {
        if (!isActive && !disabled) {
          e.currentTarget.style.backgroundImage = `url(${config.images.hover})`
        }
      }}
      onMouseLeave={(e) => {
        if (!isActive && !disabled) {
          e.currentTarget.style.backgroundImage = `url(${config.images.normal})`
        }
      }}
      aria-label={`切换到${config.name}强化`}
    >
      <span className="sr-only">{config.name}</span>
    </button>
  )
}

export default function TabSelector({ currentTab, onTabChange, disabled = false }: TabSelectorProps) {
  const tabs: EnhancementType[] = ['starforce', 'potential', 'bonusstat']
  
  return (
    <>
      {/* 标签页按钮 */}
      {tabs.map((tab) => (
        <TabButton
          key={tab}
          type={tab}
          isActive={currentTab === tab}
          disabled={disabled}
          onClick={() => !disabled && onTabChange(tab)}
        />
      ))}
      
      {/* 标签页分割线 */}
      <div
        className="absolute w-[482px] h-[2px] bg-cover bg-no-repeat z-[100]"
        style={{
          top: '112px',
          left: '7px',
          backgroundImage: 'url(/images/UIEquipEnchant/Main/Tab/Image_Line.png)',
        }}
      />
    </>
  )
}
