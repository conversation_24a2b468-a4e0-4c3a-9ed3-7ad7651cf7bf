const { execSync } = require("child_process")
const fs = require("fs")
const path = require("path")

console.log("Final build test...")

try {
  // Create the cms-216 directory if it doesn't exist
  const cmsDir = path.join(process.cwd(), "public/images/cms-216")
  if (!fs.existsSync(cmsDir)) {
    fs.mkdirSync(cmsDir, { recursive: true })
    console.log("Created cms-216 directory")
  }

  // Run the build
  console.log("Running final build test...")
  const result = execSync("npm run build", {
    encoding: "utf8",
    stdio: "pipe",
    timeout: 120000, // 2 minutes timeout
  })

  console.log("✅ Build successful!")
  console.log("Build output:", result)
} catch (error) {
  console.log("❌ Build failed:")
  console.log("STDOUT:", error.stdout)
  console.log("STDERR:", error.stderr)

  // Provide specific error analysis
  const errorOutput = (error.stderr || "") + (error.stdout || "")

  if (errorOutput.includes("Module not found")) {
    console.log("\n🔍 Missing modules detected")
  }

  if (errorOutput.includes("Type error")) {
    console.log("\n🔍 TypeScript errors detected")
  }

  if (errorOutput.includes("Cannot resolve")) {
    console.log("\n🔍 Import resolution errors detected")
  }
}
