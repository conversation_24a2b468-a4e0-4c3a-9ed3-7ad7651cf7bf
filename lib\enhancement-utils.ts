// 装备强化模拟器工具函数

import {
  EnhancementType,
  EnhancementProbability,
  EnhancementResult,
  EnhancementResultType,
  EquipmentStats,
  ProbabilityTable,
  EnhancementCosts,
  CategoryMapping,
  TestEquipItem,
  ItemDatabase,
  ItemDatabaseEntry,
} from '@/types/enhancement'

// 星之力强化概率数据（按照游戏设定）
export const STARFORCE_PROBABILITIES: ProbabilityTable = {
  0: { success: 95, failure: 5, major_failure: 0, failure_drop: 0 },
  1: { success: 90, failure: 10, major_failure: 0, failure_drop: 0 },
  2: { success: 85, failure: 15, major_failure: 0, failure_drop: 0 },
  3: { success: 85, failure: 15, major_failure: 0, failure_drop: 0 },
  4: { success: 80, failure: 20, major_failure: 0, failure_drop: 0 },
  5: { success: 75, failure: 25, major_failure: 0, failure_drop: 0 },
  6: { success: 70, failure: 30, major_failure: 0, failure_drop: 0 },
  7: { success: 65, failure: 35, major_failure: 0, failure_drop: 0 },
  8: { success: 60, failure: 40, major_failure: 0, failure_drop: 0 },
  9: { success: 55, failure: 45, major_failure: 0, failure_drop: 0 },
  10: { success: 50, failure: 45, major_failure: 5, failure_drop: 0 },
  11: { success: 45, failure: 50, major_failure: 5, failure_drop: 0 },
  12: { success: 40, failure: 55, major_failure: 5, failure_drop: 0 },
  13: { success: 35, failure: 60, major_failure: 5, failure_drop: 0 },
  14: { success: 30, failure: 65, major_failure: 5, failure_drop: 0 },
  15: { success: 30, failure: 67.9, major_failure: 2.1, failure_drop: 0 },
  16: { success: 30, failure: 67, major_failure: 3, failure_drop: 0 },
  17: { success: 30, failure: 65.9, major_failure: 4.1, failure_drop: 0 },
  18: { success: 30, failure: 64.8, major_failure: 5.2, failure_drop: 0 },
  19: { success: 30, failure: 63.7, major_failure: 6.3, failure_drop: 0 },
  20: { success: 30, failure: 62.6, major_failure: 7.4, failure_drop: 0 },
  21: { success: 30, failure: 61.5, major_failure: 8.5, failure_drop: 0 },
  22: { success: 3, failure: 77.6, major_failure: 19.4, failure_drop: 0 },
  23: { success: 2, failure: 68.6, major_failure: 29.4, failure_drop: 0 },
  24: { success: 1, failure: 59.4, major_failure: 39.6, failure_drop: 0 },
}

// 强化费用（金币）
export const ENHANCEMENT_COSTS: EnhancementCosts = {
  0: 1000000, 1: 2000000, 2: 4000000, 3: 8000000, 4: 16000000,
  5: 32000000, 6: 64000000, 7: 128000000, 8: 256000000, 9: 512000000,
  10: 1024000000, 11: 2048000000, 12: 4096000000, 13: 8192000000, 14: 16384000000,
  15: 32768000000, 16: 65536000000, 17: 131072000000, 18: 262144000000, 19: 524288000000,
  20: 1048576000000, 21: 2097152000000, 22: 4194304000000, 23: 8388608000000, 24: 16777216000000,
}

// 装备类型映射
export const CATEGORY_MAPPING: CategoryMapping = {
  1000201001: '战士帽子', 1000301001: '普通帽子',
  1000201002: '战士上衣', 1000301002: '普通上衣',
  1000201003: '战士下装', 1000301003: '普通下装',
  1000201004: '战士套装', 1000301004: '普通套装',
  1000201005: '战士鞋子', 1000301005: '普通鞋子',
  1000201006: '战士手套', 1000301006: '普通手套',
  1000201007: '战士武器', 1000301007: '普通武器',
  1000201008: '战士盾牌', 1000301008: '普通盾牌',
  1000201009: '战士饰品', 1000301009: '普通饰品',
  1000401001: '宠物',
  1000501001: '消耗品',
}

// 测试装备列表
export const TEST_EQUIP_ITEMS: TestEquipItem[] = [
  { id: '1001164', name: '加载中...', type: 'hat' },
  { id: '1072952', name: '加载中...', type: 'shoes' },
  { id: '1082169', name: '加载中...', type: 'gloves' },
  { id: '1102149', name: '加载中...', type: 'ring' },
  { id: '1382104', name: '加载中...', type: 'overall' },
  { id: '1702424', name: '加载中...', type: 'weapon' },
  { id: '1702952', name: '加载中...', type: 'weapon' },
  { id: '1790342', name: '加载中...', type: 'weapon' },
  { id: '1791494', name: '加载中...', type: 'weapon' },
]

/**
 * 获取强化概率
 */
export function getEnhancementProbability(
  type: EnhancementType,
  level: number,
  starcatchEnabled: boolean = false,
  preventEnabled: boolean = false
): EnhancementProbability {
  if (type === 'starforce') {
    const baseProbs = STARFORCE_PROBABILITIES[level] || 
      { success: 0, failure: 0, major_failure: 0, failure_drop: 0 }
    
    // 应用镇护效果
    let adjustedProbs = { ...baseProbs }
    if (preventEnabled && adjustedProbs.major_failure > 0) {
      adjustedProbs.failure += adjustedProbs.major_failure
      adjustedProbs.major_failure = 0
    }
    
    return adjustedProbs
  } else {
    // 潜能和额外属性总是成功
    return { success: 100, failure: 0, major_failure: 0, failure_drop: 0 }
  }
}

/**
 * 获取强化费用
 */
export function getEnhancementCost(type: EnhancementType, level: number): number {
  if (type === 'starforce') {
    return ENHANCEMENT_COSTS[level] || 0
  } else {
    return 500000 // 潜能和额外属性固定费用
  }
}

/**
 * 计算强化结果
 */
export function calculateEnhancementResult(
  type: EnhancementType,
  currentLevel: number,
  starcatchEnabled: boolean = false,
  preventEnabled: boolean = false,
  minigameBonus: number = 0
): EnhancementResult {
  if (type === 'starforce') {
    const probs = getEnhancementProbability(type, currentLevel, starcatchEnabled, preventEnabled)
    const random = Math.random() * 100
    let adjustedSuccessRate = probs.success + minigameBonus
    
    if (random < adjustedSuccessRate) {
      // 成功
      const newLevel = currentLevel + 1
      return {
        type: 'success',
        message: `强化成功！当前星力：${newLevel}`,
        level: newLevel,
        previousLevel: currentLevel,
      }
    } else if (random < adjustedSuccessRate + probs.failure) {
      // 失败
      return {
        type: 'failed',
        message: '强化失败，装备未发生变化',
        level: currentLevel,
        previousLevel: currentLevel,
      }
    } else if (probs.major_failure > 0) {
      // 大失败
      let newLevel = currentLevel
      if (currentLevel > 15) {
        newLevel = Math.max(12, currentLevel - 3)
        return {
          type: 'major_failure',
          message: `装备破坏！星力降低至：${newLevel}`,
          level: newLevel,
          previousLevel: currentLevel,
        }
      } else if (currentLevel > 10) {
        newLevel = currentLevel - 1
        return {
          type: 'major_failure',
          message: `强化失败！星力下降至：${newLevel}`,
          level: newLevel,
          previousLevel: currentLevel,
        }
      }
    }
    
    // 默认失败
    return {
      type: 'failed',
      message: '强化失败，装备未发生变化',
      level: currentLevel,
      previousLevel: currentLevel,
    }
  } else {
    // 潜能和额外属性总是成功
    return {
      type: 'success',
      message: `${type === 'potential' ? '潜能重设' : '额外属性强化'}成功！`,
      level: currentLevel,
      previousLevel: currentLevel,
    }
  }
}

/**
 * 计算装备属性
 */
export function calculateEquipmentStats(level: number): EquipmentStats {
  // 简化的属性计算（每级+2主属性，+5HP，+6防御）
  return {
    STR: level * 2,
    DEX: level * 2,
    INT: level * 2,
    LUK: level * 2,
    MaxHP: level * 5,
    DEF: level * 6,
  }
}

/**
 * 计算迷你游戏奖励
 */
export function calculateMinigameBonus(gaugePosition: number): number {
  // 根据停止位置计算成功率奖励
  if (gaugePosition >= 45 && gaugePosition <= 55) {
    return 10 // 完美停止，+10%成功率
  } else if (gaugePosition >= 35 && gaugePosition <= 65) {
    return 5 // 良好停止，+5%成功率
  }
  return 0 // 无奖励
}

/**
 * 获取装备类型名称
 */
export function getItemTypeName(category: number): string {
  return CATEGORY_MAPPING[category] || '未知类型'
}

/**
 * 格式化数字（添加千分位分隔符）
 */
export function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 获取随机测试装备
 */
export function getRandomTestEquip(): TestEquipItem {
  return TEST_EQUIP_ITEMS[Math.floor(Math.random() * TEST_EQUIP_ITEMS.length)]
}

/**
 * 加载装备数据库
 */
export async function loadItemDatabase(): Promise<ItemDatabase | null> {
  try {
    const response = await fetch('/data/itemList.json')
    if (!response.ok) {
      throw new Error('Failed to load item database')
    }
    return await response.json()
  } catch (error) {
    console.error('加载装备数据库失败:', error)
    return null
  }
}

/**
 * 从数据库获取装备信息
 */
export function getItemInfo(itemDatabase: ItemDatabase | null, itemId: string): ItemDatabaseEntry | null {
  if (!itemDatabase) return null
  
  const numericId = parseInt(itemId)
  return itemDatabase.orderBook.find(item => item.itemId === numericId) || null
}

/**
 * 预加载图片资源
 */
export function preloadImages(): void {
  const imagesToPreload = [
    '/images/UIEquipEnchant/Main/Background.png',
    '/images/UIEquipEnchant/Main/Equip/Slot/Starforce.png',
    '/images/UIEquipEnchant/Main/Equip/Slot/Potential.png',
    '/images/UIEquipEnchant/Main/Equip/Slot/Bonusstat.png',
  ]

  // 预加载所有特效动画帧
  for (let i = 0; i < 16; i++) {
    imagesToPreload.push(`/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Standby/${i}.png`)
    imagesToPreload.push(`/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Progress/Loop/${i}.png`)
  }

  for (let i = 0; i < 18; i++) {
    imagesToPreload.push(`/images/UIEquipEnchant/Main/Equip/Effect/Starforce/Result/Success/${i}.png`)
  }

  imagesToPreload.forEach(src => {
    const img = new Image()
    img.src = src
  })
}
