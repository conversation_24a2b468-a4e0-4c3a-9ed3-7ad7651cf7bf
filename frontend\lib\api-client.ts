/**
 * API Client for backend communication
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

// 认证相关类型
export interface UserRegisterRequest {
  username?: string
  email?: string
  password: string
  fingerprint_id?: string
}

export interface UserLoginRequest {
  username?: string
  email?: string
  password: string
  fingerprint_id?: string
}

export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: UserResponse
}

export interface UserResponse {
  id: number
  username: string
  email?: string
  is_active: boolean
  is_verified: boolean
  created_at: string
  last_login?: string
  fingerprint_id?: string
}

export interface EmailVerificationRequest {
  token: string
}

export interface ResendVerificationRequest {
  email: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirmRequest {
  token: string
  new_password: string
}

class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  // 令牌管理
  private getAccessToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('access_token')
  }

  private getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('refresh_token')
  }

  private setTokens(accessToken: string, refreshToken: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem('access_token', accessToken)
    localStorage.setItem('refresh_token', refreshToken)
  }

  private clearTokens(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }

  // 设备指纹管理
  private getFingerprintId(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('fingerprint_id')
  }

  public setFingerprintId(fingerprintId: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem('fingerprint_id', fingerprintId)
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`

    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    // 添加认证令牌
    const token = this.getAccessToken()
    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`
    }

    // 添加设备指纹
    const fingerprintId = this.getFingerprintId()
    if (fingerprintId) {
      defaultHeaders['X-Fingerprint-ID'] = fingerprintId
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      // 处理 401 错误（令牌过期）
      if (response.status === 401 && token) {
        try {
          await this.refreshToken()
          // 重试请求
          const newToken = this.getAccessToken()
          if (newToken) {
            const retryConfig = {
              ...config,
              headers: {
                ...config.headers,
                Authorization: `Bearer ${newToken}`
              }
            }
            const retryResponse = await fetch(url, retryConfig)
            return await retryResponse.json()
          }
        } catch (refreshError) {
          this.clearTokens()
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login'
          }
          throw refreshError
        }
      }

      if (!response.ok) {
        throw new Error(data.error?.message || data.detail || `HTTP ${response.status}`)
      }

      return data
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // 刷新令牌
  async refreshToken(): Promise<void> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await fetch(`${this.baseURL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    })

    const result = await response.json()
    if (result.success && result.data) {
      this.setTokens(result.data.access_token, result.data.refresh_token)
    } else {
      throw new Error('Token refresh failed')
    }
  }

  // Health check (note: health endpoint is at root level, not under /api/v1)
  async healthCheck() {
    const url = `${process.env.NEXT_PUBLIC_API_URL?.replace('/api/v1', '') || 'http://localhost:8000'}/health`

    const response = await fetch(url)
    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error?.message || `HTTP ${response.status}`)
    }

    return data
  }

  // Auth endpoints
  async register(userData: UserRegisterRequest): Promise<ApiResponse<{ user: UserResponse; message: string }>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  }

  async login(credentials: UserLoginRequest): Promise<ApiResponse<TokenResponse>> {
    const result = await this.request<TokenResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })

    if (result.success && result.data) {
      this.setTokens(result.data.access_token, result.data.refresh_token)
    }

    return result
  }

  async logout(): Promise<ApiResponse<{ message: string }>> {
    const refreshToken = this.getRefreshToken()
    const result = await this.request('/auth/logout', {
      method: 'POST',
      body: JSON.stringify({ refresh_token: refreshToken }),
    })

    this.clearTokens()
    return result
  }

  async verifyEmail(data: EmailVerificationRequest): Promise<ApiResponse<{ message: string }>> {
    return this.request('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async resendVerification(data: ResendVerificationRequest): Promise<ApiResponse<{ message: string }>> {
    return this.request('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async forgotPassword(data: PasswordResetRequest): Promise<ApiResponse<{ message: string }>> {
    return this.request('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async resetPassword(data: PasswordResetConfirmRequest): Promise<ApiResponse<{ message: string }>> {
    return this.request('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // User endpoints
  async getCurrentUser(): Promise<ApiResponse<UserResponse>> {
    return this.request('/users/me')
  }

  // Currency endpoints
  async getBalance() {
    return this.request('/currency/balance')
  }

  // Enhancement endpoints
  async starforceEnhancement(data: any) {
    return this.request('/enhancement/starforce', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient()
export default apiClient
