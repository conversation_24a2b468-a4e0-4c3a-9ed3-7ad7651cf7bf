'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { useAuthStore } from '@/store/auth-store'
import { Loader2, Mail, User, CheckCircle } from 'lucide-react'

export default function RegisterPage() {
  const router = useRouter()
  const { register, isLoading, error, clearError, isAuthenticated } = useAuthStore()

  const [registerType, setRegisterType] = useState<'email' | 'username'>('email')
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    confirmPassword: ''
  })
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [registrationSuccess, setRegistrationSuccess] = useState(false)

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/')
    }
  }, [isAuthenticated, router])

  // 清除错误信息
  useEffect(() => {
    return () => clearError()
  }, [clearError])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    clearError()
  }

  const validateForm = () => {
    if (registerType === 'email' && !formData.email) {
      return '请输入邮箱地址'
    }
    if (registerType === 'username' && !formData.username) {
      return '请输入用户名'
    }
    if (!formData.password) {
      return '请输入密码'
    }
    if (formData.password.length < 8) {
      return '密码长度至少8位'
    }
    if (formData.password !== formData.confirmPassword) {
      return '两次输入的密码不一致'
    }
    if (!agreedToTerms) {
      return '请同意服务条款和隐私政策'
    }
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const validationError = validateForm()
    if (validationError) {
      // 这里可以设置错误状态，但由于我们没有在 store 中设置自定义错误的方法，
      // 我们可以使用 alert 或者扩展 store
      alert(validationError)
      return
    }

    const registerData = {
      password: formData.password,
      ...(registerType === 'email' 
        ? { email: formData.email }
        : { username: formData.username }
      )
    }

    const success = await register(registerData)
    
    if (success) {
      setRegistrationSuccess(true)
    }
  }

  if (registrationSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100 px-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-800">
              注册成功！
            </CardTitle>
            <CardDescription>
              {registerType === 'email' 
                ? '我们已向您的邮箱发送了验证邮件，请查收并点击验证链接完成账户激活。'
                : '您的账户已创建成功，现在可以登录使用所有功能。'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={() => router.push('/auth/login')}
              className="w-full"
            >
              前往登录
            </Button>
            {registerType === 'email' && (
              <Button 
                variant="outline"
                onClick={() => setRegistrationSuccess(false)}
                className="w-full"
              >
                重新发送验证邮件
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">
            注册冒险岛情报站
          </CardTitle>
          <CardDescription className="text-center">
            创建您的账户以享受完整功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Tabs value={registerType} onValueChange={(value) => setRegisterType(value as 'email' | 'username')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="email" className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  邮箱注册
                </TabsTrigger>
                <TabsTrigger value="username" className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  用户名注册
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="email" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">邮箱地址</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="请输入邮箱地址"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                  />
                  <p className="text-xs text-gray-500">
                    邮箱注册需要验证，验证后可正常使用
                  </p>
                </div>
              </TabsContent>
              
              <TabsContent value="username" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">用户名</Label>
                  <Input
                    id="username"
                    name="username"
                    type="text"
                    placeholder="请输入用户名（3-50个字符）"
                    value={formData.username}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    minLength={3}
                    maxLength={50}
                  />
                  <p className="text-xs text-gray-500">
                    用户名注册无需验证，注册后可直接使用
                  </p>
                </div>
              </TabsContent>
            </Tabs>

            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="请输入密码（至少8位）"
                value={formData.password}
                onChange={handleInputChange}
                required
                disabled={isLoading}
                minLength={8}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">确认密码</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                disabled={isLoading}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="terms" 
                checked={agreedToTerms}
                onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
              />
              <Label htmlFor="terms" className="text-sm">
                我同意{' '}
                <Link href="/terms" className="text-blue-600 hover:underline">
                  服务条款
                </Link>
                {' '}和{' '}
                <Link href="/privacy" className="text-blue-600 hover:underline">
                  隐私政策
                </Link>
              </Label>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button 
              type="submit" 
              className="w-full" 
              disabled={isLoading || !agreedToTerms}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  注册中...
                </>
              ) : (
                '注册账户'
              )}
            </Button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-600">
            已有账户？{' '}
            <Link 
              href="/auth/login" 
              className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
            >
              立即登录
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
