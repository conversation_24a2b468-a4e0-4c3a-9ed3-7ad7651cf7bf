'use client'

import { createContext, useContext, useEffect, ReactNode } from 'react'
import { useAuthStore } from '@/store/auth-store'
import { apiClient } from '@/lib/api-client'

interface AuthContextType {
  // 这里可以添加额外的认证相关方法
}

const AuthContext = createContext<AuthContextType>({})

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { getCurrentUser, isAuthenticated } = useAuthStore()

  useEffect(() => {
    // 初始化时检查用户认证状态
    const initAuth = async () => {
      const token = localStorage.getItem('access_token')
      if (token && !isAuthenticated) {
        try {
          await getCurrentUser()
        } catch (error) {
          console.error('Failed to get current user:', error)
          // 清除无效的令牌
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
        }
      }
    }

    initAuth()
  }, [getCurrentUser, isAuthenticated])

  useEffect(() => {
    // 设置设备指纹（如果可用）
    const setFingerprint = async () => {
      try {
        // 这里可以集成 FingerprintJS
        // 暂时使用简单的浏览器指纹
        const fingerprint = generateSimpleFingerprint()
        apiClient.setFingerprintId(fingerprint)
      } catch (error) {
        console.error('Failed to set fingerprint:', error)
      }
    }

    setFingerprint()
  }, [])

  return (
    <AuthContext.Provider value={{}}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// 生成简单的浏览器指纹
function generateSimpleFingerprint(): string {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.textBaseline = 'top'
    ctx.font = '14px Arial'
    ctx.fillText('Browser fingerprint', 2, 2)
  }

  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|')

  // 简单的哈希函数
  let hash = 0
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }

  return Math.abs(hash).toString(36)
}
