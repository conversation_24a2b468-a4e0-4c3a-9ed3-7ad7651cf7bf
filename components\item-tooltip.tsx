"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface ItemTooltipProps {
  itemId: string
  children: React.ReactNode
}

interface ItemDetail {
  name: string
  type: string
  level: number
  description: string
  stats: {
    attack?: number
    str?: number
    dex?: number
    int?: number
    luk?: number
  }
}

export function ItemTooltip({ itemId, children }: ItemTooltipProps) {
  const [itemDetail, setItemDetail] = useState<ItemDetail | null>(null)
  const [loading, setLoading] = useState(false)

  const fetchItemDetail = async () => {
    if (loading || itemDetail) return

    setLoading(true)
    try {
      // 尝试从API获取数据
      const response = await fetch(`/api/items/${itemId}`)
      if (response.ok) {
        const data = await response.json()
        setItemDetail(data)
      } else {
        // 如果API失败，使用模拟数据
        setItemDetail({
          name: itemId.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
          type: "装备",
          level: 200,
          description: "CMS-216版本新增道具",
          stats: {
            attack: 150,
            str: 50,
            dex: 30,
          },
        })
      }
    } catch (error) {
      console.error("Failed to fetch item detail:", error)
      // 使用模拟数据作为后备
      setItemDetail({
        name: itemId.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
        type: "装备",
        level: 200,
        description: "CMS-216版本新增道具",
        stats: {
          attack: 150,
          str: 50,
          dex: 30,
        },
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild onMouseEnter={fetchItemDetail}>
          {children}
        </TooltipTrigger>
        <TooltipContent side="right" className="max-w-xs">
          {loading ? (
            <div className="p-2">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-1"></div>
                <div className="h-3 bg-gray-200 rounded"></div>
              </div>
            </div>
          ) : itemDetail ? (
            <div className="p-2 space-y-2">
              <div className="font-semibold text-blue-600">{itemDetail.name}</div>
              <div className="text-sm text-gray-600">
                <div>类型: {itemDetail.type}</div>
                <div>等级: {itemDetail.level}</div>
              </div>
              <div className="text-xs text-gray-500">{itemDetail.description}</div>
              {itemDetail.stats && (
                <div className="text-xs space-y-1 border-t pt-2">
                  {itemDetail.stats.attack && <div>攻击力: +{itemDetail.stats.attack}</div>}
                  {itemDetail.stats.str && <div>STR: +{itemDetail.stats.str}</div>}
                  {itemDetail.stats.dex && <div>DEX: +{itemDetail.stats.dex}</div>}
                  {itemDetail.stats.int && <div>INT: +{itemDetail.stats.int}</div>}
                  {itemDetail.stats.luk && <div>LUK: +{itemDetail.stats.luk}</div>}
                </div>
              )}
            </div>
          ) : (
            <div className="p-2 text-sm text-gray-500">点击查看详情</div>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
