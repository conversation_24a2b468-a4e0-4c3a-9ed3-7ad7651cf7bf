// scripts/gen-json-cms-216.js

const fs = require('fs')
const path = require('path')

function formatName(id) {
    return id.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
}

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min
}

// 假设你知道要生成哪些 ID
const itemIds = ['a001', 'dragon_sword', 'legendary_staff']

const items = itemIds.map((id) => ({
    id,
    name: formatName(id),
    type: '装备',
    level: getRandomInt(150, 250),
    description: 'CMS-216版本新增道具，具有强大的属性加成',
    stats: {
        attack: getRandomInt(100, 300),
        str: getRandomInt(30, 130),
        dex: getRandomInt(30, 130),
        int: getRandomInt(30, 130),
        luk: getRandomInt(30, 130),
    },
}))

// 保存为 public/items.json
const outputPath = path.join(process.cwd(), '../public/data/items', '1.json')
fs.writeFileSync(outputPath, JSON.stringify(items, null, 2))
console.log('✅ Items data generated:', outputPath)