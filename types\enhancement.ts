// 装备强化模拟器相关类型定义

// 强化类型
export type EnhancementType = 'starforce' | 'potential' | 'bonusstat'

// 装备信息
export interface Equipment {
  itemId: string | null
  name: string
  type: string
  category: string
  imageUrl?: string
}

// 强化概率数据
export interface EnhancementProbability {
  success: number        // 成功概率 (%)
  failure: number        // 失败概率 (%)
  major_failure: number  // 大失败概率 (%)
  failure_drop: number   // 等级下降概率 (%)
}

// 强化费用数据
export type EnhancementCosts = Record<number, number>

// 强化概率表
export type ProbabilityTable = Record<number, EnhancementProbability>

// 强化结果类型
export type EnhancementResultType = 'success' | 'failed' | 'major_failure'

// 强化结果
export interface EnhancementResult {
  type: EnhancementResultType
  message: string
  level: number
  previousLevel?: number
}

// 装备属性
export interface EquipmentStats {
  STR: number
  DEX: number
  INT: number
  LUK: number
  MaxHP: number
  DEF: number
}

// 动画效果类型
export type EffectType = 'standby' | 'progress' | 'success' | 'failed'

// 动画状态
export interface AnimationState {
  isActive: boolean
  currentFrame: number
  effectType: EffectType
  isLooping: boolean
}

// 迷你游戏状态
export interface MinigameState {
  isActive: boolean
  gaugePosition: number
  direction: number
  isAnimating: boolean
}

// 加载状态
export interface LoadingState {
  isLoading: boolean
  progress: number
  status: string
  totalResources: number
  loadedResources: number
}

// 物品选择器状态
export interface ItemSelectorState {
  isVisible: boolean
  selectedCategory: number | null
  searchTerm: string
  filteredItems: ItemListEntry[]
  hoveredItem: ItemListEntry | null
}

// 模拟器状态
export interface SimulatorState {
  currentTab: EnhancementType
  equipLevel: number
  equipMaxLevel: number
  enhancing: boolean
  currentEquip: Equipment
  animation: AnimationState
  minigame: MinigameState
  starcatchEnabled: boolean
  preventEnabled: boolean
  loading: LoadingState
  itemSelector: ItemSelectorState
  itemDatabase: ItemDatabase
  itemList: ItemListEntry[]
}

// 完整的装备数据库项目接口
export interface ItemDatabaseEntry {
  itemId: number
  itemCategory: number
  itemName: string
  totalCount: number
  minBidPriceWei: string
  maxBidPriceWei: string
  imageUrl: string
  detailInfo: {
    metadata: {
      category: {
        categoryNo: number
        label: string
        tier0: { label: string; code: string }
        tier1: { label: string; code: string }
        tier2: { label: string; code: string }
        tier3: { label: string; code: string }
      }
      common: {
        itemName: string
        itemId: number
        desc: string
        isCashItem: boolean
        isMintable: boolean
        isSbt: boolean
        isOnly: boolean
        isOnlyEquip: boolean
        isBossReward: boolean
        blockUpgradeStarforce: boolean
        blockUpgradePotential: boolean
        blockUpgradeExtraOption: boolean
        enableStarforce: boolean
        maxStarforce: number
        slotMax: number
        ridingSkill: number
        setItemId: number
      }
      required: {
        level: number
        str: number
        dex: number
        int: number
        luk: number
        job: {
          classCode: number
          className: string
          jobCode: number
          jobName: string
        }
      }
      stats: {
        maxChuc: number
        attackSpeed: number
        knockback: number
        str: number
        dex: number
        int: number
        luk: number
        maxHp: number
        maxMp: number
        maxHpr: number
        maxMpr: number
        pad: number
        pdd: number
        mad: number
        mdd: number
        bdr: number
        imdr: number
        damr: number
        statr: number
        acc: number
        eva: number
        speed: number
        craft: number
        jump: number
        recoveryHp: number
        recoveryMp: number
        senseExp: number
        willExp: number
        insightExp: number
        charismaExp: number
        charmExp: number
        craftExp: number
      }
      image: {
        iconImageUrl: string
        sampleImageUrl: string
      }
    }
  }
  hasDetails: boolean
  detailStatus: string
}

// 简化的装备项目接口（用于选择器）
export interface ItemListEntry {
  itemId: number
  name: string
  category: number
  imageUrl: string
  level: number  // 装备等级
  detailInfo: ItemDatabaseEntry['detailInfo']
}

// 装备数据库
export interface ItemDatabase {
  [itemId: number]: ItemListEntry
}

// 装备类型映射
export interface CategoryMapping {
  [key: number]: string
}

// 测试装备项目
export interface TestEquipItem {
  id: string
  name: string
  type: string
  category?: string
}

// 组件 Props 类型
export interface EnhancementSimulatorProps {
  className?: string
}

export interface TabSelectorProps {
  currentTab: EnhancementType
  onTabChange: (tab: EnhancementType) => void
  disabled?: boolean
}

export interface EquipmentSlotProps {
  equipment: Equipment
  enhancementType: EnhancementType
  level: number
  onEquipmentSelect: () => void
  onEquipmentClear: () => void
  isEnhancing: boolean
}

export interface InfoPanelProps {
  enhancementType: EnhancementType
  level: number
  probabilities: EnhancementProbability
  cost: number
  stats: {
    current: EquipmentStats
    next: EquipmentStats
  }
  starcatchEnabled: boolean
  preventEnabled: boolean
  onStarcatchChange: (enabled: boolean) => void
  onPreventChange: (enabled: boolean) => void
}

export interface EffectRendererProps {
  animation: AnimationState
  enhancementType: EnhancementType
}

export interface MinigameOverlayProps {
  isActive: boolean
  gaugePosition: number
  onStop: () => void
}

export interface ResultOverlayProps {
  isVisible: boolean
  result: EnhancementResult | null
  onClose: () => void
}

export interface ConfirmDialogProps {
  isVisible: boolean
  message: string
  onConfirm: () => void
  onCancel: () => void
}

export interface LoadingScreenProps {
  isVisible: boolean
  progress: number
  status: string
}

export interface ItemSelectorProps {
  isVisible: boolean
  items: ItemListEntry[]
  selectedCategory: number | null
  currentEnhancementType: EnhancementType
  onItemSelect: (item: ItemListEntry) => void
  onCategoryChange: (category: number | null) => void
  onToggleVisibility: () => void
}

export interface ItemTooltipProps {
  item: ItemListEntry | null
  isVisible: boolean
  position: { x: number; y: number }
}

// 事件处理器类型
export type EnhancementEventHandler = () => void
export type TabChangeHandler = (tab: EnhancementType) => void
export type ToggleHandler = (enabled: boolean) => void

// 工具函数返回类型
export interface CalculationResult {
  result: EnhancementResult
  newLevel: number
  cost: number
}

export interface StatsCalculationResult {
  current: EquipmentStats
  next: EquipmentStats
}

// 常量类型
export interface MaxFrames {
  standby: number
  progress: number
  success: number
  failed: number
}
