'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  EnhancementType,
  Equipment,
  SimulatorState,
  EnhancementResult,
  ItemDatabase,
  ItemListEntry,
  LoadingState,
  ItemSelectorState
} from '@/types/enhancement'
import {
  getEnhancementProbability,
  getEnhancementCost,
  calculateEnhancementResult,
  calculateEquipmentStats,
  calculateMinigameBonus,
  getRandomTestEquip,
  loadItemDatabase,
  getItemInfo,
  preloadImages,
} from '@/lib/enhancement-utils'
import { ResourcePreloader } from '@/lib/resource-preloader'

import TabSelector from './TabSelector'
import EquipmentSlot from './EquipmentSlot'
import InfoPanel from './InfoPanel'
import EffectRenderer from './EffectRenderer'
import MinigameOverlay from './MinigameOverlay'
import ResultOverlay from './ResultOverlay'
import ConfirmDialog from './ConfirmDialog'
import StarProgress from './StarProgress'
import CostPanel from './CostPanel'
import LoadingScreen from './LoadingScreen'
import ItemSelector from './ItemSelector'

const INITIAL_EQUIPMENT: Equipment = {
  itemId: null,
  name: '',
  type: '',
  category: '',
}

const INITIAL_STATE: SimulatorState = {
  currentTab: 'starforce',
  equipLevel: 0,
  equipMaxLevel: 25,
  enhancing: false,
  currentEquip: INITIAL_EQUIPMENT,
  animation: {
    isActive: false,
    currentFrame: 0,
    effectType: 'standby',
    isLooping: true,
  },
  minigame: {
    isActive: false,
    gaugePosition: 0,
    direction: 1,
    isAnimating: false,
  },
  starcatchEnabled: false,
  preventEnabled: false,
  loading: {
    isLoading: true,
    progress: 0,
    status: '初始化中...',
    totalResources: 0,
    loadedResources: 0,
  },
  itemSelector: {
    isVisible: false,
    selectedCategory: null,
    searchTerm: '',
    filteredItems: [],
    hoveredItem: null,
  },
  itemDatabase: {},
  itemList: [],
}

export default function EnhancementSimulator() {
  const [state, setState] = useState<SimulatorState>(INITIAL_STATE)
  const [showConfirm, setShowConfirm] = useState(false)
  const [showResult, setShowResult] = useState(false)
  const [enhancementResult, setEnhancementResult] = useState<EnhancementResult | null>(null)

  // 初始化
  useEffect(() => {
    const init = async () => {
      const preloader = new ResourcePreloader((progress) => {
        setState(prev => ({
          ...prev,
          loading: {
            ...prev.loading,
            progress: progress.percentage,
            status: `加载中... (${progress.loaded}/${progress.total})`,
            loadedResources: progress.loaded,
            totalResources: progress.total,
          }
        }))
      })

      try {
        // 步骤1: 加载装备数据库
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, progress: 10, status: '加载装备数据库...' }
        }))

        const itemData = await preloader.preloadItemDatabase()
        const itemList: ItemListEntry[] = Array.isArray(itemData) ? itemData.map(item => ({
          itemId: item.itemId,
          name: item.itemName,
          category: item.itemCategory,
          imageUrl: item.imageUrl,
          level: item.detailInfo?.metadata?.required?.level || 0,
          detailInfo: item.detailInfo
        })) : []

        const itemDatabase: ItemDatabase = {}
        itemList.forEach(item => {
          itemDatabase[item.itemId] = item
        })

        setState(prev => ({
          ...prev,
          itemList,
          itemDatabase,
          loading: { ...prev.loading, progress: 30, status: '预加载UI资源...' }
        }))

        // 步骤2: 预加载UI图片
        const uiImages = preloader.getUIImageList()
        await preloader.preloadImages(uiImages)

        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, progress: 70, status: '预加载特效资源...' }
        }))

        // 步骤3: 预加载特效图片
        const effectImages = preloader.getEffectImageList()
        await preloader.preloadImages(effectImages)

        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, progress: 90, status: '初始化完成...' }
        }))

        // 步骤4: 自动选择装备
        setTimeout(() => {
          handleEquipmentSelect()
          setState(prev => ({
            ...prev,
            loading: {
              ...prev.loading,
              progress: 100,
              status: '加载完成！',
              isLoading: false,
            }
          }))
        }, 500)

      } catch (error) {
        console.error('初始化失败:', error)
        setState(prev => ({
          ...prev,
          loading: {
            ...prev.loading,
            progress: 0,
            status: '加载失败，请刷新页面重试',
            isLoading: false,
          }
        }))
      }
    }

    init()
  }, [])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return

      switch (e.key) {
        case '1':
          handleTabChange('starforce')
          break
        case '2':
          handleTabChange('potential')
          break
        case '3':
          handleTabChange('bonusstat')
          break
        case 'Enter':
          if (!state.enhancing) {
            handleEnhanceStart()
          }
          break
        case 'Escape':
          handleEnhanceCancel()
          break
        case 'e':
        case 'E':
          handleEquipmentSelect()
          break
        case 'r':
        case 'R':
          handleEquipmentClear()
          break
        case ' ':
          if (state.minigame.isActive) {
            e.preventDefault()
            handleMinigameStop()
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [state.enhancing, state.minigame.isActive])

  // 标签页切换
  const handleTabChange = useCallback((tab: EnhancementType) => {
    setState(prev => ({
      ...prev,
      currentTab: tab,
      animation: {
        ...prev.animation,
        isActive: false,
      },
    }))
    
    // 延迟启动待机特效
    if (tab === 'starforce') {
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          animation: {
            isActive: true,
            currentFrame: 0,
            effectType: 'standby',
            isLooping: true,
          },
        }))
      }, 300)
    }
  }, [])

  // 装备选择
  const handleEquipmentSelect = useCallback(() => {
    if (state.itemList.length === 0) return

    // 从支持当前强化类型的装备中随机选择
    const { filterItemsByEnhancementType } = require('@/lib/item-filter-utils')
    const compatibleItems = filterItemsByEnhancementType(state.itemList, state.currentTab)

    if (compatibleItems.length === 0) return

    const randomItem = compatibleItems[Math.floor(Math.random() * compatibleItems.length)]

    setState(prev => ({
      ...prev,
      currentEquip: {
        itemId: randomItem.itemId.toString(),
        name: randomItem.name,
        type: randomItem.detailInfo?.metadata?.category?.tier3?.label || 'unknown',
        category: randomItem.category.toString(),
        imageUrl: randomItem.imageUrl,
      },
    }))
  }, [state.itemList, state.currentTab])

  // 从选择器选择装备
  const handleItemSelect = useCallback((item: ItemListEntry) => {
    setState(prev => ({
      ...prev,
      currentEquip: {
        itemId: item.itemId.toString(),
        name: item.name,
        type: item.detailInfo?.metadata?.category?.tier3?.label || 'unknown',
        category: item.category.toString(),
        imageUrl: item.imageUrl,
      },
      itemSelector: {
        ...prev.itemSelector,
        isVisible: false,
      },
    }))
  }, [])

  // 切换物品选择器显示
  const handleToggleItemSelector = useCallback(() => {
    setState(prev => ({
      ...prev,
      itemSelector: {
        ...prev.itemSelector,
        isVisible: !prev.itemSelector.isVisible,
      },
    }))
  }, [])

  // 物品选择器类别变化
  const handleCategoryChange = useCallback((category: number | null) => {
    setState(prev => ({
      ...prev,
      itemSelector: {
        ...prev.itemSelector,
        selectedCategory: category,
      },
    }))
  }, [])

  // 装备清除
  const handleEquipmentClear = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentEquip: INITIAL_EQUIPMENT,
      equipLevel: 0,
    }))
  }, [])

  // 开始强化
  const handleEnhanceStart = useCallback(() => {
    if (state.enhancing || !state.currentEquip.itemId) return
    setShowConfirm(true)
  }, [state.enhancing, state.currentEquip.itemId])

  // 确认强化
  const handleEnhanceConfirm = useCallback(() => {
    setShowConfirm(false)
    
    setState(prev => ({
      ...prev,
      enhancing: true,
      animation: {
        isActive: false,
        currentFrame: 0,
        effectType: 'progress',
        isLooping: true,
      },
    }))

    // 检查是否需要迷你游戏
    if (state.currentTab === 'starforce' && state.equipLevel >= 10) {
      // 启动迷你游戏
      setState(prev => ({
        ...prev,
        minigame: {
          isActive: true,
          gaugePosition: 0,
          direction: 1,
          isAnimating: true,
        },
      }))
      
      // 3秒后自动停止
      setTimeout(() => {
        if (state.minigame.isActive) {
          handleMinigameStop()
        }
      }, 3000)
    } else {
      // 直接进行强化
      setTimeout(() => {
        performEnhancement()
      }, 2000)
    }
  }, [state.currentTab, state.equipLevel, state.minigame.isActive])

  // 取消强化
  const handleEnhanceCancel = useCallback(() => {
    setShowConfirm(false)
    setState(prev => ({
      ...prev,
      enhancing: false,
      minigame: {
        ...prev.minigame,
        isActive: false,
        isAnimating: false,
      },
      animation: {
        isActive: prev.currentTab === 'starforce',
        currentFrame: 0,
        effectType: 'standby',
        isLooping: true,
      },
    }))
  }, [])

  // 迷你游戏停止
  const handleMinigameStop = useCallback(() => {
    const bonus = calculateMinigameBonus(state.minigame.gaugePosition)

    setState(prev => ({
      ...prev,
      minigame: {
        ...prev.minigame,
        isActive: false,
        isAnimating: false,
      },
    }))

    setTimeout(() => {
      performEnhancement(bonus)
    }, 500)
  }, [state.minigame.gaugePosition, state.currentTab, state.equipLevel, state.starcatchEnabled, state.preventEnabled])

  // 执行强化
  const performEnhancement = useCallback((bonus: number = 0) => {
    const result = calculateEnhancementResult(
      state.currentTab,
      state.equipLevel,
      state.starcatchEnabled,
      state.preventEnabled,
      bonus
    )

    // 播放结果特效
    setState(prev => ({
      ...prev,
      animation: {
        isActive: true,
        currentFrame: 0,
        effectType: result.type === 'success' ? 'success' : 'failed',
        isLooping: false,
      },
      equipLevel: result.level,
    }))

    setTimeout(() => {
      setEnhancementResult(result)
      setShowResult(true)

      setState(prev => ({
        ...prev,
        enhancing: false,
      }))

      // 2秒后恢复待机特效
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          animation: {
            isActive: prev.currentTab === 'starforce',
            currentFrame: 0,
            effectType: 'standby',
            isLooping: true,
          },
        }))
      }, 2000)
    }, 1500)
  }, [state.currentTab, state.equipLevel, state.starcatchEnabled, state.preventEnabled])

  // 关闭结果对话框
  const handleResultClose = useCallback(() => {
    setShowResult(false)
    setEnhancementResult(null)
  }, [])

  // 镇护选项切换
  const handleStarcatchChange = useCallback((enabled: boolean) => {
    setState(prev => ({ ...prev, starcatchEnabled: enabled }))
  }, [])

  const handlePreventChange = useCallback((enabled: boolean) => {
    setState(prev => ({ ...prev, preventEnabled: enabled }))
  }, [])

  // 计算当前概率和费用
  const currentProbabilities = getEnhancementProbability(
    state.currentTab,
    state.equipLevel,
    state.starcatchEnabled,
    state.preventEnabled
  )
  
  const currentCost = getEnhancementCost(state.currentTab, state.equipLevel)
  
  const currentStats = calculateEquipmentStats(state.equipLevel)
  const nextStats = calculateEquipmentStats(state.equipLevel + 1)

  return (
    <div className="relative">
      {/* 加载屏幕 */}
      <LoadingScreen
        isVisible={state.loading.isLoading}
        progress={state.loading.progress}
        status={state.loading.status}
      />

      {/* 主界面容器 */}
      <div
        className="relative mx-auto bg-cover bg-no-repeat"
        style={{
          width: '507px',
          height: '712px',
          backgroundImage: 'url(/images/UIEquipEnchant/Main/Background.png)',
        }}
      >
        {/* 物品选择器 */}
        <ItemSelector
          isVisible={state.itemSelector.isVisible}
          items={state.itemList}
          selectedCategory={state.itemSelector.selectedCategory}
          currentEnhancementType={state.currentTab}
          onItemSelect={handleItemSelect}
          onCategoryChange={handleCategoryChange}
          onToggleVisibility={handleToggleItemSelector}
        />
        {/* 标签页选择器 */}
        <TabSelector
          currentTab={state.currentTab}
          onTabChange={handleTabChange}
          disabled={state.enhancing}
        />
        
        {/* 装备槽 */}
        <EquipmentSlot
          equipment={state.currentEquip}
          enhancementType={state.currentTab}
          level={state.equipLevel}
          onEquipmentSelect={handleEquipmentSelect}
          onEquipmentClear={handleEquipmentClear}
          isEnhancing={state.enhancing}
        />

        {/* 物品选择器切换按钮 */}
        <button
          className="absolute z-[5] w-8 h-8 bg-blue-600/80 hover:bg-blue-500/90 border border-blue-400/50 rounded text-white text-sm font-bold transition-all duration-200 hover:scale-105"
          style={{
            top: '120px',
            left: '10px',
          }}
          onClick={handleToggleItemSelector}
          title="打开装备选择器"
        >
          📦
        </button>
        
        {/* 特效渲染器 */}
        <EffectRenderer
          animation={state.animation}
          enhancementType={state.currentTab}
        />
        
        {/* 信息面板 */}
        <InfoPanel
          enhancementType={state.currentTab}
          level={state.equipLevel}
          probabilities={currentProbabilities}
          cost={currentCost}
          stats={{ current: currentStats, next: nextStats }}
          starcatchEnabled={state.starcatchEnabled}
          preventEnabled={state.preventEnabled}
          onStarcatchChange={handleStarcatchChange}
          onPreventChange={handlePreventChange}
        />

        {/* 星级进度显示 */}
        {state.currentTab === 'starforce' && (
          <StarProgress
            currentLevel={state.equipLevel}
            maxLevel={state.equipMaxLevel}
          />
        )}

        {/* 费用面板 */}
        <CostPanel cost={currentCost} />

        {/* Z-10: 主要按钮 - 按照原版位置 */}
        <button
          className="absolute border-none cursor-pointer z-[10] transition-all duration-200 w-[136px] h-[38px] bg-cover bg-no-repeat hover:-translate-y-px disabled:cursor-not-allowed disabled:transform-none"
          style={{
            bottom: '35px',
            left: '114px',
            backgroundImage: state.enhancing || !state.currentEquip.itemId || state.equipLevel >= state.equipMaxLevel
              ? 'url(/images/UIEquipEnchant/Main/button_Enhance/disabled/0.png)'
              : 'url(/images/UIEquipEnchant/Main/button_Enhance/normal/0.png)',
            backgroundSize: '136px 38px',
          }}
          onClick={handleEnhanceStart}
          disabled={state.enhancing || !state.currentEquip.itemId || state.equipLevel >= state.equipMaxLevel}
          onMouseEnter={(e) => {
            if (!state.enhancing && state.currentEquip.itemId && state.equipLevel < state.equipMaxLevel) {
              e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Enhance/mouseOver/0.png)'
            }
          }}
          onMouseLeave={(e) => {
            if (!state.enhancing && state.currentEquip.itemId && state.equipLevel < state.equipMaxLevel) {
              e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Enhance/normal/0.png)'
            }
          }}
          onMouseDown={(e) => {
            if (!state.enhancing && state.currentEquip.itemId && state.equipLevel < state.equipMaxLevel) {
              e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Enhance/pressed/0.png)'
              e.currentTarget.style.transform = 'translateY(0)'
            }
          }}
          onMouseUp={(e) => {
            if (!state.enhancing && state.currentEquip.itemId && state.equipLevel < state.equipMaxLevel) {
              e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Enhance/mouseOver/0.png)'
              e.currentTarget.style.transform = 'translateY(-1px)'
            }
          }}
        />

        {/* 取消按钮 */}
        <button
          className="absolute border-none cursor-pointer z-[10] transition-all duration-200 w-[136px] h-[38px] bg-cover bg-no-repeat hover:-translate-y-px"
          style={{
            bottom: '35px',
            left: '258px',
            backgroundImage: 'url(/images/UIEquipEnchant/Main/button_Cancel/normal/0.png)',
            backgroundSize: '136px 38px',
          }}
          onClick={handleEnhanceCancel}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Cancel/mouseOver/0.png)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Cancel/normal/0.png)'
          }}
          onMouseDown={(e) => {
            e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Cancel/pressed/0.png)'
            e.currentTarget.style.transform = 'translateY(0)'
          }}
          onMouseUp={(e) => {
            e.currentTarget.style.backgroundImage = 'url(/images/UIEquipEnchant/Main/button_Cancel/mouseOver/0.png)'
            e.currentTarget.style.transform = 'translateY(-1px)'
          }}
        />
      </div>
      
      {/* 迷你游戏覆盖层 */}
      <MinigameOverlay
        isActive={state.minigame.isActive}
        gaugePosition={state.minigame.gaugePosition}
        onStop={handleMinigameStop}
      />
      
      {/* 确认对话框 */}
      <ConfirmDialog
        isVisible={showConfirm}
        message="确定要强化这个装备吗？"
        onConfirm={handleEnhanceConfirm}
        onCancel={handleEnhanceCancel}
      />
      
      {/* 结果对话框 */}
      <ResultOverlay
        isVisible={showResult}
        result={enhancementResult}
        onClose={handleResultClose}
      />
    </div>
  )
}
