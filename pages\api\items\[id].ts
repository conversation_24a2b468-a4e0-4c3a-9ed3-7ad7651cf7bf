// pages/api/items/[id].ts

import type { NextApiRequest, NextApiResponse } from 'next'

/**
 * 根据 id 生成道具名称
 */
function formatName(id: string): string {
  return id.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
}

/**
 * 随机生成一个数值在 min 到 max 之间的整数
 */
function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 模拟返回道具详情
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid ID' })
  }

  // 构造 mock 数据
  const item = {
    name: formatName(id),
    type: '装备',
    level: getRandomInt(150, 250),
    description: 'CMS-216版本新增道具，具有强大的属性加成222222',
    stats: {
      attack: getRandomInt(100, 300),
      str: getRandomInt(30, 130),
      dex: getRandomInt(30, 130),
      int: getRandomInt(30, 130),
      luk: getRandomInt(30, 130),
    },
    rarity: '传说',
    version: 'CMS-216',
  }

  res.status(200).json(item)
}