# 1
请阅读项目根目录下的 `README.md` 和 `DEVELOPER_GUIDE.md` 文档，并分析现有的代码结构。然后基于冒险岛情报站项目（当前使用 Next.js + SSG 配置），设计并输出一个详细的前后端分离架构实施方案。

**技术栈要求：**
- 前端：Next.js 14+ (保持现有 SSG 配置)
- 后端：FastAPI (Python)
- 数据库：PostgreSQL 16 (端口 5433，数据库名：mxd_info_db)
- 邮件服务：Resend API
- 设备指纹：FingerprintJS 免费版
- 认证：JWT Token 管理
- 缓存：Redis (如果需要)

**核心功能实现要求（按优先级排序）：**

1. **项目结构重组和配置**
    - 提供完整的前后端分离目录结构
    - 设计环境变量配置文件（.env.example 和 .env.local）
    - 配置 FastAPI 后端项目结构
    - 设置跨域 CORS 配置

2. **数据库设计和迁移**
    - 设计完整的数据库表结构（用户、会员等级、虚拟货币、交易记录等）
    - 所有表主键使用自增长 ID
    - 提供数据库初始化脚本和迁移方案
    - 兼容现有数据结构

3. **用户认证系统**
    - 实现用户注册页面（/register）- 仅需用户名和密码
    - 实现登录页面（/login）
    - 集成 Resend 邮件服务实现邮箱验证流程
    - 实现密码重置功能（/reset-password）
    - JWT token 管理和自动刷新机制
    - 认证中间件和路由保护

4. **三级会员系统和权限控制**
    - 实现五级权限模型：游客、注册用户、黄金用户、钻石用户、管理员
    - 集成 FingerprintJS 免费版进行设备指纹识别（游客用户绑定设备）
    - 权限检查中间件和组件级权限控制
    - 用户仪表板页面（/dashboard）显示会员状态和权限

5. **虚拟货币系统（欢乐豆）**
    - "欢乐豆"虚拟货币的数据模型和业务逻辑
    - 余额查询、消费扣除、充值记录的 API 接口
    - 交易安全机制：余额验证、交易日志、防重复提交
    - 防刷币检测和异常交易监控
    - 交易记录页面和余额管理界面
    - 界面上的名称都是欢乐豆。不要出现虚拟货币。

6. **RESTful API 接口系统**
    - 装备强化相关 API：
        - `/api/enhancement/starforce` - 星力强化
        - `/api/enhancement/potential` - 潜能强化
        - `/api/enhancement/additional` - 附加属性强化
    - 用户管理 API：
        - `/api/users/profile` - 用户资料
        - `/api/users/membership` - 会员信息
    - 虚拟货币 API：
        - `/api/currency/balance` - 余额查询
        - `/api/currency/consume` - 消费扣除
        - `/api/currency/transactions` - 交易记录
    - 完整的错误处理、输入验证、速率限制中间件

7. **你可以按你的最佳实践修改我上面的内容。我希望是最加的方案**

**页面访问权限和渲染策略：**
- **保持 SSG 渲染的页面：**
    - 首页（/）
    - 所有工具页面（/tools/*）
    - 道具展示页面（/cms-216）
    - 认证相关页面：登录（/login）、注册（/register）、密码重置（/reset-password）

- **需要登录验证的页面：**
    - 用户仪表板（/dashboard）
    - 个人资料（/profile）
    - 交易记录（/transactions）
    - 设置页面（/settings）

- **其他所有页面均可匿名访问**

**输出要求：**
生成一个完整的前后端分离架构实施方案文档，保存为 Markdown 格式文件到项目根目录，文件名为 `FRONTEND_BACKEND_SEPARATION_PLAN.md`。文档应包含：
- 详细的技术架构图
- 完整的目录结构
- 数据库设计方案
- API 接口规范
- 部署和配置指南
- 迁移步骤和注意事项

请确保方案与现有项目结构兼容，并考虑渐进式迁移策略。


# 2
纠正`FRONTEND_BACKEND_SEPARATION_PLAN.md`几点：
1、生产环境使用操作系统Rocky9.5
2、不使用docker技术。
3、当前项目，只做了纯前端页面开发。不涉及库。所以没有数据库迁移。


# 3
现在可以开发
按`FRONTEND_BACKEND_SEPARATION_PLAN.md`文档的按渐进式迁移方案开始开发。
完成 阶段一：基础架构搭建 
要求，不破坏现有的功能。

# 3.1
根据 `FRONTEND_BACKEND_SEPARATION_PLAN.md` 文档中的渐进式迁移方案，开始实施阶段一：基础架构搭建。

**具体任务要求：**

1. **项目结构重组**
   - 创建新的前后端分离目录结构（frontend/ 和 backend/ 目录）
   - 将现有的 Next.js 项目内容迁移到 frontend/ 目录
   - 保持所有现有文件的相对路径和功能不变

2. **FastAPI 后端框架搭建**
   - 在 backend/ 目录创建完整的 FastAPI 项目结构
   - 实现基础的应用入口 (main.py)
   - 配置核心模块：config.py, database.py, deps.py
   - 创建基础的路由结构和中间件

3. **数据库环境配置**
   - 配置 PostgreSQL 16 连接（端口 5433，数据库名：mxd_info_db）
   - 设置 Alembic 数据库迁移工具
   - 创建所有必要的数据库表结构（9个核心表）
   - 初始化基础数据（会员等级等）

4. **Redis 缓存配置**
   - 配置 Redis 连接和基础缓存功能
   - 实现缓存工具类和中间件

5. **CORS 和基础中间件配置**
   - 配置跨域请求处理
   - 实现基础的错误处理中间件
   - 配置请求日志和响应格式

6. **开发环境配置**
   - 创建环境变量配置文件 (.env.example 和 .env)
   - 配置前后端的开发启动脚本
   - 确保前端能成功调用后端的健康检查接口

**验收标准：**
- [ ] 后端 FastAPI 服务能正常启动（http://localhost:8000）
- [ ] 数据库连接正常，所有表创建成功
- [ ] Redis 连接正常
- [ ] 前端项目在新目录结构下正常运行，所有现有功能保持不变
- [ ] 前端能成功调用后端的 /health 接口
- [ ] 开发环境可以同时启动前后端服务

**重要约束：**
- 绝对不能破坏现有的装备强化模拟器、星之力强化模拟器功能
- 现有的前端代码，可以按`FRONTEND_BACKEND_SEPARATION_PLAN.md` 文档的设计放到/frontend

请按照文档中的技术栈要求（Next.js 14+, FastAPI, PostgreSQL 16, Redis）和目录结构进行实施。


# 3
根据 `FRONTEND_BACKEND_SEPARATION_PLAN.md` 文档中的渐进式迁移方案，开始实施阶段二：用户认证系统。
PostgreSQL数据库配置入下
    host: 'localhost',
    port: 5433,
    database: 'mxd_info_db',
    user: 'postgres',
    password: 'postgres',

## 3.1
根据 `FRONTEND_BACKEND_SEPARATION_PLAN.md` 文档中的渐进式迁移方案，实施阶段二：用户认证系统。请按照以下具体要求执行：

**数据库配置信息：**
- Host: localhost
- Port: 5433
- Database: mxd_info_db
- User: postgres
- Password: postgres

**实施要求：**
1. **后端实现（FastAPI）：**
   - 创建用户认证相关的数据库模型（User, UserSession, EmailVerification 等）
   - 使用 Alembic 生成并执行数据库迁移
   - 实现 JWT 认证机制（access token + refresh token）
   - 完善 `/api/v1/auth/` 路由下的所有端点：
      - POST /register（用户注册）
      - POST /login（用户登录）
      - POST /refresh（刷新令牌）
      - POST /logout（用户登出）
      - POST /verify-email（邮箱验证）
   - 实现密码哈希和验证
   - 添加认证中间件和依赖注入
   - 集成邮件服务（用于邮箱验证）
   - 邮箱+密码注册，需进行邮箱验证（发送验证码或确认链接）。登录使用邮箱和密码。可选参数是用户名。
   - 用户名+密码注册，无需邮箱验证，可直接注册）。登录使用用户名和密码
   - 检测到邮箱已注册但未激活时，提示用户"该邮箱已注册但未激活，请查收验证邮件或重新发送激活邮
     件"，并提供重新发送激活邮件的选项

2. **前端实现（Next.js）：**
   - 更新 API 客户端以支持认证相关请求
   - 创建登录/注册页面组件
   - 实现 JWT token 的本地存储和管理
   - 添加认证状态管理（Context 或 Zustand）
   - 实现路由保护（需要登录的页面）
   - 创建用户信息显示组件

3. **测试验证：**
   - 确保数据库连接正常
   - 测试所有认证 API 端点
   - 验证前后端认证流程完整性
   - 测试 JWT token 的生成、验证和刷新机制

4. **安全考虑：**
   - 实现密码强度验证
   - 添加登录失败次数限制
   - 确保敏感信息不在日志中暴露
   - 实现 CSRF 保护

请按照现有的项目结构和代码风格进行开发，确保与已搭建的前后端分离架构兼容。

