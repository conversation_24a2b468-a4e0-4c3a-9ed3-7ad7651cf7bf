# 1
请阅读项目根目录下的 `README.md` 和 `DEVELOPER_GUIDE.md` 文档，并分析现有的代码结构。然后基于冒险岛情报站项目（当前使用 Next.js + SSG 配置），设计并输出一个详细的前后端分离架构实施方案。

**技术栈要求：**
- 前端：Next.js 14+ (保持现有 SSG 配置)
- 后端：FastAPI (Python)
- 数据库：PostgreSQL 16 (端口 5433，数据库名：mxd_info_db)
- 邮件服务：Resend API
- 设备指纹：FingerprintJS 免费版
- 认证：JWT Token 管理
- 缓存：Redis (如果需要)

**核心功能实现要求（按优先级排序）：**

1. **项目结构重组和配置**
    - 提供完整的前后端分离目录结构
    - 设计环境变量配置文件（.env.example 和 .env.local）
    - 配置 FastAPI 后端项目结构
    - 设置跨域 CORS 配置

2. **数据库设计和迁移**
    - 设计完整的数据库表结构（用户、会员等级、虚拟货币、交易记录等）
    - 所有表主键使用自增长 ID
    - 提供数据库初始化脚本和迁移方案
    - 兼容现有数据结构

3. **用户认证系统**
    - 实现用户注册页面（/register）- 仅需用户名和密码
    - 实现登录页面（/login）
    - 集成 Resend 邮件服务实现邮箱验证流程
    - 实现密码重置功能（/reset-password）
    - JWT token 管理和自动刷新机制
    - 认证中间件和路由保护

4. **三级会员系统和权限控制**
    - 实现五级权限模型：游客、注册用户、黄金用户、钻石用户、管理员
    - 集成 FingerprintJS 免费版进行设备指纹识别（游客用户绑定设备）
    - 权限检查中间件和组件级权限控制
    - 用户仪表板页面（/dashboard）显示会员状态和权限

5. **虚拟货币系统（欢乐豆）**
    - "欢乐豆"虚拟货币的数据模型和业务逻辑
    - 余额查询、消费扣除、充值记录的 API 接口
    - 交易安全机制：余额验证、交易日志、防重复提交
    - 防刷币检测和异常交易监控
    - 交易记录页面和余额管理界面
    - 界面上的名称都是欢乐豆。不要出现虚拟货币。

6. **RESTful API 接口系统**
    - 装备强化相关 API：
        - `/api/enhancement/starforce` - 星力强化
        - `/api/enhancement/potential` - 潜能强化
        - `/api/enhancement/additional` - 附加属性强化
    - 用户管理 API：
        - `/api/users/profile` - 用户资料
        - `/api/users/membership` - 会员信息
    - 虚拟货币 API：
        - `/api/currency/balance` - 余额查询
        - `/api/currency/consume` - 消费扣除
        - `/api/currency/transactions` - 交易记录
    - 完整的错误处理、输入验证、速率限制中间件

7. **你可以按你的最佳实践修改我上面的内容。我希望是最加的方案**

**页面访问权限和渲染策略：**
- **保持 SSG 渲染的页面：**
    - 首页（/）
    - 所有工具页面（/tools/*）
    - 道具展示页面（/cms-216）
    - 认证相关页面：登录（/login）、注册（/register）、密码重置（/reset-password）

- **需要登录验证的页面：**
    - 用户仪表板（/dashboard）
    - 个人资料（/profile）
    - 交易记录（/transactions）
    - 设置页面（/settings）

- **其他所有页面均可匿名访问**

**输出要求：**
生成一个完整的前后端分离架构实施方案文档，保存为 Markdown 格式文件到项目根目录，文件名为 `FRONTEND_BACKEND_SEPARATION_PLAN.md`。文档应包含：
- 详细的技术架构图
- 完整的目录结构
- 数据库设计方案
- API 接口规范
- 部署和配置指南
- 迁移步骤和注意事项

请确保方案与现有项目结构兼容，并考虑渐进式迁移策略。


# 2
纠正`FRONTEND_BACKEND_SEPARATION_PLAN.md`几点：
1、生产环境使用操作系统Rocky9.5
2、不使用docker技术。
3、当前项目，只做了纯前端页面开发。不涉及库。所以没有数据库迁移。


按`FRONTEND_BACKEND_SEPARATION_PLAN.md`文档的按渐进式迁移方案开始开发。



