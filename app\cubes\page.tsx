"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

export default function CubesPage() {
  const [equipmentType, setEquipmentType] = useState("weapon")
  const [cubeType, setCubeType] = useState("red")
  const [targetLines, setTargetLines] = useState<string[]>([])
  const [simulationResults, setSimulationResults] = useState<any>(null)

  const cubeTypes = {
    red: { name: "红魔方", cost: 12000000, rates: { legendary: 5, unique: 15, epic: 30 } },
    black: { name: "黑魔方", cost: 22000000, rates: { legendary: 10, unique: 20, epic: 35 } },
    bonus: { name: "附加魔方", cost: 5000000, rates: { legendary: 3, unique: 12, epic: 25 } },
  }

  const potentialLines = {
    weapon: [
      "攻击力 +12%",
      "攻击力 +9%",
      "总伤害 +12%",
      "总伤害 +9%",
      "BOSS伤害 +40%",
      "BOSS伤害 +35%",
      "BOSS伤害 +30%",
      "无视防御 +40%",
      "无视防御 +35%",
      "无视防御 +30%",
    ],
    armor: [
      "STR +12%",
      "STR +9%",
      "DEX +12%",
      "DEX +9%",
      "INT +12%",
      "INT +9%",
      "LUK +12%",
      "LUK +9%",
      "全属性 +9%",
      "全属性 +6%",
    ],
    accessory: [
      "STR +12%",
      "STR +9%",
      "攻击力 +12%",
      "攻击力 +9%",
      "总伤害 +12%",
      "总伤害 +9%",
      "暴击率 +12%",
      "暴击率 +8%",
    ],
  }

  const runSimulation = () => {
    let attempts = 0
    let totalCost = 0
    let success = false
    const maxAttempts = 10000
    const cube = cubeTypes[cubeType as keyof typeof cubeTypes]

    while (!success && attempts < maxAttempts) {
      attempts++
      totalCost += cube.cost

      // Simulate cube rolling
      const lines: string[] = []
      for (let i = 0; i < 3; i++) {
        const availableLines = potentialLines[equipmentType as keyof typeof potentialLines]
        const randomLine = availableLines[Math.floor(Math.random() * availableLines.length)]
        lines.push(randomLine)
      }

      // Check if target lines are achieved
      if (targetLines.length === 0 || targetLines.every((target) => lines.includes(target))) {
        success = true
      }
    }

    setSimulationResults({
      attempts,
      totalCost,
      success,
      averageCost: Math.round(totalCost / attempts),
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">洗魔方模拟器</h1>
        <Badge variant="secondary">潜能洗练</Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Simulator Settings */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>模拟设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Equipment Type */}
            <div>
              <label className="text-sm font-medium mb-2 block">装备类型</label>
              <Select value={equipmentType} onValueChange={setEquipmentType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weapon">武器</SelectItem>
                  <SelectItem value="armor">防具</SelectItem>
                  <SelectItem value="accessory">饰品</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Cube Type */}
            <div>
              <label className="text-sm font-medium mb-2 block">魔方类型</label>
              <Select value={cubeType} onValueChange={setCubeType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="red">红魔方 (1200万)</SelectItem>
                  <SelectItem value="black">黑魔方 (2200万)</SelectItem>
                  <SelectItem value="bonus">附加魔方 (500万)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Equipment Preview */}
            <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg p-4">
              <div className="text-center mb-4">
                <div className="w-16 h-16 bg-purple-200 rounded-lg mx-auto flex items-center justify-center mb-2">
                  <span className="text-2xl">
                    {equipmentType === "weapon" ? "⚔️" : equipmentType === "armor" ? "🛡️" : "💍"}
                  </span>
                </div>
                <p className="text-sm font-medium">传说级装备</p>
              </div>

              <div className="space-y-2">
                <div className="bg-white/50 rounded p-2 text-sm">
                  <div className="text-purple-600 font-medium">潜能选项:</div>
                  <div className="text-gray-600">STR +9%</div>
                  <div className="text-gray-600">攻击力 +6%</div>
                  <div className="text-gray-600">总伤害 +9%</div>
                </div>
              </div>
            </div>

            {/* Target Lines Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">目标潜能 (可选)</label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {potentialLines[equipmentType as keyof typeof potentialLines].map((line, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Checkbox
                      id={`line-${index}`}
                      checked={targetLines.includes(line)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setTargetLines([...targetLines, line])
                        } else {
                          setTargetLines(targetLines.filter((l) => l !== line))
                        }
                      }}
                    />
                    <label htmlFor={`line-${index}`} className="text-sm">
                      {line}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex space-x-2">
              <Button onClick={runSimulation} className="flex-1">
                开始模拟
              </Button>
              <Button variant="outline" onClick={() => setTargetLines([])}>
                清除目标
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>模拟结果</CardTitle>
          </CardHeader>
          <CardContent>
            {simulationResults ? (
              <div className="space-y-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold mb-2">
                    {simulationResults.success ? "🎯 达成目标!" : "⏰ 模拟完成"}
                  </div>
                  <p className="text-gray-600">{targetLines.length > 0 ? "已达成目标潜能" : "模拟洗练过程"}</p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>使用次数:</span>
                    <span className="font-semibold">{simulationResults.attempts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>总花费:</span>
                    <span className="font-semibold text-purple-600">
                      {simulationResults.totalCost.toLocaleString()} 金币
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>平均每次花费:</span>
                    <span className="font-semibold">{simulationResults.averageCost.toLocaleString()} 金币</span>
                  </div>
                </div>

                {targetLines.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">目标潜能:</h4>
                    <div className="space-y-1">
                      {targetLines.map((line, index) => (
                        <div key={index} className="text-sm bg-purple-50 p-2 rounded">
                          {line}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-4">🎲</div>
                <p>点击"开始模拟"查看结果</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Information */}
      <Card className="bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>魔方说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl mb-2">🔴</div>
              <h3 className="font-semibold mb-2">红魔方</h3>
              <p className="text-sm text-gray-600">基础魔方，价格适中</p>
              <p className="text-sm font-medium">1200万 金币</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl mb-2">⚫</div>
              <h3 className="font-semibold mb-2">黑魔方</h3>
              <p className="text-sm text-gray-600">高级魔方，成功率更高</p>
              <p className="text-sm font-medium">2200万 金币</p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl mb-2">🔵</div>
              <h3 className="font-semibold mb-2">附加魔方</h3>
              <p className="text-sm text-gray-600">附加潜能专用</p>
              <p className="text-sm font-medium">500万 金币</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
