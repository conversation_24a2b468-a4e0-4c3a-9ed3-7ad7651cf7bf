import { type NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params

  // 模拟数据库查询
  // 在实际项目中，这里应该连接到真实的数据库
  const mockItemData = {
    name: id.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
    type: "装备",
    level: Math.floor(Math.random() * 100) + 150,
    description: "CMS-216版本新增道具，具有强大的属性加成",
    stats: {
      attack: Math.floor(Math.random() * 200) + 100,
      str: Math.floor(Math.random() * 100) + 30,
      dex: Math.floor(Math.random() * 100) + 30,
      int: Math.floor(Math.random() * 100) + 30,
      luk: Math.floor(Math.random() * 100) + 30,
    },
    rarity: "传说",
    version: "CMS-216",
  }

  return NextResponse.json(mockItemData)
}
