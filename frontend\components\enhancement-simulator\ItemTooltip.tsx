'use client'

import { ItemTooltipProps } from '@/types/enhancement'
import {
  getItemLevel,
  getItemTypeName,
  isCashItem,
  isBossReward,
  isOnlyEquip,
  getJobRequirement,
  getStatRequirements
} from '@/lib/item-filter-utils'

export default function ItemTooltip({ item, isVisible, position }: ItemTooltipProps) {
  if (!isVisible || !item) return null

  const metadata = item.detailInfo?.metadata
  const common = metadata?.common
  const required = metadata?.required
  const stats = metadata?.stats

  const level = getItemLevel(item)
  const typeName = getItemTypeName(item)
  const jobRequirement = getJobRequirement(item)
  const statRequirements = getStatRequirements(item)
  const cashItem = isCashItem(item)
  const bossReward = isBossReward(item)
  const onlyEquip = isOnlyEquip(item)

  return (
    <div
      className="fixed bg-black border border-yellow-400/70 rounded-lg p-3 z-[1000] pointer-events-none text-xs"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: '280px',
        maxHeight: '400px',
        overflowY: 'auto',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.1)'
      }}
    >
      {/* 装备名称和基本信息 */}
      <div className="mb-3">
        <div className="text-yellow-400 text-sm font-bold mb-1">
          {item.name}
        </div>
        <div className="text-gray-400 text-xs">
          #{item.itemId} ({cashItem ? '现金道具' : '装备道具'})
        </div>
        {bossReward && (
          <div className="text-red-400 text-xs mt-1">★ BOSS奖励装备</div>
        )}
      </div>

      {/* 需求信息区域 */}
      <div className="bg-blue-900/20 border border-blue-400/30 rounded p-2 mb-3">
        <div className="text-blue-300 font-bold mb-1">装备要求</div>
        <div className="text-white">REQ LEV: {level}</div>
        {(statRequirements.str > 0 || statRequirements.dex > 0 ||
          statRequirements.int > 0 || statRequirements.luk > 0) && (
          <div className="grid grid-cols-2 gap-1 mt-1 text-xs">
            {statRequirements.str > 0 && <div className="text-red-300">STR: {statRequirements.str}</div>}
            {statRequirements.dex > 0 && <div className="text-green-300">DEX: {statRequirements.dex}</div>}
            {statRequirements.int > 0 && <div className="text-blue-300">INT: {statRequirements.int}</div>}
            {statRequirements.luk > 0 && <div className="text-yellow-300">LUK: {statRequirements.luk}</div>}
          </div>
        )}
        <div className="text-gray-300 text-xs mt-1">职业: {jobRequirement}</div>
      </div>

      {/* 装备类型 */}
      <div className="mb-3">
        <div className="text-gray-300">Equipment Type: {typeName}</div>
      </div>

      {/* 装备属性 */}
      {stats && (
        <div className="bg-green-900/20 border border-green-400/30 rounded p-2 mb-3">
          <div className="text-green-300 font-bold mb-1">装备属性</div>
          <div className="grid grid-cols-2 gap-1">
            {stats.str > 0 && <div className="text-red-300">STR: +{stats.str}</div>}
            {stats.dex > 0 && <div className="text-green-300">DEX: +{stats.dex}</div>}
            {stats.int > 0 && <div className="text-blue-300">INT: +{stats.int}</div>}
            {stats.luk > 0 && <div className="text-yellow-300">LUK: +{stats.luk}</div>}
            {stats.maxHp > 0 && <div className="text-pink-300">HP: +{stats.maxHp}</div>}
            {stats.maxMp > 0 && <div className="text-cyan-300">MP: +{stats.maxMp}</div>}
            {stats.pad > 0 && <div className="text-orange-300">攻击力: +{stats.pad}</div>}
            {stats.mad > 0 && <div className="text-purple-300">魔力: +{stats.mad}</div>}
            {stats.pdd > 0 && <div className="text-gray-300">物防: +{stats.pdd}</div>}
            {stats.mdd > 0 && <div className="text-gray-300">魔防: +{stats.mdd}</div>}
            {stats.acc > 0 && <div className="text-white">命中: +{stats.acc}</div>}
            {stats.eva > 0 && <div className="text-white">回避: +{stats.eva}</div>}
            {stats.speed > 0 && <div className="text-white">移动: +{stats.speed}</div>}
            {stats.jump > 0 && <div className="text-white">跳跃: +{stats.jump}</div>}
          </div>
        </div>
      )}

      {/* 功能支持 */}
      <div className="bg-purple-900/20 border border-purple-400/30 rounded p-2 mb-3">
        <div className="text-purple-300 font-bold mb-1">强化支持</div>
        <div className="space-y-1">
          <div className={`flex items-center ${
            common?.enableStarforce ? 'text-green-400' : 'text-red-400'
          }`}>
            <span className="mr-2 w-3">
              {common?.enableStarforce ? '✓' : '✗'}
            </span>
            星力强化 {common?.maxStarforce ? `(最大${common.maxStarforce}星)` : ''}
          </div>

          <div className={`flex items-center ${
            !common?.blockUpgradePotential ? 'text-green-400' : 'text-red-400'
          }`}>
            <span className="mr-2 w-3">
              {!common?.blockUpgradePotential ? '✓' : '✗'}
            </span>
            潜能重设
          </div>

          <div className={`flex items-center ${
            !common?.blockUpgradeExtraOption ? 'text-green-400' : 'text-red-400'
          }`}>
            <span className="mr-2 w-3">
              {!common?.blockUpgradeExtraOption ? '✓' : '✗'}
            </span>
            额外属性
          </div>
        </div>
      </div>

      {/* 特殊标记 */}
      <div className="flex flex-wrap gap-1">
        {cashItem && (
          <span className="px-2 py-1 bg-purple-600/30 text-purple-300 rounded text-xs">
            💎 现金道具
          </span>
        )}
        {bossReward && (
          <span className="px-2 py-1 bg-red-600/30 text-red-300 rounded text-xs">
            👑 BOSS奖励
          </span>
        )}
        {onlyEquip && (
          <span className="px-2 py-1 bg-orange-600/30 text-orange-300 rounded text-xs">
            ⚡ 唯一装备
          </span>
        )}
        {common?.isMintable && (
          <span className="px-2 py-1 bg-green-600/30 text-green-300 rounded text-xs">
            🔨 可铸造
          </span>
        )}
      </div>
    </div>
  )
}
